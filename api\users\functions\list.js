import {
  success,
  failure
} from "../../../libs/response-lib";

import {
  is_level_permitted
} from "../../../libs/permissions";
import * as bugsnagClient from "../../../libs/bugsnag";

const AWS = require('aws-sdk')
const cognito_client = new AWS.CognitoIdentityServiceProvider()

export async function users(event, context, callback) {

  try {
    let access_scope = ['owner', 'manager', 'employee']
    //  const data = JSON.parse(event.body);
    let user_id = event.requestContext.identity.cognitoAuthenticationProvider.split('CognitoSignIn:')[1]

    const permissions = await is_level_permitted(user_id, access_scope);

    if (permissions.level_allowed) {

      let pool_id = process.env.COGNITO_POOL_ID
      let users = []

      let params = {
        UserPoolId: pool_id,
        Filter: "name = \"" + permissions.user_company_id + "\"",
        AttributesToGet: null,
        Limit: 0
      }

      let users_data = await cognito_client.listUsers(params).promise()

      for (var x in users_data.Users) {

        users.push(users_data.Users[x])
      }

      callback(null, success(users))

    } else {

      callback(null, failure({
        status: 'you do not have access to this api call'
      }));

    }

  } catch (e) {
    bugsnagClient.notify(user_id, e)
    callback(null, failure({
      status: e
    }));
  }

}