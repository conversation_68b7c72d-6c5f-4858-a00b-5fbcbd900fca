import uuid from "uuid";
import * as dynamoDbLib from "../../../libs/dynamodb-lib";
import * as bugsnagClient from "../../../libs/bugsnag";

import {
  success,
  failure
} from "../../../libs/response-lib";

import {
  is_level_permitted
} from "../../../libs/permissions";

export async function payment_method(event, context, callback) {

  try {
    const access_scope = ['owner', 'manager', 'employee']

    const data = JSON.parse(event.body);
    var user_id = event.requestContext.identity.cognitoAuthenticationProvider.split('CognitoSignIn:')[1]
    const permissions = await is_level_permitted(user_id, access_scope);

    if (permissions.level_allowed) {

      let shop_id = event.pathParameters.id

      let pkey = null
      let skey = process.env.STRIPE_PUBLISHABLE_KEY
      let stripe_integration_id = process.env.STRIPE_INTEGRATION_ID

      if (shop_id != 'kompis_app') {

        const shopInfo = await dynamoDbLib.call("scan", {
          TableName: process.env.objectsTableName,
          FilterExpression: "objectType = :objectType and objectId = :objectId",
          ExpressionAttributeValues: {
            ":objectType": 'shop',
            ":objectId": shop_id
          }
        });

        stripe_integration_id = shopInfo.Items[0].objectStripeIntegration

        const integrationParams = {
          TableName: process.env.integrationsTableName,
          FilterExpression: "integrationType = :integrationType and integrationId = :integrationId",
          ExpressionAttributeValues: {
            ":integrationType": 'stripe',
            ":integrationId": stripe_integration_id
          }
        }


        const stripeIntegrationResult = await dynamoDbLib.call("scan", integrationParams);
        let stripeIntegration = stripeIntegrationResult.Items[0]
        pkey = stripeIntegration.integrationPKey
        skey = stripeIntegration.integrationSKey

      }


      let company = await dynamoDbLib.call("scan", {
        TableName: process.env.objectsTableName,
        FilterExpression: "objectType = :objectType and objectId = :objectId",
        ExpressionAttributeValues: {
          ":objectType": 'company',
          ":objectId": permissions.user_company_id
        }
      })

      const stripe = require('stripe')(skey);

      const customer = await stripe.customers.create({
        payment_method: data.objectPaymentMethodId,
        name: company.Items[0].objectName,
      });

      const params = {
        TableName: process.env.objectsTableName,
        Item: {
          objectId: uuid.v1(),
          objectCreatedBy: user_id,
          objectCompanyId: permissions.user_company_id,
          objectName: data.objectName,
          objectPaymentMethodId: data.objectPaymentMethodId,
          objectPaymentMethodHolder: data.objectPaymentMethodHolder,
          objectStripeCustomerId: customer.id,
          objectIsDefault: false,
          objectIntegrationId: stripe_integration_id,
          objectType: "payment_method",
          createdAt: new Date().getTime()
        }
      };

      await dynamoDbLib.call("put", params);
      callback(null, success(params.Item));

    } else {

      callback(null, failure({
        status: 'you do not have access to this api call'
      }));

    }

  } catch (e) {
    bugsnagClient.notify(user_id, e)
    callback(null, failure({
      status: e
    }));
  }

}