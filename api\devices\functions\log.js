import uuid from "uuid";
import * as dynamoDbLib from "../../../libs/dynamodb-lib";
import * as bugsnagClient from "../../../libs/bugsnag";

import {
  success,
  failure
} from "../../../libs/response-lib";

import {
  is_level_permitted
} from "../../../libs/permissions";

export async function device(event, context, callback) {


  try {

    const access_scope = ['owner', 'manager', 'employee']

    const data = JSON.parse(event.body);
    var user_id = event.requestContext.identity.cognitoAuthenticationProvider.split('CognitoSignIn:')[1]
    var devices = data.devices


    const permissions = await is_level_permitted(user_id, access_scope);
    if (permissions.level_allowed) {

      for (var x in devices) {

        let device_id = devices[x].device_id
        let logged_value = devices[x].logged_value

        const sensorParam = {
          TableName: process.env.objectsTableName,
          FilterExpression: "objectType = :objectType and objectParent = :objectParent",
          ExpressionAttributeValues: {
            ":objectType": 'sensor',
            ":objectParent": device_id
          }
        };

        let sensorsResult = await dynamoDbLib.call("scan", sensorParam);
        let sensor = sensorsResult.Items[0];
        let sensor_id = sensor.objectId

        const params = {

          TableName: process.env.sensorsDataLogsTableName,
          Item: {
            sensorId: sensor_id,
            sensorData: {
              state: {
                desired: null,
                reported: {
                  temp: parseFloat(logged_value)
                }
              }
            },
            createdAt: new Date().getTime()
          }
        };

        await dynamoDbLib.call("put", params);
      }

    } else {

      callback(null, failure({
        status: 'you do not have access to this api call'
      }));

    }
  } catch (e) {
    bugsnagClient.notify(user_id, e)
    callback(null, failure({
      status: false
    }));
  }

  callback(null, success({
    status: true
  }));


}