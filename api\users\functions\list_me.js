import {
  success,
  failure
} from "../../../libs/response-lib";

import {
  is_level_permitted
} from "../../../libs/permissions";
import * as bugsnagClient from "../../../libs/bugsnag";


const AWS = require('aws-sdk')
const cognito_client = new AWS.CognitoIdentityServiceProvider()

export async function user(event, context, callback) {


  try {

    let access_scope = ['owner', 'manager', 'employee']

    //  const data = JSON.parse(event.body);
    let user_id = event.requestContext.identity.cognitoAuthenticationProvider.split('CognitoSignIn:')[1]


    const permissions = await is_level_permitted(user_id, access_scope);
    if (permissions.level_allowed) {

      let pool_id = process.env.COGNITO_POOL_ID

      let params = {
        UserPoolId: pool_id,
        Filter: "username = \"" + user_id + "\""
      };

      let users = await cognito_client.listUsers(params).promise()

      let me = users.Users[0]

      let first_name = ''
      let last_name = ''
      let phone = ''
      let language = ''
      let pin_code = ''
      let email = ''


      for (var x in me.Attributes) {

        if (me.Attributes[x].Name == 'custom:first_name') {
          first_name = me.Attributes[x].Value
        }

        if (me.Attributes[x].Name == 'custom:last_name') {
          last_name = me.Attributes[x].Value
        }

        if (me.Attributes[x].Name == 'custom:user_phone') {
          phone = me.Attributes[x].Value
        }

        if (me.Attributes[x].Name == 'custom:language') {
          language = me.Attributes[x].Value
        }

        if (me.Attributes[x].Name == 'custom:pin_code') {
          pin_code = me.Attributes[x].Value
        }

        if (me.Attributes[x].Name == 'email') {
          email = me.Attributes[x].Value
        }

      }

      callback(null, success({
        username: user_id,
        first_name,
        last_name,
        phone,
        language,
        pin_code,
        email
      }))

    } else {

      callback(null, failure({
        status: 'you do not have access to this api call'
      }));

    }

  } catch (e) {
    bugsnagClient.notify(user_id, e)
    callback(null, failure({
      status: e
    }));
  }

}