import uuid from "uuid";
import * as dynamoDbLib from "../../../libs/dynamodb-lib";
import * as bugsnagClient from "../../../libs/bugsnag";

import {
  success,
  failure
} from "../../../libs/response-lib";

export async function payment_method(event, context, callback) {

  try {

    const data = JSON.parse(event.body);

    let session = null
    let shop_id = 'kompis_app'

    let pkey = null
    let skey = process.env.STRIPE_PUBLISHABLE_KEY

    const stripe = require('stripe')(skey);

    const intent = await stripe.setupIntents.create({})

    callback(null, success(intent));


  } catch (e) {
    bugsnagClient.notify(user_id, e)
    callback(null, failure({
      status: e
    }));
  }

}