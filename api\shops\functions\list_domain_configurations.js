import * as dynamoDbLib from "../../../libs/dynamodb-lib";
import * as bugsnagClient from "../../../libs/bugsnag";
import {
  success,
  failure
} from "../../../libs/response-lib";

import {
  is_level_permitted
} from "../../../libs/permissions";

var arraySort = require('array-sort')

const AWS = require('aws-sdk')

const acm = new AWS.ACM({
  region: "us-east-1",
  accessKeyId: '********************',
  secretAccessKey: '6bB750d1OHP1Efz2f1AXrXO8kPZhAOpKf22xsyVk'
});

const cloudfront = new AWS.CloudFront({
  apiVersion: '2018-11-05',
  accessKeyId: '********************',
  secretAccessKey: '6bB750d1OHP1Efz2f1AXrXO8kPZhAOpKf22xsyVk',
});

export async function shop(event, context, callback) {


  try {

    const access_scope = ['owner', 'manager', 'employee']

    var user_id = event.requestContext.identity.cognitoAuthenticationProvider.split('CognitoSignIn:')[1]
    var shop_id = event.pathParameters.id

    const permissions = await is_level_permitted(user_id, access_scope);

    if (permissions.level_allowed) {

      const shops_result = await dynamoDbLib.call("scan", {
        TableName: process.env.objectsTableName,
        FilterExpression: "objectType = :objectType and objectCompanyId = :objectCompanyId and objectId = :objectId",
        ExpressionAttributeValues: {
          ":objectType": 'shop',
          ":objectCompanyId": permissions.user_company_id,
          ":objectId": shop_id
        }
      });

      let shop = shops_result.Items[0]
      let shop_domain = (shop.objectDomain) ? shop.objectDomain : null
      let domain_id = (shop.domainId) ? shop.domainId : null
      let shop_status = (shop.domainStatus) ? shop.domainStatus : null
      let sslArn = null
      let cloudFrontId = null
      let sll_status = null
      let certificate = null
      let ssl_validation = null
      let cf = null
      let cfdomain = null

      if (domain_id) {

        const domain_result = await dynamoDbLib.call("scan", {
          TableName: process.env.domainsTableName,
          FilterExpression: "domainId = :domainId",
          ExpressionAttributeValues: {
            ":domainId": domain_id,
          }
        });

        let domain = domain_result.Items[0]

        sslArn = (domain.sslArn) ? domain.sslArn : null
        cloudFrontId = (domain.cloudFrontId) ? domain.cloudFrontId : null
        sll_status = (domain.domainStatus) ? domain.domainStatus : null


        if (sslArn) {
          certificate = await acm.describeCertificate({
            CertificateArn: sslArn
          }).promise()
          ssl_validation = certificate.Certificate.DomainValidationOptions[0]
        }



        if (cloudFrontId) {

          cf = await cloudfront.getDistribution({
            Id: cloudFrontId
          }).promise()

          cfdomain = cf.Distribution.DomainName

        }

      }

      callback(null, success({
        ssl_validation: ssl_validation,
        shop_domain: shop_domain,
        cfdomain: cfdomain,
        shop_status: shop_status,
        sll_status: sll_status
      }));

    } else {

      callback(null, failure({
        status: 'you do not have access to this api call'
      }));

    }

  } catch (e) {
    bugsnagClient.notify(user_id, e)
    callback(null, failure({
      status: e
    }));
  }
}