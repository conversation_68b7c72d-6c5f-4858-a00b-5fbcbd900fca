service: kompis-payments

plugins:
  - serverless-webpack

custom:
  webpackIncludeModules: true
  stage: ${opt:stage, self:provider.stage}
  stageSufix:
    prod: ""
    dev: -dev
  congnito_pool_id:
    dev: eu-central-1_o36r1nMGj
    prod: eu-central-1_E1nLfop0C
  stripe_publishable_key:
    dev: sk_test_WGGioCRxEnyu3wXcaPQ0KcFS00qnNm4u3t
    prod: ******************************************
  stripe_integration_id:
    dev: 18072560-3f5-11e9-9b43-6db913a7fae0
    prod: 18072560-3f5-11e9-9b43-6db913a7fae0

provider:
  versionFunctions: false
  name: aws
  endpointType: REGIONAL
  runtime: nodejs16.x
  stage: dev
  region: eu-central-1
  environment:
    COGNITO_POOL_ID: ${self:custom.congnito_pool_id.${self:provider.stage}}
    STRIPE_PUBLISHABLE_KEY: ${self:custom.stripe_publishable_key.${self:provider.stage}}
    STRIPE_INTEGRATION_ID: ${self:custom.stripe_integration_id.${self:provider.stage}}
    generateCompaniesTasksSNSTopic: arn:aws:sns:eu-central-1:589634798762:generate_companies_tasks${self:custom.stageSufix.${self:custom.stage}}
    cartTableName: cart${self:custom.stageSufix.${self:custom.stage}}
    generateBillsSNSTopic: arn:aws:sns:eu-central-1:589634798762:generate_bills${self:custom.stageSufix.${self:custom.stage}}
    exceptionsTableName: exceptions${self:custom.stageSufix.${self:custom.stage}}
    objectsTableName: objects${self:custom.stageSufix.${self:custom.stage}}
    sensorsDataLogsTableName: sensorsDataLogs${self:custom.stageSufix.${self:custom.stage}}
    tasksTableName: tasks${self:custom.stageSufix.${self:custom.stage}}
    tasksLogTableName: tasksLog${self:custom.stageSufix.${self:custom.stage}}
    tasksSchedulesTableName: tasksSchedules${self:custom.stageSufix.${self:custom.stage}}
    userPermissionsTableName: userPermissions${self:custom.stageSufix.${self:custom.stage}}
    logsTableName: logs${self:custom.stageSufix.${self:custom.stage}}
    integrationsTableName: integrations${self:custom.stageSufix.${self:custom.stage}}
    billsTableName: bills${self:custom.stageSufix.${self:custom.stage}}

  iamRoleStatements:
    - Effect: "Allow"
      Action:
        - "SNS:Publish"
      Resource: "arn:aws:sns:eu-central-1:589634798762:generate_companies_tasks${self:custom.stageSufix.${self:custom.stage}}"

    - Effect: "Allow"
      Action:
        - "cognito-idp:*"
      Resource: "arn:aws:cognito-idp:eu-central-1:*:userpool/*"

    - Effect: "Allow"
      Action:
        - "SNS:Publish"
      Resource: "arn:aws:sns:eu-central-1:589634798762:generate_bills${self:custom.stageSufix.${self:custom.stage}}"

    - Effect: Allow
      Action:
        - dynamodb:Query
        - dynamodb:Scan
        - dynamodb:GetItem
        - dynamodb:PutItem
        - dynamodb:UpdateItem
        - dynamodb:DeleteItem
      Resource: "arn:aws:dynamodb:eu-central-1:*:*"

functions:
  createPaymentMethod:
    handler: functions/create.payment_method
    events:
      - http:
          path: /{id}
          method: post
          cors: true
          authorizer: aws_iam

  setupStripePaymentMethod:
    handler: functions/setup.payment_method
    events:
      - http:
          path: /stripe/setup/{id}
          method: post
          cors: true
          authorizer: aws_iam

  setupStripePaymentMethodGuest:
    handler: functions/setup_guest.payment_method
    events:
      - http:
          path: /stripe/setup/guest
          method: post
          cors: true

  deletePaymentMethod:
    handler: functions/delete.payment_method
    events:
      - http:
          path: /{id}
          method: delete
          cors: true
          authorizer: aws_iam

  setDefualtPaymentMethod:
    handler: functions/set_defualt.payment_method
    events:
      - http:
          path: /defualt/{id}
          method: put
          cors: true
          authorizer: aws_iam

  listPaymentMethods:
    handler: functions/list.payment_methods
    events:
      - http:
          path: /{id}
          method: get
          cors: true
          authorizer: aws_iam

  listPaymentMethodsByCustomer:
    handler: functions/list_customer_payments.payment_methods
    events:
      - http:
          path: /customer/{id}/{company_id}
          method: get
          cors: true
          authorizer: aws_iam

  listBills:
    handler: functions/list_bills.bills
    events:
      - http:
          path: /bills/{id}
          method: get
          cors: true
          authorizer: aws_iam
