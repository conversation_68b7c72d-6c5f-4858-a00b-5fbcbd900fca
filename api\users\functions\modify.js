import {
  success,
  failure
} from "../../../libs/response-lib";
import * as bugsnagClient from "../../../libs/bugsnag";
const AWS = require('aws-sdk')
const cognito_client = new AWS.CognitoIdentityServiceProvider()

export async function users(event, context, callback) {

  try {

    let pool_id = process.env.COGNITO_POOL_ID

    let users = []

    let user_pool = await cognito_client.describeUserPool({
      UserPoolId: pool_id
    }).promise()

    let number_of_users = user_pool.UserPool.EstimatedNumberOfUsers

    let loops = Math.ceil(number_of_users / 60)

    let token = ''

    for (var i = 1; i <= loops; i++) {

      let params = {};

      if (token != '') {
        params = {
          UserPoolId: pool_id,
          AttributesToGet: null,
          Limit: 0,
          PaginationToken: token
        }
      } else {
        params = {
          UserPoolId: pool_id,
          AttributesToGet: null,
          Limit: 0
        }
      }

      let users_data = await cognito_client.listUsers(params).promise()

      token = users_data.PaginationToken

      for (var x in users_data.Users) {

        for (var y in users_data.Users[x].Attributes) {

          if (users_data.Users[x].Attributes[y].Name == 'custom:company_id') {
            let c_id = users_data.Users[x].Attributes[y].Value

            await cognito_client.adminUpdateUserAttributes({
              UserAttributes: [{
                Name: 'name',
                Value: c_id
              }],
              UserPoolId: pool_id,
              Username: users_data.Users[x].Username
            }).promise();

            users.push(users_data.Users[x])
          }
        }
      }

    }

    callback(null, success(users))



  } catch (e) {
    bugsnagClient.notify(user_id, e)
    callback(null, failure({
      status: e
    }));
  }

}