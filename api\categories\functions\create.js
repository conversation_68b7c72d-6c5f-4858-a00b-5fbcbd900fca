import uuid from "uuid";
import * as dynamoDbLib from "../../../libs/dynamodb-lib";
import * as bugsnagClient from "../../../libs/bugsnag";

import {
  success,
  failure
} from "../../../libs/response-lib";

import {
  is_level_permitted
} from "../../../libs/permissions";
const AWS = require('aws-sdk')
const SNS = new AWS.SNS();
import {
  log_event
} from "../../../libs/logs";

export async function category(event, context, callback) {

  try {
    const access_scope = ['owner', 'manager', 'employee']

    const data = JSON.parse(event.body);
    var user_id = event.requestContext.identity.cognitoAuthenticationProvider.split('CognitoSignIn:')[1]

    const permissions = await is_level_permitted(user_id, access_scope);
    if (permissions.level_allowed) {

      let cat_id = uuid.v1()
      const params = {
        TableName: process.env.objectsTableName,
        Item: {
          objectCreatedBy: user_id,
          objectCompanyId: permissions.user_company_id,
          objectId: cat_id,
          objectName: data.objectName,
          objectVisibility: true,
          objectShopId: data.objectShopId,
          objectType: "category",
          createdAt: new Date().getTime()
        }
      };

      let category = await dynamoDbLib.call("put", params);

      await log_event(
        user_id,
        permissions.user_company_id,
        "category_created", {
          cartId: cat_id,
          name: data.objectName
        },
        data.objectShopId
      )

      await SNS.publish({
        Message: cat_id,
        TopicArn: process.env.generatePowerOfficeCategoriesSNSTopic
      }, function(err, data) {

        if (err) {
          callback(null, err);
        }

      });


      callback(null, success(params.Item));

    } else {

      callback(null, failure({
        status: 'you do not have access to this api call'
      }));

    }

  } catch (e) {
    bugsnagClient.notify(user_id, e)
    callback(null, failure({
      status: e
    }));
  }

}