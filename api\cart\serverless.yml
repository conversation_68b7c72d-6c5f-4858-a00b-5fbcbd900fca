service: kompis-cart

plugins:
  - serverless-webpack

custom:
  webpackIncludeModules: true
  stage: ${opt:stage, self:provider.stage}
  stageSufix:
    prod: ""
    dev: -dev
  congnito_pool_id:
    dev: eu-central-1_o36r1nMGj
    prod: eu-central-1_E1nLfop0C

  poweroffice_auth_url:
    dev: https://api-demo.poweroffice.net/OAuth/Token
    prod: https://api.poweroffice.net/OAuth/Token
  poweroffice_api_url:
    dev: https://api-demo.poweroffice.net
    prod: https://api.poweroffice.net

  poweroffice_app_key:
    dev: 0fabc9dc-5477-4459-bd0c-7bc19a02caf3
    prod: 4ace3064-f987-48f1-9111-c96c896f36c6

  kompis_url:
    dev: https://development.kompis.app
    prod: https://kompis.app

provider:
  versionFunctions: false
  name: aws
  endpointType: REGIONAL
  runtime: nodejs16.x
  stage: dev
  region: eu-central-1
  environment:
    COGNITO_POOL_ID: ${self:custom.congnito_pool_id.${self:provider.stage}}
    KOMPIS_APP_URL: ${self:custom.kompis_url.${self:provider.stage}}
    POWEROFFICE_AUTH_URL: ${self:custom.poweroffice_auth_url.${self:provider.stage}}
    POWEROFFICE_API_URL: ${self:custom.poweroffice_api_url.${self:provider.stage}}
    POWEROFFICE_APP_KEY: ${self:custom.poweroffice_app_key.${self:provider.stage}}
    generateCompaniesTasksSNSTopic: arn:aws:sns:eu-central-1:589634798762:generate_companies_tasks${self:custom.stageSufix.${self:custom.stage}}
    generateRecurringOrdersSNSTopic: arn:aws:sns:eu-central-1:589634798762:generate_recurring_orders${self:custom.stageSufix.${self:custom.stage}}
    sendOrderConfirmationSecondaryCustomerSNSTopic: arn:aws:sns:eu-central-1:589634798762:send_confirmation_email_secondary${self:custom.stageSufix.${self:custom.stage}}
    sendOrderConfirmationSecondaryPrimarySNSTopic: arn:aws:sns:eu-central-1:589634798762:send_confirmation_email_primary${self:custom.stageSufix.${self:custom.stage}}
    generatePowerOfficeOrdersSNSTopic: arn:aws:sns:eu-central-1:589634798762:generate_poweroffice_orders${self:custom.stageSufix.${self:custom.stage}}
    cartTableName: cart${self:custom.stageSufix.${self:custom.stage}}
    exceptionsTableName: exceptions${self:custom.stageSufix.${self:custom.stage}}
    objectsTableName: objects${self:custom.stageSufix.${self:custom.stage}}
    sensorsDataLogsTableName: sensorsDataLogs${self:custom.stageSufix.${self:custom.stage}}
    tasksTableName: tasks${self:custom.stageSufix.${self:custom.stage}}
    tasksLogTableName: tasksLog${self:custom.stageSufix.${self:custom.stage}}
    tasksSchedulesTableName: tasksSchedules${self:custom.stageSufix.${self:custom.stage}}
    userPermissionsTableName: userPermissions${self:custom.stageSufix.${self:custom.stage}}
    logsTableName: logs${self:custom.stageSufix.${self:custom.stage}}
    integrationsTableName: integrations${self:custom.stageSufix.${self:custom.stage}}
    transactionsTableName: transactions${self:custom.stageSufix.${self:custom.stage}}
    eventsLogTableName: eventsLog${self:custom.stageSufix.${self:custom.stage}}

  iamRoleStatements:
    - Effect: "Allow"
      Action:
        - "SNS:Publish"
      Resource: "arn:aws:sns:eu-central-1:589634798762:generate_companies_tasks${self:custom.stageSufix.${self:custom.stage}}"

    - Effect: "Allow"
      Action:
        - "SNS:Publish"
      Resource: "arn:aws:sns:eu-central-1:589634798762:generate_recurring_orders${self:custom.stageSufix.${self:custom.stage}}"

    - Effect: "Allow"
      Action:
        - "SNS:Publish"
      Resource: "arn:aws:sns:eu-central-1:589634798762:send_confirmation_email_secondary${self:custom.stageSufix.${self:custom.stage}}"

    - Effect: "Allow"
      Action:
        - "SNS:Publish"
      Resource: "arn:aws:sns:eu-central-1:589634798762:send_confirmation_email_primary${self:custom.stageSufix.${self:custom.stage}}"

    - Effect: "Allow"
      Action:
        - "cognito-idp:*"
      Resource: "arn:aws:cognito-idp:eu-central-1:*:userpool/*"

    - Effect: "Allow"
      Action:
        - "SNS:Publish"
      Resource: "arn:aws:sns:eu-central-1:589634798762:generate_poweroffice_orders${self:custom.stageSufix.${self:custom.stage}}"

    - Effect: Allow
      Action:
        - dynamodb:Query
        - dynamodb:Scan
        - dynamodb:GetItem
        - dynamodb:PutItem
        - dynamodb:UpdateItem
        - dynamodb:DeleteItem
      Resource: "arn:aws:dynamodb:eu-central-1:*:*"

functions:
  listShopInfo:
    handler: functions/list_shop_info.info
    events:
      - http:
          path: /info/{id}
          method: get
          cors: true
          authorizer: aws_iam

  listShopVideos:
    handler: functions/list_shop_videos.videos
    events:
      - http:
          path: /videos/{id}
          method: get
          cors: true
          authorizer: aws_iam

  checkCustomerRequrringOrders:
    handler: functions/check-requrring.check
    events:
      - http:
          path: /requrring/{id}
          method: post
          cors: true
          authorizer: aws_iam

  createUserByInvite:
    handler: functions/create-user-by-invite.user
    events:
      - http:
          path: /users
          method: post
          cors: true
          authorizer: aws_iam

  listShopProducts:
    handler: functions/list_shop_products.products
    events:
      - http:
          path: /products/{id}
          method: get
          cors: true
          authorizer: aws_iam

  listProductsForecast:
    handler: functions/forecast.products
    timeout: 300
    memorySize: 2048
    events:
      - http:
          path: /forecast/{id}
          method: post
          cors: true
          authorizer: aws_iam

  listProductsForecastCustomer:
    handler: functions/forecast_customer.products
    events:
      - http:
          path: /customer/forecast/{id}
          method: post
          cors: true
          authorizer: aws_iam

  listProductsForecastVolume:
    handler: functions/forecast_customer_volume.products
    timeout: 300
    memorySize: 2048
    events:
      - http:
          path: /volume/forecast/{id}
          method: post
          cors: true
          authorizer: aws_iam

  listShopCategories:
    handler: functions/list_shop_categories.categories
    events:
      - http:
          path: /categories/{id}
          method: get
          cors: true
          authorizer: aws_iam

  listShopDeliveryPoints:
    handler: functions/list_shop_delivery_points.delivery_points
    events:
      - http:
          path: /delivery_points/{id}/{company_id}
          method: get
          cors: true
          authorizer: aws_iam

  listShopCustomerEmployees:
    handler: functions/list_employees_by_company_id.users
    events:
      - http:
          path: /employees/{id}/{company_id}
          method: get
          cors: true
          authorizer: aws_iam

  checkAccess:
    handler: functions/access.access
    events:
      - http:
          path: /access/{id}
          method: get
          cors: true
          authorizer: aws_iam

  createCart:
    handler: functions/create.cart
    events:
      - http:
          path: /{id}
          method: post
          cors: true
          authorizer: aws_iam

  createVolumeCart:
    handler: functions/create_volume_order.cart
    events:
      - http:
          path: /volume/{id}
          method: post
          cors: true
          authorizer: aws_iam

  createServiceMachineCart:
    handler: functions/create_service_order.cart
    events:
      - http:
          path: /service/{id}
          method: post
          cors: true
          authorizer: aws_iam

  listCart:
    handler: functions/list.cart
    events:
      - http:
          path: /{id}
          method: get
          cors: true
          authorizer: aws_iam

  listOrder:
    handler: functions/list_order.cart
    events:
      - http:
          path: /order/{id}
          method: get
          cors: true
          authorizer: aws_iam

  shipOrder:
    handler: functions/ship.order
    events:
      - http:
          path: /orders/ship/{id}
          method: put
          cors: true
          authorizer: aws_iam

  updateOrderStatus:
    handler: functions/update_status.order
    timeout: 300
    events:
      - http:
          path: /orders/status/{id}
          method: post
          cors: true
          authorizer: aws_iam

  runPogoSync:
    handler: functions/run_pogo_sync.order
    timeout: 300
    events:
      - http:
          path: /pogo/sync/{id}
          method: post
          cors: true
          authorizer: aws_iam

  updatePogoOrderSendStatus:
    handler: functions/update_pogo_send_status.order
    timeout: 300
    events:
      - http:
          path: /orders/pogo/status/{id}
          method: post
          cors: true
          authorizer: aws_iam

  listShopOrdersOwner:
    handler: functions/list_shop_orders_owner.orders
    timeout: 300
    memorySize: 2048
    events:
      - http:
          path: /owner/orders/{id}
          method: post
          cors: true
          authorizer: aws_iam

  listShopOrdersOwnerByOrderId:
    handler: functions/list_shop_orders_owner_by_order_id.orders
    events:
      - http:
          path: /owner/order/{id}
          method: post
          cors: true
          authorizer: aws_iam

  listShopOrdersOwnerByDate:
    handler: functions/list_shop_orders_owner_print.orders
    timeout: 300
    memorySize: 2048
    events:
      - http:
          path: /owner/orders/print/{id}
          method: post
          cors: true
          authorizer: aws_iam

  listShopOrdersCustomer:
    handler: functions/list_shop_orders_customer.orders
    events:
      - http:
          path: /customer/orders
          method: get
          cors: true
          authorizer: aws_iam

  listShopOrdersCustomerById:
    handler: functions/list_shop_orders_customer_by_id.orders
    timeout: 300
    memorySize: 2048
    events:
      - http:
          path: /customer/orders/{id}
          method: post
          cors: true
          authorizer: aws_iam

  sendOrderConfirmationSecondaryCustomer:
    handler: functions/order_confirmation_secondary.send
    events:
      - sns: send_confirmation_email_secondary${self:custom.stageSufix.${self:custom.stage}}

  sendOrderConfirmationSecondaryPrimary:
    handler: functions/order_confirmation_primary.send
    events:
      - sns: send_confirmation_email_primary${self:custom.stageSufix.${self:custom.stage}}
