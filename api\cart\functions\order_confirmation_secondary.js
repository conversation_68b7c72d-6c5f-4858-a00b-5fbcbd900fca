import {
  success,
  failure
} from "../../../libs/response-lib";

import * as bugsnagClient from "../../../libs/bugsnag";
import * as dynamoDbLib from "../../../libs/dynamodb-lib";

import nodemailer from "nodemailer"

const AWS = require('aws-sdk')


export async function send(event, context, callback) {
  try {

    let cart_id = event.Records[0].Sns.Message
    //let cart_id = 'c820c490-e910-11e9-9c41-ebd12a4f22e5'
    let cognito_client = new AWS.CognitoIdentityServiceProvider()
    let pool_id = process.env.COGNITO_POOL_ID



    const params = {
      TableName: process.env.cartTableName,
      FilterExpression: "cartId = :cartId",
      ExpressionAttributeValues: {
        ":cartId": cart_id
      }
    };

    const order = await dynamoDbLib.call("scan", params);

    let shop_id = order.Items[0].shopId


    const shopInfo = await dynamoDbLib.call("scan", {
      TableName: process.env.objectsTableName,
      FilterExpression: "objectType = :objectType and objectId = :objectId",
      ExpressionAttributeValues: {
        ":objectType": 'shop',
        ":objectId": shop_id
      }
    });

    let shop_name = shopInfo.Items[0].objectName


    let user = await cognito_client.adminGetUser({
      UserPoolId: pool_id,
      Username: (order.Items[0].on_behalf_of) ? order.Items[0].on_behalf_of : order.Items[0].userId
    }).promise()

    let email = ''
    let user_type = ''

    for (var x in user.UserAttributes) {

      if (user.UserAttributes[x].Name == 'email') {
        email = user.UserAttributes[x].Value
      }

      if (user.UserAttributes[x].Name == 'custom:user_type') {
        user_type = user.UserAttributes[x].Value
      }

    }

    let link = 'https://' + shop_id + '.shops.kompis.app'

    let transporter = nodemailer.createTransport({
      host: 'email-smtp.eu-west-1.amazonaws.com',
      port: 465,
      secure: true,
      auth: {
        user: 'AKIAJLILB3DMXPRVD4AA',
        pass: 'Amg0o1jLDdfBLT++mU1G0xw99F+cCjVKFg4DYMlQOppz'
      }
    });

    let from_email = '<EMAIL>'

    let mailOptions = {
      from: from_email,
      to: email,
      subject: 'Din ordrebekreftelse',
      html: shop_name + ' har mottatt din ordre! <br /><br />Ønsker du å se din ordre, <a href="' + link + '">trykk her</a> og logg inn på din profil for ordrehistorikk eller for å ta kontakt med oss. <br /><br />Takk for handelen :)<br /><br />Mhv<br />' + shop_name
    };

    await transporter.sendMail(mailOptions).then(function(info) {
      callback(null, success(cart_id));
    }).catch(function(err) {
      callback(null, failure({
        status: err
      }));
    });

    callback(null, 'done');

  } catch (e) {
    //  bugsnagClient.notify(user_id, e)
    console.log(e);
    callback(null, failure({
      error: e
    }))
  }

}