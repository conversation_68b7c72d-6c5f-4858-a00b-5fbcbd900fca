service: kompis-users

plugins:
  - serverless-webpack

custom:
  webpackIncludeModules: true
  stage: ${opt:stage, self:provider.stage}
  stageSufix:
    prod: ""
    dev: -dev
  congnito_pool_id:
    dev: eu-central-1_o36r1nMGj
    prod: eu-central-1_E1nLfop0C
  kompis_url:
    dev: https://development.kompis.app
    prod: https://kompis.app
  stripe_publishable_key:
    dev: sk_test_WGGioCRxEnyu3wXcaPQ0KcFS00qnNm4u3t
    prod: ******************************************
  stripe_integration_id:
    dev: 18072560-3f5-11e9-9b43-6db913a7fae0
    prod: 18072560-3f5-11e9-9b43-6db913a7fae0

provider:
  versionFunctions: false
  name: aws
  endpointType: REGIONAL
  runtime: nodejs16.x
  stage: dev
  region: eu-central-1
  environment:
    COGNITO_POOL_ID: ${self:custom.congnito_pool_id.${self:provider.stage}}
    KOMPIS_APP_URL: ${self:custom.kompis_url.${self:provider.stage}}
    STRIPE_PUBLISHABLE_KEY: ${self:custom.stripe_publishable_key.${self:provider.stage}}
    STRIPE_INTEGRATION_ID: ${self:custom.stripe_integration_id.${self:provider.stage}}
    generateCompaniesTasksSNSTopic: arn:aws:sns:eu-central-1:589634798762:generate_companies_tasks${self:custom.stageSufix.${self:custom.stage}}
    cartTableName: cart${self:custom.stageSufix.${self:custom.stage}}
    exceptionsTableName: exceptions${self:custom.stageSufix.${self:custom.stage}}
    objectsTableName: objects${self:custom.stageSufix.${self:custom.stage}}
    sensorsDataLogsTableName: sensorsDataLogs${self:custom.stageSufix.${self:custom.stage}}
    tasksTableName: tasks${self:custom.stageSufix.${self:custom.stage}}
    tasksLogTableName: tasksLog${self:custom.stageSufix.${self:custom.stage}}
    tasksSchedulesTableName: tasksSchedules${self:custom.stageSufix.${self:custom.stage}}
    userPermissionsTableName: userPermissions${self:custom.stageSufix.${self:custom.stage}}
    logsTableName: logs${self:custom.stageSufix.${self:custom.stage}}
    challengesTableName: challenges${self:custom.stageSufix.${self:custom.stage}}

  iamRoleStatements:
    - Effect: "Allow"
      Action:
        - "SNS:Publish"
      Resource: "arn:aws:sns:eu-central-1:589634798762:generate_companies_tasks${self:custom.stageSufix.${self:custom.stage}}"

    - Effect: "Allow"
      Action:
        - "cognito-idp:*"
      Resource: "arn:aws:cognito-idp:eu-central-1:*:userpool/*"

    - Effect: Allow
      Action:
        - dynamodb:Query
        - dynamodb:Scan
        - dynamodb:GetItem
        - dynamodb:PutItem
        - dynamodb:UpdateItem
        - dynamodb:DeleteItem
      Resource: "arn:aws:dynamodb:eu-central-1:*:*"

functions:
  createUser:
    handler: functions/create.user
    events:
      - http:
          path: /
          method: post
          cors: true
          authorizer: aws_iam

  reinviteUser:
    handler: functions/reinvite.user
    events:
      - http:
          path: /reinvite
          method: post
          cors: true
          authorizer: aws_iam

  deleteUser:
    handler: functions/delete.user
    events:
      - http:
          path: /{id}
          method: delete
          cors: true
          authorizer: aws_iam

  activeUser:
    handler: functions/active.user
    events:
      - http:
          path: /active/{id}
          method: put
          cors: true
          authorizer: aws_iam

  updateUser:
    handler: functions/update.user
    events:
      - http:
          path: /{id}
          method: put
          cors: true
          authorizer: aws_iam

  updateCompany:
    handler: functions/update_company.company
    events:
      - http:
          path: /company/
          method: put
          cors: true
          authorizer: aws_iam

  updateUserEmail:
    handler: functions/update_email.user

  listUsers:
    handler: functions/list.users
    events:
      - http:
          path: /
          method: get
          cors: true
          authorizer: aws_iam

  listCompany:
    handler: functions/list_company.company
    events:
      - http:
          path: /company/{id}
          method: get
          cors: true
          authorizer: aws_iam

  modifyUsers:
    handler: functions/modify.users

  listMe:
    handler: functions/list_me.user
    events:
      - http:
          path: /me/
          method: get
          cors: true
          authorizer: aws_iam

  concurrent:
    handler: functions/concurrent.users
    events:
      - http:
          path: /concurrent/
          method: get
          cors: true
          authorizer: aws_iam

  signupEmail:
    handler: functions/signup.send

  defineChallengeTrigger:
    handler: functions/defineChallenge.handler

  verifyChallengeTrigger:
    handler: functions/verifyChallenge.handler

  createChallengeTrigger:
    handler: functions/createChallenge.handler
