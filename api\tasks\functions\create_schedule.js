import uuid from "uuid";
import * as dynamoDbLib from "../../../libs/dynamodb-lib";
import * as bugsnagClient from "../../../libs/bugsnag";
import {
  success,
  failure
} from "../../../libs/response-lib";

import {
  is_level_permitted
} from "../../../libs/permissions";



export async function task(event, context, callback) {

  try {

    let access_scope = ['owner', 'manager', 'employee']
    const data = JSON.parse(event.body);
    var user_id = event.requestContext.identity.cognitoAuthenticationProvider.split('CognitoSignIn:')[1]

    const permissions = await is_level_permitted(user_id, access_scope);

    if (permissions.level_allowed) {

      let in_x_hours = (data.in_x_hours) ? data.in_x_hours : 0


      let task_time = new Date();
      task_time.setHours(task_time.getHours() + in_x_hours);

      await dynamoDbLib.call("put", {
        TableName: process.env.tasksSchedulesTableName,
        Item: {
          taskScheduleId: uuid.v1(),
          companyId: permissions.user_company_id,
          taskId: data.taskId,
          taskType: data.taskType,
          assignees: data.assignees,
          assignerId: user_id,
          taskStatus: 'open',
          locationId: data.locationId,
          taskName: data.taskName,
          tasksScheduleTime: task_time.getTime(),
          cartId: (data.cartId) ? data.cartId : null,
          productId: (data.productId) ? data.productId : null,
          quantity: (data.quantity) ? data.quantity : null,
          shopId: (data.shopId) ? data.shopId : null,
          TaskexceptionId: (data.TaskexceptionId) ? data.TaskexceptionId : null

        }
      });

      callback(null, success('success'));

    } else {

      callback(null, failure({
        status: 'you do not have access to this api call'
      }));

    }
  } catch (e) {
    bugsnagClient.notify(user_id, e)
    callback(null, failure({
      status: false
    }));
  }
}