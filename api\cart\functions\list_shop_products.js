import * as dynamoDbLib from "../../../libs/dynamodb-lib";
import * as bugsnagClient from "../../../libs/bugsnag";
import {
  success,
  failure
} from "../../../libs/response-lib";
var arraySort = require('array-sort')
export async function products(event, context, callback) {

  try {

    let shop_id = event.pathParameters.id

    const params = {
      TableName: process.env.objectsTableName,
      FilterExpression: "objectStatus <> :objectStatus and objectType = :objectType and objectShopId = :objectShopId and objectVisibility = :objectVisibility",
      ExpressionAttributeValues: {
        ":objectType": 'product',
        ":objectShopId": shop_id,
        ":objectVisibility": true,
        ":objectStatus": 'deleted'
      }
    };

    const result = await dynamoDbLib.call("scan", params);
    callback(null, success(arraySort(result.Items, 'objectOrder')));


  } catch (e) {
    bugsnagClient.notify('guest', e)
    callback(null, failure({
      status: e
    }));
  }
}