import * as dynamoDbLib from "../../../libs/dynamodb-lib";
import * as bugsnagClient from "../../../libs/bugsnag";
import {
  success,
  failure
} from "../../../libs/response-lib";
import {
  is_level_permitted
} from "../../../libs/permissions";

var arraySort = require('array-sort')

export async function delivery_points(event, context, callback) {

  try {

    const access_scope = ['owner', 'manager', 'employee']
    var user_id = event.requestContext.identity.cognitoAuthenticationProvider.split('CognitoSignIn:')[1]

    const permissions = await is_level_permitted(user_id, access_scope);
    if (permissions.level_allowed) {

      let shop_id = event.pathParameters.id
      let company_id = event.pathParameters.company_id

      let params1 = {
        TableName: process.env.userPermissionsTableName,
        FilterExpression: "userId = :userId",
        ExpressionAttributeValues: {
          ":userId": (company_id != 'null') ? company_id : permissions.user_company_id
        }
      };

      const result1 = await dynamoDbLib.call("scan", params1);

      let delivery_points = []

      for (var x in result1.Items) {
        if (result1.Items[x].locationId) {
          let params2 = {
            TableName: process.env.objectsTableName,
            FilterExpression: "objectType = :objectType and objectId = :objectId",
            ExpressionAttributeValues: {
              ":objectType": 'delivery_point',
              ":objectId": result1.Items[x].locationId,
            }
          };

          let result2 = await dynamoDbLib.call("scan", params2);

          if (result2.Items[0].objectShopId == shop_id) {
            delivery_points.push(result2.Items[0])
          }
        }

      }
      delivery_points = removeDuplicates(delivery_points)
      callback(null, success(arraySort(delivery_points, 'objectName')));

    } else {

      callback(null, failure({
        status: 'you do not have access to this api call'
      }));

    }

  } catch (e) {
    bugsnagClient.notify(user_id, e)
    callback(null, failure({
      status: e
    }));
  }
}

function removeDuplicates(arr) {

  const result = [];
  const duplicatesIndices = [];

  // Loop through each item in the original array
  arr.forEach((current, index) => {

    if (duplicatesIndices.includes(index)) return;

    result.push(current);

    // Loop through each other item on array after the current one
    for (let comparisonIndex = index + 1; comparisonIndex < arr.length; comparisonIndex++) {

      const comparison = arr[comparisonIndex];
      const currentKeys = Object.keys(current);
      const comparisonKeys = Object.keys(comparison);

      // Check number of keys in objects
      if (currentKeys.length !== comparisonKeys.length) continue;

      // Check key names
      const currentKeysString = currentKeys.sort().join("").toLowerCase();
      const comparisonKeysString = comparisonKeys.sort().join("").toLowerCase();
      if (currentKeysString !== comparisonKeysString) continue;

      // Check values
      let valuesEqual = true;
      for (let i = 0; i < currentKeys.length; i++) {
        const key = currentKeys[i];
        if (current[key] !== comparison[key]) {
          valuesEqual = false;
          break;
        }
      }
      if (valuesEqual) duplicatesIndices.push(comparisonIndex);

    } // end for loop

  }); // end arr.forEach()

  return result;
}