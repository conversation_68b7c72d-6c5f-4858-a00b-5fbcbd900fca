import uuid from "uuid";
import * as dynamoDbLib from "../../../../libs/dynamodb-lib";

import {
  success,
  failure
} from "../../../../libs/response-lib";

import {
  is_level_permitted
} from "../../../../libs/permissions";

export async function delivery(event, context, callback) {

  const access_scope = ['owner', 'manager', 'employee']

  const data = JSON.parse(event.body);
  var user_id = event.requestContext.identity.cognitoAuthenticationProvider.split('CognitoSignIn:')[1]

  try {
    const permissions = await is_level_permitted(user_id, access_scope);
    if (permissions.level_allowed) {

      const params = {
        TableName: process.env.logsTableName,
        Item: {
          logCreatedBy: user_id,
          logCompanyId: permissions.user_company_id,
          logId: uuid.v1(),
          senderName: data.senderName,
          typeOfGoods: data.typeOfGoods,
          ingredients: data.ingredients,
          firstReading: data.firstReading,
          logStatus: data.logStatus,
          logType: data.logType,
          createdAt: new Date().getTime()
        }
      };

      await dynamoDbLib.call("put", params);
      callback(null, success(params.Item));

    } else {

      callback(null, failure({
        status: 'you do not have access to this api call'
      }));

    }

  } catch (e) {
    callback(null, failure({
      status: e
    }));
  }

}