import { success, failure } from "../../../libs/response-lib";

import { is_level_permitted } from "../../../libs/permissions";
import * as bugsnagClient from "../../../libs/bugsnag";
import * as dynamoDbLib from "../../../libs/dynamodb-lib";

import PowerOffice from "../../../libs/poweroffice";

const AWS = require("aws-sdk");
const cognito_client = new AWS.CognitoIdentityServiceProvider();

export async function init(event, context, callback) {
  try {
    let shop_id = event.Records[0].Sns.Message;
    //let shop_id = "dfea4f90-c455-11e9-9ab0-7fc92b1e522d";

    const shopsSarams = {
      TableName: process.env.objectsTableName,
      FilterExpression: "objectType = :objectType and objectId = :objectId",
      ExpressionAttributeValues: {
        ":objectType": "shop",
        ":objectId": shop_id,
      },
    };

    const shops = await dynamoDbLib.call("scan", shopsSarams);
    let shop_currency = shops.Items[0].shopCurrency
      ? shops.Items[0].shopCurrency.toUpperCase()
      : "NOK";

    let orderDeadline = shops.Items[0].orderDeadline
      ? parseInt(shops.Items[0].orderDeadline)
      : 0;
    let orderDeadlineTime = shops.Items[0].orderDeadlineTime
      ? parseInt(shops.Items[0].orderDeadlineTime)
      : 0;

    let categoriesParams = {
      TableName: process.env.objectsTableName,
      FilterExpression:
        "objectType = :objectType and objectShopId = :objectShopId",
      ExpressionAttributeValues: {
        ":objectType": "category",
        ":objectShopId": shop_id,
      },
    };

    let shopDeliveryPoints = await dynamoDbLib.call("scan", {
      TableName: process.env.objectsTableName,
      FilterExpression:
        "objectType = :objectType and objectShopId = :objectShopId",
      ExpressionAttributeValues: {
        ":objectType": "delivery_point",
        ":objectShopId": shop_id,
      },
    });

    let categories = await dynamoDbLib.call("scan", categoriesParams);

    if (shops.Items[0].objectPowerOfficeIntegration) {
      let company_id = shops.Items[0].objectCompanyId;
      let migration_mode = shops.Items[0].objectPowerOfficeMigrationMode;

      let shipping_product_id =
        shops.Items[0].objectPowerOfficeShippingProductId;
      let shipping_product_code =
        shops.Items[0].objectPowerOfficeShippingProductCode;
      let stripe_poweroffice_customer_id = shops.Items[0]
        .objectStripePowerOfficeCustomerId
        ? shops.Items[0].objectStripePowerOfficeCustomerId
        : null;

      const integrationParams = {
        TableName: process.env.integrationsTableName,
        FilterExpression: "integrationId = :integrationId",
        ExpressionAttributeValues: {
          ":integrationId": shops.Items[0].objectPowerOfficeIntegration,
        },
      };

      const integrations = await dynamoDbLib.call("scan", integrationParams);
      let poweroffice_client_key = integrations.Items[0].integrationPOAppKey;
      let poweroffice_secrect_key =
        integrations.Items[0].integrationPOClientKey;
      let poweroffice_0_mva_account = integrations.Items[0].integrationPO0MVA;
      let poweroffice_15_mva_account = integrations.Items[0].integrationPO15MVA;
      let poweroffice_25_mva_account = integrations.Items[0].integrationPO25MVA;
      let poweroffice_bank_fees_account = integrations.Items[0]
        .integrationPOBankFeesAccount
        ? integrations.Items[0].integrationPOBankFeesAccount
        : null;
      let migration_start_date = integrations.Items[0].createdAt;

      let poweroffice = await new PowerOffice(
        process.env.POWEROFFICE_APP_KEY,
        poweroffice_secrect_key
      );

      if (shops.Items[0].objectStripeIntegration) {
        if (!stripe_poweroffice_customer_id) {
          let cus = await poweroffice.createCustomer({
            EmailAddress: "<EMAIL>",
            FirstName: "Stripe",
            LastName: "Kompis",
            InvoiceDeliveryType: 1,
            InvoiceEmailAddress: "<EMAIL>",
            IsPerson: false,
            IsArchived: false,
            LegalName: "Stripe",
            Name: "Stripe",
          });

          stripe_poweroffice_customer_id = cus.data.data.code;

          const result = await dynamoDbLib.call("update", {
            TableName: process.env.objectsTableName,

            Key: {
              objectCompanyId: shops.Items[0].objectCompanyId,
              objectId: shop_id,
            },

            UpdateExpression:
              "SET objectStripePowerOfficeCustomerId = :objectStripePowerOfficeCustomerId",
            ExpressionAttributeValues: {
              ":objectStripePowerOfficeCustomerId":
                stripe_poweroffice_customer_id,
            },
            ReturnValues: "ALL_NEW",
          });
        }
      }

      let dealdine_date = new Date();
      dealdine_date.setDate(dealdine_date.getDate() + orderDeadline);
      //dealdine_date.setHours(parseInt(orderDeadlineTime));
      dealdine_date.setHours(23);
      dealdine_date.setMinutes(0);
      dealdine_date.setSeconds(0);

      // send orders as invoices
      let ordersParams = {};

      if (migration_mode == "all") {
        ordersParams = {
          TableName: process.env.cartTableName,
          FilterExpression:
            "cartId <> :cartId and schedule_type = :schedule_type and orderStatus <> :orderStatus and (delivery_date < :delivery_date or poweroffice_manual_send = :poweroffice_manual_send) and shopId = :shopId and (attribute_not_exists(powerOfficeInvoiceId) or powerOfficeInvoiceId = :powerOfficeInvoiceId )",
          ExpressionAttributeValues: {
            ":orderStatus": "canceled",
            ":shopId": shop_id,
            //":paymentMethod": 'manual',
            //":PaymentMethodId": 'manual',
            ":schedule_type": "once",
            ":powerOfficeInvoiceId": null,
            ":delivery_date": dealdine_date.getTime(),
            ":poweroffice_manual_send": true,
            ":cartId": "9873bf80-1115-11eb-b5cb-af0b3b298b42",
          },
        };
      } else {
        ordersParams = {
          TableName: process.env.cartTableName,
          FilterExpression:
            "cartId <> :cartId and schedule_type = :schedule_type and orderStatus <> :orderStatus and (delivery_date < :delivery_date or poweroffice_manual_send = :poweroffice_manual_send ) and shopId = :shopId and createdAt > :createdAt and (attribute_not_exists(powerOfficeInvoiceId) or powerOfficeInvoiceId = :powerOfficeInvoiceId )",
          ExpressionAttributeValues: {
            ":orderStatus": "canceled",
            ":shopId": shop_id,
            ":createdAt": migration_start_date,
            //":paymentMethod": 'manual',
            //":PaymentMethodId": 'manual',
            ":schedule_type": "once",
            ":powerOfficeInvoiceId": null,
            ":delivery_date": dealdine_date.getTime(),
            ":poweroffice_manual_send": true,
            ":cartId": "9873bf80-1115-11eb-b5cb-af0b3b298b42",
          },
        };
      }

      let ordersResult = await dynamoDbLib.call("scan", ordersParams);

      for (var x in ordersResult.Items) {
        console.log(ordersResult.Items[x].cartId);

        let invoice_lines = [];
        let discount = ordersResult.Items[x].discount
          ? ordersResult.Items[x].discount
          : 0;
        let sort = 0;
        for (var y in ordersResult.Items[x].items) {
          sort++;
          let this_product = await dynamoDbLib.call("scan", {
            TableName: process.env.objectsTableName,
            FilterExpression:
              "objectType = :objectType and objectId = :objectId",
            ExpressionAttributeValues: {
              ":objectType": "product",
              ":objectId": ordersResult.Items[x].items[y].id,
            },
          });

          let applied_discount = null;
          let product_discount = this_product.Items[0].objectMaximumDiscount
            ? this_product.Items[0].objectMaximumDiscount
            : null;
          if (product_discount != null) {
            if (discount >= product_discount) {
              applied_discount = product_discount;
            } else {
              applied_discount = discount;
            }
          } else {
            applied_discount = discount;
          }

          if (this_product.Items.length == 1) {
            let category_name = "";
            for (var z in categories.Items) {
              if (
                categories.Items[z].objectId ==
                this_product.Items[0].objectCategory
              ) {
                category_name = categories.Items[z].objectName;
              }
            }

            invoice_lines.push({
              SortOrder: sort,
              //ExemptVat: (this_product.Items[0].objectMVA == 0) ? true : false,
              ProductCode: this_product.Items[0].powerOfficeProductCode,
              Description:
                this_product.Items[0].objectName + " - " + category_name,
              Quantity: ordersResult.Items[x].items[y].quantity,
              DiscountPercent: applied_discount / 100,
              UnitPrice: parseFloat(ordersResult.Items[x].items[y].price),
              NetAmount:
                parseFloat(ordersResult.Items[x].items[y].price) *
                ordersResult.Items[x].items[y].quantity,
            });
          }
        }

        invoice_lines.push({
          SortOrder: invoice_lines.length + 1,
          ProductCode: shipping_product_code,
          Description: "Shipping Cost",
          Quantity: 1,
          UnitPrice: parseFloat(ordersResult.Items[x].freight),
          TotalAmount: parseFloat(ordersResult.Items[x].freight),
          NetAmount: parseFloat(ordersResult.Items[x].freight),
          DiscountPercent: 0,
        });

        let this_comapny_id = ordersResult.Items[x].companyId;
        let this_customer_id = null;
        let address_id = null;

        let shops_permissions = await dynamoDbLib.call("scan", {
          TableName: process.env.userPermissionsTableName,
          FilterExpression:
            "companyId = :companyId and roleType = :roleType and isActive = :isActive and shopId = :shopId",
          ExpressionAttributeValues: {
            ":companyId": this_comapny_id,
            ":roleType": "shop_access_role",
            ":isActive": true,
            ":shopId": shop_id,
          },
        });

        this_customer_id = shops_permissions.Items[0].powerofficeCustomerId;
        let poweroffice_customers = await poweroffice.getCustomer(
          this_customer_id
        );

        let customer_id = null;
        let this_customer_code = null;
        let this_customer_email = null;
        let delivery_type = null;
        let customer_refrence = "";
        let delivery_point = ordersResult.Items[x].delivery_point;

        if (poweroffice_customers.data.id == this_customer_id) {
          customer_id = poweroffice_customers.data.id;
          this_customer_code = poweroffice_customers.data.code;
          this_customer_email = poweroffice_customers.data.emailAddress;
          delivery_type = poweroffice_customers.data.invoiceDeliveryType;

          let poweroffice_customer_contact_persons =
            await poweroffice.getCustomerContactPerson(customer_id);
          if (poweroffice_customer_contact_persons.data.length > 0) {
            customer_refrence =
              poweroffice_customer_contact_persons.data[0].firstName +
              " " +
              poweroffice_customer_contact_persons.data[0].lastName;
          }

          for (var k in poweroffice_customers.data.streetAddresses) {
            for (var l in shopDeliveryPoints.Items) {
              let this_delivery_point = null;

              if (
                delivery_point == shopDeliveryPoints.Items[l].objectId &&
                shopDeliveryPoints.Items[l].objectAddress ==
                  poweroffice_customers.data.streetAddresses[k].address1
              ) {
                address_id = poweroffice_customers.data.streetAddresses[k].id;
              }
            }
          }
        }

        let thisOrderResult = await dynamoDbLib.call("scan", {
          TableName: process.env.cartTableName,
          FilterExpression: "cartId = :cartId",
          ExpressionAttributeValues: {
            ":cartId": ordersResult.Items[x].cartId,
          },
        });

        if (
          !thisOrderResult.Items[0].powerOfficeInvoiceId &&
          !thisOrderResult.Items[0].powerOfficeInvoiceIdentifier
        ) {
          let order = await poweroffice.createInvoice({
            OrderDate: convertToDate(ordersResult.Items[x].createdAt),
            CustomerCode: this_customer_code,
            CustomerEmail: this_customer_email,
            CustomerReference: customer_refrence,
            DeliveryAddressId: address_id,
            DeliveryDate: convertToDate(
              ordersResult.Items[x].delivery_date + 10800000
            ),
            InvoiceDeliveryType: delivery_type,
            currencyCode: shop_currency,
            //TotalAmount: ordersResult.Items[x].total,
            //NetAmount: ordersResult.Items[x].total,
            PurchaseOrderNo: ordersResult.Items[x].cartId,
            Status: 0,
            OutgoingInvoiceLines: invoice_lines,
          });

          let updateOrderParams = {
            TableName: process.env.cartTableName,
            Key: {
              cartId: ordersResult.Items[x].cartId,
            },

            UpdateExpression:
              "SET powerOfficeInvoiceId = :powerOfficeInvoiceId,powerOfficeInvoiceIdentifier = :powerOfficeInvoiceIdentifier",
            ExpressionAttributeValues: {
              ":powerOfficeInvoiceId": order.data.data.orderNo,
              ":powerOfficeInvoiceIdentifier": order.data.data.id,
            },
            ReturnValues: "ALL_NEW",
          };

          await dynamoDbLib.call("update", updateOrderParams);
        }

        // if this is a stripe order.

        if (
          ordersResult.Items[x].paymentMethod == "stripe" &&
          ordersResult.Items[x].PaymentMethodId != "manual"
        ) {
          let transaction_id = ordersResult.Items[x].transactionId;

          const stripeTransactionResult = await dynamoDbLib.call("scan", {
            TableName: process.env.transactionsTableName,
            FilterExpression: "transactionId = :transactionId",
            ExpressionAttributeValues: {
              ":transactionId": transaction_id,
            },
          });
          let stripeTransaction = stripeTransactionResult.Items[0];

          let charge_id =
            stripeTransaction.transactionDetails.charges.data[0].id;

          let total_amount = 0;
          let stripe_fees = 0;

          const stripeIntegrationResult = await dynamoDbLib.call("scan", {
            TableName: process.env.integrationsTableName,
            FilterExpression:
              "integrationType = :integrationType and integrationId = :integrationId",
            ExpressionAttributeValues: {
              ":integrationType": "stripe",
              ":integrationId": shops.Items[0].objectStripeIntegration,
            },
          });
          let stripeIntegration = stripeIntegrationResult.Items[0];
          let pkey = stripeIntegration.integrationPKey;
          let skey = stripeIntegration.integrationSKey;

          const stripe = require("stripe")(skey);

          let charge = await stripe.charges.retrieve(charge_id, {
            expand: ["balance_transaction"],
          });

          total_amount = parseFloat(charge.balance_transaction.amount / 100);
          stripe_fees = parseFloat(charge.balance_transaction.fee / 100);

          let p1 = await poweroffice.createBankJournalVoucher({
            voucherDate: convertToDate(ordersResult.Items[x].createdAt),
            CurrencyCode: shop_currency,
            Description: "Stripe order",

            Lines: [
              {
                AccountCode: poweroffice_bank_fees_account,
                Amount: stripe_fees,
                CurrencyCode: shop_currency,
                Date: convertToDate(ordersResult.Items[x].createdAt),
                CustomMatchingReference: ordersResult.Items[x].cartId,
              },
              {
                AccountCode: stripe_poweroffice_customer_id,
                Amount: total_amount - stripe_fees,
                CurrencyCode: shop_currency,
                Date: convertToDate(ordersResult.Items[x].createdAt),
                CustomMatchingReference: ordersResult.Items[x].cartId,
              },
              {
                AccountCode: this_customer_code,
                Amount: -1 * total_amount,
                CurrencyCode: shop_currency,
                Date: convertToDate(ordersResult.Items[x].createdAt),
                CustomMatchingReference: ordersResult.Items[x].cartId,
              },
            ],
          });

          /*

          let p3 = await poweroffice.createBankJournalVoucher({
            voucherDate: convertToDate(ordersResult.Items[x].createdAt),
            CurrencyCode: 'nok',
            Description: 'Stripe fees',
            ExternalImportReference: ordersResult.Items[x].cartId,
            Lines: [{
                AccountCode: poweroffice_bank_fees_account,
                Amount: stripe_fees,
                CurrencyCode: 'nok',
                Date: convertToDate(ordersResult.Items[x].createdAt)
              },
              {
                AccountCode: stripe_poweroffice_customer_id,
                Amount: -1 * stripe_fees,
                CurrencyCode: 'nok',
                Date: convertToDate(ordersResult.Items[x].createdAt)
              }
            ]
          })

          */
        }
      }
    }

    callback(null, success(true));
  } catch (e) {
    //  bugsnagClient.notify(user_id, e)
    console.log(e);
    callback(
      null,
      failure({
        error: e,
      })
    );
  }
}

function convertToDate(timestamp) {
  let date = new Date(timestamp);
  return (
    date.getFullYear() + "-" + (date.getMonth() + 1) + "-" + date.getDate()
  );
}

function sleep(seconds) {
  return new Promise((resolve) => setTimeout(resolve, seconds * 1000));
}
