import * as dynamoDbLib from "../../../libs/dynamodb-lib";
import * as bugsnagClient from "../../../libs/bugsnag";
import {
  success,
  failure
} from "../../../libs/response-lib";

import {
  is_level_permitted
} from "../../../libs/permissions";

var arraySort = require('array-sort')

export async function videos(event, context, callback) {


  try {

    const access_scope = ['owner', 'manager', 'employee']

    var user_id = event.requestContext.identity.cognitoAuthenticationProvider.split('CognitoSignIn:')[1]


    const permissions = await is_level_permitted(user_id, access_scope);
    if (permissions.level_allowed) {

      let params = {}

      if (event.pathParameters.id != 'null') {
        params = {
          TableName: process.env.objectsTableName,
          FilterExpression: "objectType = :objectType and objectCompanyId = :objectCompanyId and objectShopId = :objectShopId",
          ExpressionAttributeValues: {
            ":objectType": 'video',
            ":objectCompanyId": permissions.user_company_id,
            ":objectShopId": event.pathParameters.id
          }
        };
      } else {

        params = {
          TableName: process.env.objectsTableName,
          FilterExpression: "objectType = :objectType and objectCompanyId = :objectCompanyId",
          ExpressionAttributeValues: {
            ":objectType": 'video',
            ":objectCompanyId": permissions.user_company_id
          }
        };

      }


      const result = await dynamoDbLib.call("scan", params);

      callback(null, success(result.Items));

    } else {

      callback(null, failure({
        status: 'you do not have access to this api call'
      }));

    }

  } catch (e) {
    bugsnagClient.notify(user_id, e)
    callback(null, failure({
      status: e
    }));
  }
}