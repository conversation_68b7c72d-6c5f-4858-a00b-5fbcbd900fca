import { success, failure } from "../../libs/response-lib";

import { is_level_permitted } from "../../libs/permissions";
import * as bugsnagClient from "../../libs/bugsnag";
import * as dynamoDbLib from "../../libs/dynamodb-lib";

import PowerOffice from "../../libs/poweroffice";

const AWS = require("aws-sdk");
const cognito_client = new AWS.CognitoIdentityServiceProvider();

export async function init(event, context, callback) {
  try {
    let shop_id = "dfea4f90-c455-11e9-9ab0-7fc92b1e522d";
    //let shop_id = 'e3336010-c455-11e9-9ab0-7fc92b1e522d'
    //let shop_id = event.Records[0].Sns.Message;
    //let shop_id = 'dfea4f90-c455-11e9-9ab0-7fc92b1e522d'

    const shopsSarams = {
      TableName: process.env.objectsTableName,
      FilterExpression: "objectType = :objectType and objectId = :objectId",
      ExpressionAttributeValues: {
        ":objectType": "shop",
        ":objectId": shop_id,
      },
    };

    const shops = await dynamoDbLib.call("scan", shopsSarams);

    let orderDeadline = shops.Items[0].orderDeadline
      ? parseInt(shops.Items[0].orderDeadline)
      : 0;
    let orderDeadlineTime = shops.Items[0].orderDeadlineTime
      ? parseInt(shops.Items[0].orderDeadlineTime)
      : 0;

    if (shops.Items[0].objectPowerOfficeIntegration) {
      let company_id = shops.Items[0].objectCompanyId;
      let migration_mode = shops.Items[0].objectPowerOfficeMigrationMode;

      const integrationParams = {
        TableName: process.env.integrationsTableName,
        FilterExpression: "integrationId = :integrationId",
        ExpressionAttributeValues: {
          ":integrationId": shops.Items[0].objectPowerOfficeIntegration,
        },
      };

      const integrations = await dynamoDbLib.call("scan", integrationParams);
      let poweroffice_client_key = integrations.Items[0].integrationPOAppKey;
      let poweroffice_secrect_key =
        integrations.Items[0].integrationPOClientKey;
      let poweroffice_0_mva_account = integrations.Items[0].integrationPO0MVA;
      let poweroffice_15_mva_account = integrations.Items[0].integrationPO15MVA;
      let poweroffice_25_mva_account = integrations.Items[0].integrationPO25MVA;
      let migration_start_date = integrations.Items[0].createdAt;

      let poweroffice = await new PowerOffice(
        process.env.POWEROFFICE_APP_KEY,
        poweroffice_secrect_key
      );
      console.log(process.env.POWEROFFICE_APP_KEY, poweroffice_secrect_key);
      // Product Group Creationg

      /*

      let categoriesParams = {
        TableName: process.env.objectsTableName,
        FilterExpression:
          "objectType = :objectType and objectShopId = :objectShopId",
        ExpressionAttributeValues: {
          ":objectType": "category",
          ":objectShopId": shop_id,
        },
      };

      let categories = await dynamoDbLib.call("scan", categoriesParams);

      for (var x in categories.Items) {
        const result = await dynamoDbLib.call("update", {
          TableName: process.env.objectsTableName,

          Key: {
            objectCompanyId: company_id,
            objectId: categories.Items[x].objectId,
          },

          UpdateExpression:
            "SET powerOfficeCategoryId = :powerOfficeCategoryId",
          ExpressionAttributeValues: {
            ":powerOfficeCategoryId": "",
          },
          ReturnValues: "ALL_NEW",
        });

        console.log(`Category: ${categories.Items[x].objectId}`);
      }

      // end of Product Group Creationg

      // Product Sync

      let productsParams = {
        TableName: process.env.objectsTableName,
        FilterExpression:
          "objectType = :objectType and objectShopId = :objectShopId",
        ExpressionAttributeValues: {
          ":objectType": "product",
          ":objectShopId": shop_id,
        },
      };
      let products = await dynamoDbLib.call("scan", productsParams);

      for (var x in products.Items) {
        const result = await dynamoDbLib.call("update", {
          TableName: process.env.objectsTableName,

          Key: {
            objectCompanyId: company_id,
            objectId: products.Items[x].objectId,
          },

          UpdateExpression:
            "SET powerOfficeProductId = :powerOfficeProductId,powerOfficeProductCode = :powerOfficeProductCode",
          ExpressionAttributeValues: {
            ":powerOfficeProductId": "",
            ":powerOfficeProductCode": "",
          },
          ReturnValues: "ALL_NEW",
        });

        console.log(`Product: ${products.Items[x].objectId}`);
      }

      // set the shipping as products

      let shipping_product_id = null;
      let shipping_product_code = null;

      const result = await dynamoDbLib.call("update", {
        TableName: process.env.objectsTableName,

        Key: {
          objectCompanyId: company_id,
          objectId: shop_id,
        },

        UpdateExpression:
          "SET objectPowerOfficeShippingProductId = :objectPowerOfficeShippingProductId,objectPowerOfficeShippingProductCode = :objectPowerOfficeShippingProductCode",
        ExpressionAttributeValues: {
          ":objectPowerOfficeShippingProductId": "",
          ":objectPowerOfficeShippingProductCode": "",
        },
        ReturnValues: "ALL_NEW",
      });

      console.log(`Shipping Product Update...`);

      */

      // end of products sync

      // customers sync

      let shops_permissions = await dynamoDbLib.call("scan", {
        TableName: process.env.userPermissionsTableName,
        FilterExpression:
          "grantedBy = :grantedBy and roleType = :roleType and isActive = :isActive and shopId = :shopId",
        ExpressionAttributeValues: {
          ":grantedBy": company_id,
          ":roleType": "shop_access_role",
          ":isActive": true,
          ":shopId": shop_id,
        },
      });

      let customers = [];

      let shopDeliveryPoints = await dynamoDbLib.call("scan", {
        TableName: process.env.objectsTableName,
        FilterExpression:
          "objectType = :objectType and objectShopId = :objectShopId",
        ExpressionAttributeValues: {
          ":objectType": "delivery_point",
          ":objectShopId": shop_id,
        },
      });

      for (var x in shops_permissions.Items) {
        let customer = await dynamoDbLib.call("scan", {
          TableName: process.env.objectsTableName,
          FilterExpression: "objectType = :objectType and objectId = :objectId",
          ExpressionAttributeValues: {
            ":objectType": "company",
            ":objectId": shops_permissions.Items[x].companyId,
          },
        });

        let pool_id = process.env.COGNITO_POOL_ID;
        let users = [];
        let permission_id = shops_permissions.Items[x].permissionId;
        let poweroffice_customer_id = shops_permissions.Items[x]
          .powerofficeCustomerId
          ? shops_permissions.Items[x].powerofficeCustomerId
          : null;

        let user_pool = await cognito_client
          .describeUserPool({
            UserPoolId: pool_id,
          })
          .promise();

        let number_of_users = user_pool.UserPool.EstimatedNumberOfUsers;

        let loops = Math.ceil(number_of_users / 60);

        let token = "";

        for (var i = 1; i <= loops; i++) {
          let ownersParams = {};

          if (token != "") {
            ownersParams = {
              UserPoolId: pool_id,
              AttributesToGet: null,
              Filter: 'name = "' + shops_permissions.Items[x].companyId + '"',
              Limit: 0,
              PaginationToken: token,
            };
          } else {
            ownersParams = {
              UserPoolId: pool_id,
              AttributesToGet: null,
              Filter: 'name = "' + shops_permissions.Items[x].companyId + '"',
              Limit: 0,
            };
          }

          let users_data = await cognito_client
            .listUsers(ownersParams)
            .promise();

          token = users_data.PaginationToken;

          for (var z in users_data.Users) {
            let user_type = "";
            let user_company_id = "";
            let email_verified = false;
            let owner_set = false;

            for (var y in users_data.Users[z].Attributes) {
              if (
                users_data.Users[z].Attributes[y].Name == "custom:user_type"
              ) {
                user_type = users_data.Users[z].Attributes[y].Value;
              }

              if (
                users_data.Users[z].Attributes[y].Name == "custom:company_id"
              ) {
                user_company_id = users_data.Users[z].Attributes[y].Value;
              }

              if (users_data.Users[z].Attributes[y].Name == "email_verified") {
                email_verified = users_data.Users[z].Attributes[y].Value;
              }
            }

            let contact_set = shops_permissions.Items[x].contactId
              ? true
              : false;

            if (
              !owner_set &&
              user_type == "owner" &&
              user_company_id == shops_permissions.Items[x].companyId
            ) {
              owner_set = true;
              let first_name = "";
              let last_name = "";
              let email = "";
              let phone = "";
              let address = customer.Items[0].objectAddress;

              let post_code = customer.Items[0].objectPostCode;
              let company_name = customer.Items[0].objectName;
              let organization_id = customer.Items[0].objectOrganizationId;
              let invoice_delivery_type = shops_permissions.Items[x]
                .invoice_delivery_type
                ? shops_permissions.Items[x].invoice_delivery_type
                : 1;
              let city = customer.Items[0].objectCity;
              let customer_company_id = "";

              if (invoice_delivery_type == "mail") {
                invoice_delivery_type = 1;
              }

              if (invoice_delivery_type == "ehf") {
                invoice_delivery_type = 3;
              }
              if (invoice_delivery_type == "avtale_giro") {
                invoice_delivery_type = 4;
              }

              for (var y in users_data.Users[z].Attributes) {
                if (
                  users_data.Users[z].Attributes[y].Name == "custom:first_name"
                ) {
                  first_name = users_data.Users[z].Attributes[y].Value;
                }
                if (
                  users_data.Users[z].Attributes[y].Name == "custom:last_name"
                ) {
                  last_name = users_data.Users[z].Attributes[y].Value;
                }
                if (
                  users_data.Users[z].Attributes[y].Name == "custom:user_phone"
                ) {
                  phone = users_data.Users[z].Attributes[y].Value;
                }
                if (
                  users_data.Users[z].Attributes[y].Name == "custom:post_code"
                ) {
                  //  post_code = users_data.Users[z].Attributes[y].Value
                }
                if (
                  users_data.Users[z].Attributes[y].Name == "custom:company_id"
                ) {
                  customer_company_id = users_data.Users[z].Attributes[y].Value;
                }
                if (
                  users_data.Users[z].Attributes[y].Name ==
                  "custom:company_name"
                ) {
                  //company_name = users_data.Users[z].Attributes[y].Value
                }
                if (
                  users_data.Users[z].Attributes[y].Name ==
                  "custom:organization_id"
                ) {
                  //organization_id = users_data.Users[z].Attributes[y].Value
                }
                if (users_data.Users[z].Attributes[y].Name == "custom:city") {
                  //city = users_data.Users[z].Attributes[y].Value
                }
                if (users_data.Users[z].Attributes[y].Name == "email") {
                  email = users_data.Users[z].Attributes[y].Value;
                }

                if (users_data.Users[z].Attributes[y].Name == "address") {
                  //address = users_data.Users[z].Attributes[y].Value
                }
              }

              // get customer delivery points

              let customer_address = [];

              const deliveryPointsPermissions = await dynamoDbLib.call("scan", {
                TableName: process.env.userPermissionsTableName,
                FilterExpression: "userId = :userId",
                ExpressionAttributeValues: {
                  ":userId": customer_company_id,
                },
              });

              for (var w in deliveryPointsPermissions.Items) {
                for (var z in shopDeliveryPoints.Items) {
                  if (
                    deliveryPointsPermissions.Items[w].locationId ==
                    shopDeliveryPoints.Items[z].objectId
                  ) {
                    if (
                      deliveryPointsPermissions.Items[w].powerofficeAddressId
                    ) {
                      customer_address.push({
                        Id: deliveryPointsPermissions.Items[w]
                          .powerofficeAddressId,
                        Address1: shopDeliveryPoints.Items[z].objectAddress,
                        CountryCode: "NO",
                        IsPrimary: false,
                      });
                    } else {
                      customer_address.push({
                        Address1: shopDeliveryPoints.Items[z].objectAddress,
                        CountryCode: "NO",
                        IsPrimary: false,
                      });
                    }
                  }
                }
              }

              for (var z in users_data.Users) {
                if (
                  users_data.Users[z].Username ==
                  shops_permissions.Items[x].contactId
                ) {
                  for (var y in users_data.Users[z].Attributes) {
                    if (
                      users_data.Users[z].Attributes[y].Name ==
                      "custom:first_name"
                    ) {
                      first_name = users_data.Users[z].Attributes[y].Value;
                    }
                    if (
                      users_data.Users[z].Attributes[y].Name ==
                      "custom:last_name"
                    ) {
                      last_name = users_data.Users[z].Attributes[y].Value;
                    }
                    if (
                      users_data.Users[z].Attributes[y].Name ==
                      "custom:user_phone"
                    ) {
                      phone = users_data.Users[z].Attributes[y].Value;
                    }

                    if (users_data.Users[z].Attributes[y].Name == "email") {
                      email = users_data.Users[z].Attributes[y].Value;
                    }
                  }
                }
              }

              // end get customer delivery points

              customers.push({
                first_name,
                last_name,
                phone,
                email,
                address,
                post_code,
                company_name,
                organization_id,
                city,
                customer_company_id,
                invoice_delivery_type,
                customer_address,
                poweroffice_customer_id,
                permission_id,
              });
            }
          }
        }
      }

      customers = removeDuplicates(customers, "customer_company_id");

      for (var x in customers) {
        if (customers[x].poweroffice_customer_id != 16056071) {
          await dynamoDbLib.call("update", {
            TableName: process.env.userPermissionsTableName,

            Key: {
              permissionId: customers[x].permission_id,
            },

            UpdateExpression:
              "SET powerofficeCustomerId = :powerofficeCustomerId",
            ExpressionAttributeValues: {
              ":powerofficeCustomerId": "",
            },
            ReturnValues: "ALL_NEW",
          });
        }
        console.log(`Customer Update: ${customers[x].permission_id}`);
      }

      console.log("FINISHED");
    }
  } catch (e) {
    //  bugsnagClient.notify(user_id, e)
    console.log(e);

    callback(
      null,
      failure({
        error: e,
      })
    );
  }
}

function removeDuplicates(myArr, prop) {
  return myArr.filter((obj, pos, arr) => {
    return arr.map((mapObj) => mapObj[prop]).indexOf(obj[prop]) === pos;
  });
}
