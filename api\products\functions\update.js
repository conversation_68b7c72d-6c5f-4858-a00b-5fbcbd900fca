import * as dynamoDbLib from "../../../libs/dynamodb-lib";
import * as bugsnagClient from "../../../libs/bugsnag";
import {
  success,
  failure
} from "../../../libs/response-lib";

import {
  is_level_permitted
} from "../../../libs/permissions";

const AWS = require('aws-sdk')
const SNS = new AWS.SNS();

export async function product(event, context, callback) {


  try {

    const access_scope = ['owner', 'manager', 'employee']

    const data = JSON.parse(event.body);
    var user_id = event.requestContext.identity.cognitoAuthenticationProvider.split('CognitoSignIn:')[1]


    const permissions = await is_level_permitted(user_id, access_scope);
    if (permissions.level_allowed) {

      const params = {
        TableName: process.env.objectsTableName,

        Key: {
          objectCompanyId: permissions.user_company_id,
          objectId: event.pathParameters.id
        },

        UpdateExpression: "SET objectName = :objectName, objectVisibility = :objectVisibility, objectPrice = :objectPrice,objectCost = :objectCost,objectMVA = :objectMVA,objectDesc = :objectDesc,objectImage = :objectImage,objectCategory = :objectCategory,objectLocation = :objectLocation, ObjectLastUpdatedBy = :ObjectLastUpdatedBy, objectMinimumOrderQuantity = :objectMinimumOrderQuantity,objectMaximumOrderQuantity = :objectMaximumOrderQuantity, objectMaximumDiscount = :objectMaximumDiscount",
        ExpressionAttributeValues: {
          ":objectName": data.objectName ? data.objectName : null,
          ":objectVisibility": data.objectVisibility ? data.objectVisibility : false,
          ":objectPrice": data.objectPrice ? data.objectPrice : null,
          ":objectCost": data.objectCost ? data.objectCost : null,
          ":objectMVA": data.objectMVA ? data.objectMVA : 0,
          ":objectMinimumOrderQuantity": data.objectMinimumOrderQuantity ? data.objectMinimumOrderQuantity : 1,
          ":objectMaximumOrderQuantity": data.objectMaximumOrderQuantity ? data.objectMaximumOrderQuantity : 99,
          ":objectMaximumDiscount": data.objectMaximumDiscount ? data.objectMaximumDiscount : null,
          ":objectDesc": data.objectDesc ? data.objectDesc : null,
          ":objectImage": data.objectImage ? data.objectImage : null,
          ":objectCategory": data.objectCategory ? data.objectCategory : null,
          ":objectLocation": data.objectLocation ? data.objectLocation : null,
          ":ObjectLastUpdatedBy": user_id
        },
        ReturnValues: "ALL_NEW"
      };

      const result = await dynamoDbLib.call("update", params);

      await SNS.publish({
        Message: event.pathParameters.id,
        TopicArn: process.env.generatePowerOfficeProductsSNSTopic
      }, function(err, data) {

        if (err) {
          callback(null, err);
        }

      });


      callback(null, success({
        status: true
      }));

    } else {

      callback(null, failure({
        status: 'you do not have access to this api call'
      }));

    }

  } catch (e) {
    bugsnagClient.notify(user_id, e)
    callback(null, failure({
      status: false
    }));
  }
}