import uuid from "uuid";
import * as dynamoDbLib from "../../../libs/dynamodb-lib";
import * as bugsnagClient from "../../../libs/bugsnag";


import {
  success,
  failure
} from "../../../libs/response-lib";

import {
  is_level_permitted
} from "../../../libs/permissions";

const AWS = require('aws-sdk')


export async function task(event, context, callback) {


  try {

    let access_scope = ['owner', 'manager', 'employee']

    const data = JSON.parse(event.body);
    const SNS = new AWS.SNS();
    var user_id = event.requestContext.identity.cognitoAuthenticationProvider.split('CognitoSignIn:')[1]


    const permissions = await is_level_permitted(user_id, access_scope);

    if (permissions.level_allowed) {

      let task_id = uuid.v1()


      var today = new Date();
      var h = today.getHours();
      var m = today.getMinutes();
      var s = today.getSeconds();


      let assignees = null

      if (data.taskType == 'deviation_review') {

        let locationParam = {
          TableName: process.env.objectsTableName,
          FilterExpression: "objectId = :objectId",
          ExpressionAttributeValues: {
            ":objectId": data.locationId
          }
        };

        const locationResult = await dynamoDbLib.call("scan", locationParam);

        let autoAssignTasks = (locationResult.Items[0].objectAutoAssignTasks) ? locationResult.Items[0].objectAutoAssignTasks : null
        if (autoAssignTasks) {
          assignees = autoAssignTasks.review
        }

      } else {
        assignees = data.assignees
      }


      const params = {

        TableName: process.env.tasksTableName,
        Item: {
          taskId: task_id,
          assignerId: user_id,
          assignees: assignees,
          taskName: data.taskName,
          taskType: data.taskType,
          taskDesc: data.taskDesc,
          taskRepeat: data.taskRepeat,
          locationId: data.locationId,
          companyId: permissions.user_company_id,
          taskStatus: 'open',
          taskDates: data.taskDates,
          taskTime: (data.taskTime) ? data.taskTime : h + ':' + m + ':' + s,
          taskDeviationFor: (data.taskDeviationFor) ? data.taskDeviationFor : null,
          TaskexceptionId: (data.TaskexceptionId) ? data.TaskexceptionId : null,
          tzo: (data.tzo) ? data.tzo : 0,
          createdAt: new Date().getTime()
        }
      };


      await dynamoDbLib.call("put", params);

      await SNS.publish({
        Message: task_id,
        TopicArn: process.env.generateCompaniesTasksSNSTopic
      }, function(err, data) {
        if (err) {
          callback(null, err);
        }
        // sns sent. good job
      });



      callback(null, success(params.Item));

    } else {

      callback(null, failure({
        status: 'you do not have access to this api call'
      }));

    }
  } catch (e) {
    bugsnagClient.notify(user_id, e)
    callback(null, failure({
      status: false
    }));
  }
}