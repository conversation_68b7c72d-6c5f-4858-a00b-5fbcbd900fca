import {
  success,
  failure
} from "../../../libs/response-lib";
import * as dynamoDbLib from "../../../libs/dynamodb-lib";
import * as bugsnagClient from "../../../libs/bugsnag";
import {
  is_level_permitted
} from "../../../libs/permissions";

const AWS = require('aws-sdk')
const cognito_client = new AWS.CognitoIdentityServiceProvider()

export async function users(event, context, callback) {



  try {

    let access_scope = ['owner', 'manager', 'employee']
    //  const data = JSON.parse(event.body);
    let user_id = event.requestContext.identity.cognitoAuthenticationProvider.split('CognitoSignIn:')[1]
    let company_id = event.pathParameters.company_id
    let shop_id = event.pathParameters.id

    if (company_id != null) {

      const permissions = await is_level_permitted(user_id, access_scope);

      if (permissions.level_allowed) {


        const shopInfo = await dynamoDbLib.call("scan", {
          TableName: process.env.objectsTableName,
          FilterExpression: "objectType = :objectType and objectId = :objectId",
          ExpressionAttributeValues: {
            ":objectType": 'shop',
            ":objectId": shop_id
          }
        });


        if (shopInfo.Items[0].objectCompanyId == permissions.user_company_id) {
          // this is the shop owner.

          let permissionParams = {
            TableName: process.env.userPermissionsTableName,
            FilterExpression: "companyId = :companyId and shopId = :shopId and isActive = :isActive and roleType = :roleType",
            ExpressionAttributeValues: {
              ":companyId": company_id,
              ":shopId": shop_id,
              ":isActive": true,
              ":roleType": 'shop_access_role'
            }
          };

          const shopPermissions = await dynamoDbLib.call("scan", permissionParams);

          if (shopPermissions.Items.length == 1) {

            let pool_id = process.env.COGNITO_POOL_ID
            let users = []

            let params = {
              UserPoolId: pool_id,
              Filter: "name = \"" + company_id + "\"",
              AttributesToGet: ['custom:first_name', 'custom:last_name', 'email', 'custom:user_phone'],
              Limit: 0
            }

            let users_data = await cognito_client.listUsers(params).promise()

            for (var x in users_data.Users) {

              let user_id = users_data.Users[x].Username
              let first_name = ''
              let last_name = ''
              let phone = ''
              let email = ''

              for (var y in users_data.Users[x].Attributes) {

                if (users_data.Users[x].Attributes[y].Name == 'custom:first_name') {
                  first_name = users_data.Users[x].Attributes[y].Value
                }

                if (users_data.Users[x].Attributes[y].Name == 'custom:last_name') {
                  last_name = users_data.Users[x].Attributes[y].Value
                }

                if (users_data.Users[x].Attributes[y].Name == 'custom:user_phone') {
                  phone = users_data.Users[x].Attributes[y].Value
                }

                if (users_data.Users[x].Attributes[y].Name == 'email') {
                  email = users_data.Users[x].Attributes[y].Value
                }

              }

              users.push({
                user_id: user_id,
                first_name: first_name,
                last_name: last_name,
                phone: phone,
                email: email,

              })
            }

            callback(null, success(users))

          } else {

            callback(null, failure({
              status: 'this company dose not have access to this shop'
            }));

          }

        } else {

          callback(null, failure({
            status: 'You are not the shop owner'
          }));

        }

      } else {

        callback(null, failure({
          status: 'you do not have access to this api call'
        }));

      }
    }
  } catch (e) {
    bugsnagClient.notify(user_id, e)
    callback(null, failure({
      status: e
    }));
  }

}