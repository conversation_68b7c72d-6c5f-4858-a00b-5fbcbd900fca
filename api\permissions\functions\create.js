import uuid from "uuid";
import * as dynamoDbLib from "../../../libs/dynamodb-lib";
import * as bugsnagClient from "../../../libs/bugsnag";

import {
  success,
  failure
} from "../../../libs/response-lib";

import {
  is_level_permitted,
  is_object_permitted
} from "../../../libs/permissions";

export async function permission(event, context, callback) {

  const access_scope = ['owner', 'manager', 'employee']


  try {
    const data = JSON.parse(event.body);
    var user_id = event.requestContext.identity.cognitoAuthenticationProvider.split('CognitoSignIn:')[1]


    const permissions = await is_level_permitted(user_id, access_scope);
    if (permissions.level_allowed) {

      let go = true
      /*
      if (permissions.location_allowed) {
        go = true
      } else {

        const location_permission = await is_object_permitted(user_id, permissions.user_company_id, data.locationId);

        if (location_permission.is_object_allowed) {
          go = true
        }
      }
      */

      if (go) {

        const params = {

          TableName: process.env.userPermissionsTableName,
          Item: {
            grantedBy: user_id,
            userId: data.userId,
            companyId: permissions.user_company_id,
            locationId: data.locationId,
            permissionId: uuid.v1(),
            createdAt: new Date().getTime()
          }
        };

        await dynamoDbLib.call("put", params);
        callback(null, success(params.Item));

      } else {

        callback(null, failure({
          status: 'you do not have access to this api call'
        }));

      }

    } else {

      callback(null, failure({
        status: 'you do not have access to this api call'
      }));

    }

  } catch (e) {
    bugsnagClient.notify(user_id, e)
    callback(null, failure({
      error: e
    }))
  }


}