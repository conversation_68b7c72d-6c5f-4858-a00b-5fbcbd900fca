import * as dynamoDbLib from "../../../libs/dynamodb-lib";
import * as bugsnagClient from "../../../libs/bugsnag";
import {
  success,
  failure
} from "../../../libs/response-lib";

import {
  is_level_permitted
} from "../../../libs/permissions";

const AWS = require('aws-sdk')
const SNS = new AWS.SNS();

export async function customer(event, context, callback) {

  try {

    const access_scope = ['owner', 'manager', 'employee']
    const data = JSON.parse(event.body);
    var user_id = event.requestContext.identity.cognitoAuthenticationProvider.split('CognitoSignIn:')[1]

    const permissions = await is_level_permitted(user_id, access_scope);
    if (permissions.level_allowed) {

      let company_id = data.company_id
      let shop_id = data.shop_id
      let discount = data.discount
      let shipping = (data.shipping) ? data.shipping : null
      let poweroffice_customers_id = (data.poweroffice_customers_id) ? data.poweroffice_customers_id : null
      let allow_volume_orders = data.allow_volume_orders
      let allow_recurring_orders = data.allow_recurring_orders
      let invoice_delivery_type = data.invoice_delivery_type
      let allowed_payment_options = data.allowed_payment_options


      let shops_permissions = await dynamoDbLib.call("scan", {
        TableName: process.env.userPermissionsTableName,
        FilterExpression: "grantedBy = :grantedBy and roleType = :roleType and companyId = :companyId and isActive = :isActive and shopId = :shopId",
        ExpressionAttributeValues: {
          ":grantedBy": permissions.user_company_id,
          ":roleType": 'shop_access_role',
          ":isActive": true,
          ":shopId": shop_id,
          ":companyId": company_id
        }
      })

      let permission_id = shops_permissions.Items[0].permissionId

      const result = await dynamoDbLib.call("update", {
        TableName: process.env.userPermissionsTableName,

        Key: {
          permissionId: permission_id
        },

        UpdateExpression: "SET discount = :discount,shipping = :shipping, powerofficeCustomerId = :powerofficeCustomerId, allow_volume_orders = :allow_volume_orders, allow_recurring_orders = :allow_recurring_orders ,invoice_delivery_type = :invoice_delivery_type,allowed_payment_options = :allowed_payment_options",
        ExpressionAttributeValues: {
          ":discount": discount,
          ":shipping": shipping,
          ":powerofficeCustomerId": poweroffice_customers_id,
          ":allow_volume_orders": allow_volume_orders,
          ":allow_recurring_orders": allow_recurring_orders,
          ":invoice_delivery_type": invoice_delivery_type,
          ":allowed_payment_options": allowed_payment_options
        },
        ReturnValues: "ALL_NEW"
      });

      await SNS.publish({
        Message: permission_id,
        TopicArn: process.env.generatePowerOfficeCustomersSNSTopic
      }, function(err, data) {

        if (err) {
          callback(null, err);
        }

      });

      callback(null, success({
        status: shops_permissions
      }));

    } else {

      callback(null, failure({
        status: 'you do not have access to this api call'
      }));

    }


  } catch (e) {
    bugsnagClient.notify(user_id, e)
    callback(null, failure({
      status: e
    }));
  }
}