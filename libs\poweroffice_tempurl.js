const axios = require('axios');
const oauth = require('axios-oauth-client');

export default class PowerOfficeTempUrl {

  constructor(client_organization_no) {

    return new Promise(async (resolve, reject) => {
      try {
        this.client_id = process.env.POWEROFFICE_APP_KEY;
        this.client_organization_no = client_organization_no;
        this.api_url = process.env.POWEROFFICE_API_URL;
        this.callback_url = process.env.POWEROFFICE_CALLBACK_URL;

        this.axiosInstance = axios.create({
          baseURL: this.api_url,
          timeout: 300000,
        })

      } catch (ex) {
        return reject(ex);
      }
      resolve(this);
    });
  }

  async createTemporaryUrl(redirect_url, integration_id) {
    let result = await this.axiosInstance.post('/clientauth/createtemporaryurl', {
      "applicationKey": this.client_id,
      "clientOrganizationNo": this.client_organization_no,
      "callbackUri": this.callback_url + integration_id,
      //"redirectUri": this.callback_url + integration_id,
      "redirectUri": redirect_url + '/' + integration_id + '/'
    })
    return result.data

  }
}