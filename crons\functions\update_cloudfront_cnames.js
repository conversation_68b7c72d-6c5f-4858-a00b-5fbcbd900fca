import {
  success,
  failure
} from "../../libs/response-lib";
import * as dynamoDbLib from "../../libs/dynamodb-lib";
import * as bugsnagClient from "../../libs/bugsnag";
const AWS = require('aws-sdk')

var cloudfront = new AWS.CloudFront({
  apiVersion: '2018-11-05',
  accessKeyId: '********************',
  secretAccessKey: '6bB750d1OHP1Efz2f1AXrXO8kPZhAOpKf22xsyVk',
});

export async function update(event, context, callback) {


  const params = {
    TableName: process.env.objectsTableName,
    FilterExpression: "objectType = :objectType and objectDomain <> :objectDomain",
    ExpressionAttributeValues: {
      ":objectType": 'shop',
      ":objectDomain": null
    }
  };


  const shops = await dynamoDbLib.call("scan", params);

  let aliases = []
  aliases.push('*.' + process.env.SHOP_DOMAIN_NAME)

  for (var x in shops.Items) {
    if (shops.Items[x].objectDomain) {
      aliases.push(shops.Items[x].objectDomain);
    }
  }


  let data = await cloudfront.getDistributionConfig({
    Id: process.env.SHOP_CLOUDFRONT_DIST
  }).promise();

  let ETag = data.ETag

  let DistributionConfig = data.DistributionConfig
  DistributionConfig.Aliases.Items = aliases
  DistributionConfig.Aliases.Quantity = aliases.length

  await cloudfront.updateDistribution({
    DistributionConfig: DistributionConfig,
    Id: process.env.SHOP_CLOUDFRONT_DIST,
    IfMatch: ETag
  }).promise();

}