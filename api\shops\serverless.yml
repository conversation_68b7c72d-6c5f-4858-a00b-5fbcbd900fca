service: kompis-shops

plugins:
  - serverless-webpack

custom:
  webpackIncludeModules: true
  stage: ${opt:stage, self:provider.stage}
  stageSufix:
    prod: ""
    dev: -dev
  congnito_pool_id:
    dev: eu-central-1_o36r1nMGj
    prod: eu-central-1_E1nLfop0C
  shop_cloudfront_dist:
    dev: E3LF0LR3XFNVP4
    prod: E2IUXGMYQJH84L

  poweroffice_auth_url:
    dev: https://api-demo.poweroffice.net/OAuth/Token
    prod: https://api.poweroffice.net/OAuth/Token
  poweroffice_api_url:
    dev: https://api-demo.poweroffice.net
    prod: https://api.poweroffice.net

provider:
  versionFunctions: false
  name: aws
  endpointType: REGIONAL
  runtime: nodejs16.x
  stage: dev
  region: eu-central-1
  environment:
    COGNITO_POOL_ID: ${self:custom.congnito_pool_id.${self:provider.stage}}
    POWEROFFICE_AUTH_URL: ${self:custom.poweroffice_auth_url.${self:provider.stage}}
    POWEROFFICE_API_URL: ${self:custom.poweroffice_api_url.${self:provider.stage}}
    SHOP_CLOUDFRONT_DIST: ${self:custom.shop_cloudfront_dist.${self:provider.stage}}
    generateCompaniesTasksSNSTopic: arn:aws:sns:eu-central-1:589634798762:generate_companies_tasks${self:custom.stageSufix.${self:custom.stage}}
    updateCloudFrontCnamesSNSTopic: arn:aws:sns:eu-central-1:589634798762:update_cloudfront_cnames${self:custom.stageSufix.${self:custom.stage}}
    cartTableName: cart${self:custom.stageSufix.${self:custom.stage}}
    exceptionsTableName: exceptions${self:custom.stageSufix.${self:custom.stage}}
    objectsTableName: objects${self:custom.stageSufix.${self:custom.stage}}
    sensorsDataLogsTableName: sensorsDataLogs${self:custom.stageSufix.${self:custom.stage}}
    tasksTableName: tasks${self:custom.stageSufix.${self:custom.stage}}
    tasksLogTableName: tasksLog${self:custom.stageSufix.${self:custom.stage}}
    tasksSchedulesTableName: tasksSchedules${self:custom.stageSufix.${self:custom.stage}}
    userPermissionsTableName: userPermissions${self:custom.stageSufix.${self:custom.stage}}
    logsTableName: logs${self:custom.stageSufix.${self:custom.stage}}
    domainsTableName: domains${self:custom.stageSufix.${self:custom.stage}}
    generatePowerOfficeShippingSNSTopic: arn:aws:sns:eu-central-1:589634798762:generate_poweroffice_shipping${self:custom.stageSufix.${self:custom.stage}}

  iamRoleStatements:
    - Effect: "Allow"
      Action:
        - "SNS:Publish"
      Resource: "arn:aws:sns:eu-central-1:589634798762:generate_companies_tasks${self:custom.stageSufix.${self:custom.stage}}"

    - Effect: "Allow"
      Action:
        - "SNS:Publish"
      Resource: "arn:aws:sns:eu-central-1:589634798762:generate_poweroffice_shipping${self:custom.stageSufix.${self:custom.stage}}"

    - Effect: "Allow"
      Action:
        - "SNS:Publish"
      Resource: "arn:aws:sns:eu-central-1:589634798762:update_cloudfront_cnames${self:custom.stageSufix.${self:custom.stage}}"

    - Effect: "Allow"
      Action:
        - "cognito-idp:*"
      Resource: "arn:aws:cognito-idp:eu-central-1:*:userpool/*"

    - Effect: Allow
      Action:
        - dynamodb:Query
        - dynamodb:Scan
        - dynamodb:GetItem
        - dynamodb:PutItem
        - dynamodb:UpdateItem
        - dynamodb:DeleteItem
      Resource: "arn:aws:dynamodb:eu-central-1:*:*"

    - Effect: "Allow"
      Action:
        - "s3:*"
      Resource: "arn:aws:s3:::kompis-app-uploads/*"

functions:
  createShop:
    handler: functions/create.shop
    events:
      - http:
          path: /
          method: post
          cors: true
          authorizer: aws_iam

  createOnboardingShop:
    handler: functions/create_onboarding.shop
    events:
      - http:
          path: /onboarding
          method: post
          cors: true
          authorizer: aws_iam

  configureShopDomain:
    handler: functions/configure_domain.shop
    events:
      - http:
          path: /configure_domain
          method: post
          cors: true
          authorizer: aws_iam

  updateShop:
    handler: functions/update.shop
    events:
      - http:
          path: /{id}
          method: put
          cors: true
          authorizer: aws_iam

  updateShopLandingPageSettings:
    handler: functions/update_landing_page_settings.shop
    events:
      - http:
          path: /landing-page-settings/{id}
          method: put
          cors: true
          authorizer: aws_iam

  deleteShop:
    handler: functions/delete.shop
    events:
      - http:
          path: /{id}
          method: delete
          cors: true
          authorizer: aws_iam

  listShops:
    handler: functions/list.shops
    events:
      - http:
          path: /
          method: get
          cors: true
          authorizer: aws_iam

  listShop:
    handler: functions/list_shop.shop
    events:
      - http:
          path: /{id}
          method: get
          cors: true
          authorizer: aws_iam

  listDomainConfigurations:
    handler: functions/list_domain_configurations.shop
    events:
      - http:
          path: /domain/{id}
          method: get
          cors: true
          authorizer: aws_iam

  finalizeDomainConfigurations:
    handler: functions/finalize_domain.shop
    timeout: 300
    memorySize: 2048
    events:
      - http:
          path: /domain-finalize/{id}
          method: get
          cors: true
          authorizer: aws_iam

  listCustomerShops:
    handler: functions/list_customer.shops
    events:
      - http:
          path: /customer/
          method: get
          cors: true
          authorizer: aws_iam

  listStats:
    handler: functions/stats.list
    events:
      - http:
          path: /stats
          method: get
          cors: true
          authorizer: aws_iam

  listDashboardStats:
    handler: functions/dashboard_stats.list
    events:
      - http:
          path: /shopstats
          method: post
          cors: true
          authorizer: aws_iam
