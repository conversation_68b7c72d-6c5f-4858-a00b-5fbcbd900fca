import uuid from "uuid";
import * as dynamoDbLib from "../../../libs/dynamodb-lib";
import * as bugsnagClient from "../../../libs/bugsnag";
import {
  success,
  failure
} from "../../../libs/response-lib";

import {
  is_level_permitted,
  is_object_permitted
} from "../../../libs/permissions";

const AWS = require('aws-sdk')
export async function task(event, context, callback) {


  try {

    let access_scope = ['owner', 'manager', 'employee']

    const data = JSON.parse(event.body);
    let user_id = event.requestContext.identity.cognitoAuthenticationProvider.split('CognitoSignIn:')[1]
    let task_id = event.pathParameters.id
    const SNS = new AWS.SNS();


    let taskParams = {
      TableName: process.env.tasksTableName,
      ScanIndexForward: false,
      Limit: 1,
      KeyConditionExpression: "taskId = :taskId",
      ExpressionAttributeValues: {
        ":taskId": task_id
      }
    };

    let task = await dynamoDbLib.call("query", taskParams);
    let assigner_id = task.Items[0].assignerId
    let location_id = task.Items[0].locationId

    let permissions = await is_level_permitted(assigner_id, access_scope);
    // get the user type for the task creator.
    let user_type = permissions.user_type

    if (user_type == 'employee') {
      access_scope = ['owner', 'manager', 'employee']
    }

    if (user_type == 'manager') {
      access_scope = ['owner', 'manager']
    }

    if (user_type == 'owner') {
      access_scope = ['owner']
    }

    permissions = await is_level_permitted(user_id, access_scope);

    if (permissions.level_allowed) {

      const location_permission = await is_object_permitted(user_id, permissions.user_company_id, location_id, permissions.user_type);

      if (location_permission.is_object_allowed) {



        let params_3 = {
          TableName: process.env.tasksTableName,
          Key: {
            taskId: task_id
          },
          UpdateExpression: "SET assignees = :assignees, taskStatus = :taskStatus, taskName = :taskName, taskDesc = :taskDesc, taskRepeat = :taskRepeat, taskDates = :taskDates, taskTime = :taskTime, locationId = :locationId",
          ExpressionAttributeValues: {
            ":assignees": data.assignees,
            ":taskName": data.taskName,
            ":taskDesc": data.taskDesc,
            ":taskRepeat": data.taskRepeat,
            ":taskDates": data.taskDates,
            ":taskTime": data.taskTime,
            ":taskStatus": 'open',
            ":locationId": data.taskLocation
          },
          ReturnValues: "ALL_NEW"
        };

        let update = await dynamoDbLib.call("update", params_3);



        let params_4 = {
          TableName: process.env.tasksSchedulesTableName,
          FilterExpression: "taskId = :taskId and taskStatus = :taskStatus",
          ExpressionAttributeValues: {
            ":taskId": task_id,
            ":taskStatus": 'open'
          }
        };

        let tasksSchedules = await dynamoDbLib.call("scan", params_4);


        for (var x in tasksSchedules.Items) {

          await dynamoDbLib.call("delete", {
            TableName: process.env.tasksSchedulesTableName,
            Key: {
              taskScheduleId: tasksSchedules.Items[x].taskScheduleId,
              taskId: task_id
            }
          });

        }

        await SNS.publish({
          Message: task_id,
          TopicArn: process.env.generateCompaniesTasksSNSTopic
        }, function(err, data) {
          if (err) {
            callback(null, err);
          }

        });


        callback(null, success({
          status: true
        }));


      } else {

        callback(null, failure({
          status: 'you do not have access to this location'
        }));

      }

    } else {

      callback(null, failure({
        status: 'you do not have access to this api call'
      }));

    }

  } catch (e) {
    bugsnagClient.notify(user_id, e)
    callback(null, failure({
      status: e
    }));
  }
}