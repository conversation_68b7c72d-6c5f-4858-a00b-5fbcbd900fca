import * as dynamoDbLib from "../../../libs/dynamodb-lib";
import * as bugsnagClient from "../../../libs/bugsnag";
import {
  success,
  failure
} from "../../../libs/response-lib";

export async function info(event, context, callback) {

  try {

    let params = {}

    if (event.pathParameters.id.includes('custom-')) {

      let domain_name = event.pathParameters.id
      domain_name = domain_name.split('custom-')
      domain_name = domain_name[1]

      params = {
        TableName: process.env.objectsTableName,
        FilterExpression: "objectType = :objectType and objectDomain = :objectDomain",
        ExpressionAttributeValues: {
          ":objectType": 'shop',
          ":objectDomain": domain_name
        }
      };

    } else {

      params = {
        TableName: process.env.objectsTableName,
        FilterExpression: "objectType = :objectType and objectId = :objectId",
        ExpressionAttributeValues: {
          ":objectType": 'shop',
          ":objectId": event.pathParameters.id
        }
      };

    }


    const result = await dynamoDbLib.call("scan", params);

    if (result.Items[0].objectPaymentMethod == 'stripe') {

      let stripe_integration_id = result.Items[0].objectStripeIntegration

      const integrationParams = {
        TableName: process.env.integrationsTableName,
        FilterExpression: "integrationType = :integrationType and integrationId = :integrationId",
        ExpressionAttributeValues: {
          ":integrationType": 'stripe',
          ":integrationId": stripe_integration_id
        }
      }

      const stripeIntegrationResult = await dynamoDbLib.call("scan", integrationParams);
      let stripeIntegration = stripeIntegrationResult.Items[0]
      let pkey = stripeIntegration.integrationPKey
      result.Items[0].pkey = pkey
    }


    const company = await dynamoDbLib.call("scan", {
      TableName: process.env.objectsTableName,
      FilterExpression: "objectType = :objectType and objectId = :objectId",
      ExpressionAttributeValues: {
        ":objectType": 'company',
        ":objectId": result.Items[0].objectCompanyId
      }
    });

    result.Items[0].shipping_tax = (company.Items[0].objectTax) ? company.Items[0].objectTax.shipping : 0
    result.Items[0].tax_name = (company.Items[0].objectTax) ? company.Items[0].objectTax.name : ''

    callback(null, success(result.Items[0]));

  } catch (e) {
    bugsnagClient.notify('guest', e)
    callback(null, failure({
      status: e
    }));
  }
}