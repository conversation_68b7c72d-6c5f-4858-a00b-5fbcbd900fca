import * as dynamoDbLib from "../../../../libs/dynamodb-lib";

import {
  success,
  failure
} from "../../../../libs/response-lib";

import {
  is_level_permitted
} from "../../../../libs/permissions";

export async function templog(event, context, callback) {

  const access_scope = ['owner', 'manager', 'employee']

  const data = JSON.parse(event.body);
  var user_id = event.requestContext.identity.cognitoAuthenticationProvider.split('CognitoSignIn:')[1]

  try {

    const permissions = await is_level_permitted(user_id, access_scope);
    if (permissions.level_allowed) {

      const params = {
        TableName: process.env.logsTableName,

        Key: {
          //  logCompanyId: permissions.user_company_id,
          logId: event.pathParameters.ids
        },

        UpdateExpression: "SET logSecondReading = :logSecondReading, logSecondReadingUpdatedBy = :logSecondReadingUpdatedBy, logSecondReadingUpdatedAt = :logSecondReadingUpdatedAt",
        ExpressionAttributeValues: {
          ":logSecondReading": data.value ? data.value : null,
          ":logSecondReadingUpdatedBy": user_id,
          ":logSecondReadingUpdatedAt": new Date().getTime(),
        },
        ReturnValues: "ALL_NEW"
      };

      const result = await dynamoDbLib.call("update", params);
      callback(null, success({
        status: true
      }));

    } else {

      callback(null, failure({
        status: 'you do not have access to this api call'
      }));

    }

  } catch (e) {
    callback(null, failure({
      status: false
    }));
  }
}