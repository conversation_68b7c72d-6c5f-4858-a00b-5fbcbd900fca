import * as dynamoDbLib from "../../../libs/dynamodb-lib";
import {
  success,
  failure
} from "../../../libs/response-lib";

import {
  is_level_permitted
} from "../../../libs/permissions";

var arraySort = require('array-sort')

export async function log(event, context, callback) {

  const access_scope = ['owner', 'manager', 'employee']

  var user_id = event.requestContext.identity.cognitoAuthenticationProvider.split('CognitoSignIn:')[1]

  try {

    const permissions = await is_level_permitted(user_id, access_scope);
    if (permissions.level_allowed) {

      const params = {
        TableName: process.env.logsTableName,
        FilterExpression: "logCompanyId = :logCompanyId and logId = :logId",
        ExpressionAttributeValues: {
          ":logCompanyId": permissions.user_company_id,
          ":logId": event.pathParameters.id
        }
      };

      const result = await dynamoDbLib.call("scan", params);

      callback(null, success(result.Items[0]));

    } else {

      callback(null, failure({
        status: 'you do not have access to this api call'
      }));

    }

  } catch (e) {
    callback(null, failure({
      status: e
    }));
  }
}
