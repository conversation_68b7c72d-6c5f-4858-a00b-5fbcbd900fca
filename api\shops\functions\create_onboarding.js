import uuid from "uuid";
import * as dynamoDbLib from "../../../libs/dynamodb-lib";
import * as bugsnagClient from "../../../libs/bugsnag";

import {
  success,
  failure
} from "../../../libs/response-lib";

import {
  is_level_permitted
} from "../../../libs/permissions";

import nodemailer from "nodemailer"
const AWS = require('aws-sdk')
const cognito_client = new AWS.CognitoIdentityServiceProvider()

export async function shop(event, context, callback) {


  try {

    const access_scope = ['owner', 'manager', 'employee']

    const data = JSON.parse(event.body);
    let pool_id = process.env.COGNITO_POOL_ID
    var user_id = event.requestContext.identity.cognitoAuthenticationProvider.split('CognitoSignIn:')[1]


    const permissions = await is_level_permitted(user_id, access_scope);
    if (permissions.level_allowed) {

      let shop_id = uuid.v1()

      await dynamoDbLib.call("put", {
        TableName: process.env.objectsTableName,
        Item: {
          objectCreatedBy: user_id,
          objectCompanyId: permissions.user_company_id,
          objectId: shop_id,
          objectName: data.shop_name,
          //objectPaymentMethod: data.payment,
          objectPaymentMethod: 'manual',
          objectStripeIntegration: null,
          deliveryFrequency: (data.order_schedule) ? data.order_schedule : 'daily',
          orderDates: (data.orderDates) ? data.orderDates : [0, 1, 2, 3, 4, 5, 6],
          shopCurrency: (data.shopCurrency) ? data.shopCurrency : 'NOK',
          shippingCost: (data.shipping_cost) ? data.shipping_cost : 0,
          mainBackgroundColor: data.mainBackgroundColor ? data.mainBackgroundColor : '#f8f8f8',
          mainDarkBackground: data.mainDarkBackground ? data.mainDarkBackground : '#28a745',
          mainDarkColor: data.mainDarkColor ? data.mainDarkColor : '#ffffff',
          mainTextColor: data.mainTextColor ? data.mainTextColor : '#0000000',
          secondaryTextColor: data.secondaryTextColor ? data.secondaryTextColor : '#000000',
          objectLogo: data.updated_image ? data.updated_image : null,
          objectIcon: data.updated_icon ? data.updated_icon : null,
          landingPageIsActive: (data.landing == 'yes') ? false : false,
          objectType: "shop",
          createdAt: new Date().getTime()
        }
      });


      let cat_id = uuid.v1()

      let category = await dynamoDbLib.call("put", {
        TableName: process.env.objectsTableName,
        Item: {
          objectCreatedBy: user_id,
          objectCompanyId: permissions.user_company_id,
          objectId: cat_id,
          objectName: 'Sample Category',
          objectVisibility: true,
          objectShopId: shop_id,
          objectType: "category",
          createdAt: new Date().getTime()
        }
      });

      let products = [{
          name: 'FUNGI KREM TIL TACO',
          desc: 'Enkel markedsføring og salg på nett gir nye mulighet for vekst.',
          img: 'https://images.squarespace-cdn.com/content/v1/5cd049b9809d8e3e3b83426d/1571329322380-57MHSBYCY7YAAS3PSQVK/ke17ZwdGBToddI8pDm48kK60W-ob1oA2Fm-j4E_9NQB7gQa3H78H3Y0txjaiv_0fDoOvxcdMmMKkDsyUqMSsMWxHk725yiiHCCLfrh8O1z4YTzHvnKhyp6Da-NYroOW3ZGjoBKy3azqku80C789l0kD6Ec8Uq9YczfrzwR7e2Mh5VMMOxnTbph8FXiclivDQnof69TlCeE0rAhj6HUpXkw/IMG_7618.jpg'
        },
        {
          name: 'FUNGI SYLTET RØDLØK',
          desc: 'La kundene dine velge produkter, få individuelle rabatter eller fraktrater og administrere ordre i egen tid.',
          img: 'https://images.squarespace-cdn.com/content/v1/5cd049b9809d8e3e3b83426d/1570635295642-1HEC73ADPSHVJ5S0VZIF/ke17ZwdGBToddI8pDm48kK60W-ob1oA2Fm-j4E_9NQB7gQa3H78H3Y0txjaiv_0fDoOvxcdMmMKkDsyUqMSsMWxHk725yiiHCCLfrh8O1z4YTzHvnKhyp6Da-NYroOW3ZGjoBKy3azqku80C789l0kD6Ec8Uq9YczfrzwR7e2Mh5VMMOxnTbph8FXiclivDQnof69TlCeE0rAhj6HUpXkw/IMG_2889.JPG'
        },
        {
          name: 'VERDENS BESTE PASTASAUS',
          desc: 'Ordrene samles i en produksjonsrapport, du kan printe fraktsedler fra Kompis og pakke varene.',
          img: 'https://images.squarespace-cdn.com/content/v1/5cd049b9809d8e3e3b83426d/1566834293293-BTH2F7G47K752MRFJ9X1/ke17ZwdGBToddI8pDm48kK60W-ob1oA2Fm-j4E_9NQB7gQa3H78H3Y0txjaiv_0fDoOvxcdMmMKkDsyUqMSsMWxHk725yiiHCCLfrh8O1z4YTzHvnKhyp6Da-NYroOW3ZGjoBKy3azqku80C789l0kD6Ec8Uq9YczfrzwR7e2Mh5VMMOxnTbph8FXiclivDQnof69TlCeE0rAhj6HUpXkw/verdens_enkleste_saus.jpg'
        },
        {
          name: 'SUSHI BOWL',
          desc: 'Alle ordre sendes til regnskap i PowerOffice Go automatisk!',
          img: 'https://images.squarespace-cdn.com/content/v1/5cd049b9809d8e3e3b83426d/1570627312151-2PZVFNE9O2QAOXIPVUTN/ke17ZwdGBToddI8pDm48kGeNtXjnSVE0FC98ofHjoQx7gQa3H78H3Y0txjaiv_0fDoOvxcdMmMKkDsyUqMSsMWxHk725yiiHCCLfrh8O1z5QPOohDIaIeljMHgDF5CVlOqpeNLcJ80NK65_fV7S1UVURxIstYj4VcQ3u4G8psS8p19-2HRX2fy4mVTF2-S9JlPRhrjbf-ufqwsSWgrw9rg/st%C3%A6mning.bolws+4+kopia.jpg'
        }
      ]


      for (var x in products) {
        let product_id = uuid.v1()
        await dynamoDbLib.call("put", {
          TableName: process.env.objectsTableName,
          Item: {
            objectCreatedBy: user_id,
            objectCompanyId: permissions.user_company_id,
            objectId: product_id,
            objectName: products[x].name,
            objectShopId: shop_id,
            objectVisibility: true,
            objectPrice: 30,
            objectCost: 10,
            objectMVA: 15,
            objectMinimumOrderQuantity: 1,
            objectMaximumOrderQuantity: 2,
            objectMaximumDiscount: 10,
            objectDesc: products[x].desc,
            objectLocation: null,
            objectImage: products[x].img,
            objectCategory: cat_id,
            objectType: "product",
            createdAt: new Date().getTime()
          }
        });
      }


      let transporter = nodemailer.createTransport({
        host: 'email-smtp.eu-west-1.amazonaws.com',
        port: 465,
        secure: true,
        auth: {
          user: 'AKIAJLILB3DMXPRVD4AA',
          pass: 'Amg0o1jLDdfBLT++mU1G0xw99F+cCjVKFg4DYMlQOppz'
        }
      });

      let from_email = '<EMAIL>'
      let mailOptions = {}


      let html = 'Hei<br /><br />'

      // domain
      if (data.domain) {
        html += '<b>Domain config</b><br />Du har formidlet at du ønsker å legge til ditt eget domene for butikken din.<br />Det fikser vi enkelt. Da går du <a href="https://kompis.app/app/company-dashboard/shop-management/' + shop_id + '">hit</a> og velger “Konfigurer egendefinert domene”;<br /><br />Deretter skriver du inn ditt domenenavn f.eks. <a href="http://www.kompisbutikk.no">www.kompisbutikk.no</a><br />Deretter kopierer du CNAME informasjonen og lager en CNAME post hos din domenvert som hoster ditt domene i henhold til både Steg 1 og Steg 2 .<br /><br />Så kan du lagre innstillingene i Kompis og hos domenevert og domenet ditt går live i løpet av et døgn, avhengig av domeneverts innstillinger og TTL.<br /><br />'
      }

      //pogo
      if (data.pogo) {
        html += '<b>POGO config</b><br />Du har formidlet at du ønsker å legge til PowerOffice regnskapsintegrasjon for butikken din.<br /><br />Da går du <a href="https://kompis.app/app/company-dashboard/integrations">hit</a> og trykker “+” for å legge til en ny integrasjon og velger PowerOffice go.<br />Gi integrasjonen et navn som “PowerOffice - “Kompis Butikk” og legg til ditt organisasjonsnummer.<br />Trykk så lagre og følg guiden til PowerOffice på skjermen, gi PowerOffice tilgang.<br /><br />Deretter sjekker du at regnskapskonti stemmer med dine MVA innstillinger og du er live!<br /><br />Har du ikke PowerOffice Go og trenger regnskapsprogram? Gå til <a href="http://www.poweroffice.no">www.poweroffice.no</a> for å registrere en konto.<br /><br />'
      }
      // stripe
      if (data.stripe) {
        html += '<b>Stripe config</b><br />Du har formidlet at du ønsker å legge til Stripe betalingsløsning for kortbetalinger i butikken din. <br /><br />Da går du <a href="https://kompis.app/app/company-dashboard/integrations">hit</a> og trykker “+” for å legge til en ny integrasjon og velger Stripe.<br />Gi integrasjonen et navn som “Stripe - “Kompis Butikk”.<br />Så legger du til Stripe “publishable key” og “secret key” som finnes tilgjengelig på din Stripe profil. Trykk så lagre og du er live!<br /><br />Har du ikke en Stripe konto? Gå til <a href="http://www.stripe.com">www.stripe.com</a> for å registrere en konto gratis.'
      }
      mailOptions = {
        from: from_email,
        to: permissions.user_email,
        subject: 'La oss hjelpe deg med Kompis oppsett',
        html: html
      };

      if (data.stripe || data.pogo || data.domain) {
        await transporter.sendMail(mailOptions).then(function(info) {

        }).catch(function(err) {
          bugsnagClient.notify(user_id, err)
          callback(null, failure({
            status: err
          }));
        });
      }

      callback(null, success(shop_id));

    } else {

      callback(null, failure({
        status: 'you do not have access to this api call'
      }));

    }

  } catch (e) {
    bugsnagClient.notify(user_id, e)
    callback(null, failure({
      status: e
    }));
  }

}