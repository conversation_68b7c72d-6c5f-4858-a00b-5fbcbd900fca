import * as dynamoDbLib from "../../../libs/dynamodb-lib";
import * as bugsnagClient from "../../../libs/bugsnag";

import {
  success,
  failure
} from "../../../libs/response-lib";

import PowerOffice from "../../../libs/poweroffice";

import {
  is_level_permitted
} from "../../../libs/permissions";

var arraySort = require('array-sort')

export async function accounts(event, context, callback) {

  try {

    const access_scope = ['owner', 'manager', 'employee']
    var user_id = event.requestContext.identity.cognitoAuthenticationProvider.split('CognitoSignIn:')[1]
    const data = JSON.parse(event.body);
    const permissions = await is_level_permitted(user_id, access_scope);
    if (permissions.level_allowed) {


      let client_secret = data.client_secret

      let poweroffice = await new PowerOffice(process.env.POWEROFFICE_APP_KEY, client_secret)

      let accounts = await poweroffice.getGeneralLedgerAccounts()
      let vat_codes = await poweroffice.getVatCodes()

      callback(null, success({
        accounts,
        vat_codes
      }));

    } else {

      callback(null, failure({
        status: 'you do not have access to this api call'
      }));

    }

  } catch (e) {
    bugsnagClient.notify(user_id, e)
    callback(null, failure({
      status: e
    }));
  }
}