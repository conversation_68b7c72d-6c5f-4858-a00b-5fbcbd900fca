import * as dynamoDbLib from "../../../libs/dynamodb-lib";
import * as bugsnagClient from "../../../libs/bugsnag";
import {
  success,
  failure
} from "../../../libs/response-lib";

import {
  is_level_permitted
} from "../../../libs/permissions";

export async function access(event, context, callback) {

  try {

    const access_scope = ['owner', 'manager', 'employee']
    var user_id = event.requestContext.identity.cognitoAuthenticationProvider.split('CognitoSignIn:')[1]

    const permissions = await is_level_permitted(user_id, access_scope);
    if (permissions.level_allowed) {

      let shop_id = event.pathParameters.id


      const params = {
        TableName: process.env.userPermissionsTableName,
        FilterExpression: "shopId = :shopId and roleType = :roleType and isActive = :isActive and companyId = :companyId",
        ExpressionAttributeValues: {
          ":shopId": shop_id,
          ":roleType": 'shop_access_role',
          ":isActive": true,
          ":companyId": permissions.user_company_id
        }
      };

      const result = await dynamoDbLib.call("scan", params);

      callback(null, success(result.Items))


    } else {

      callback(null, failure({
        status: 'you do not have access to this api call'
      }));

    }

  } catch (e) {
    bugsnagClient.notify(user_id, e)
    callback(null, failure({
      status: e
    }));
  }

}