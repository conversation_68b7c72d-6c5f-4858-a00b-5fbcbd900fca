import {
  success,
  failure
} from "../../../libs/response-lib";
import * as bugsnagClient from "../../../libs/bugsnag";
import {
  is_level_permitted
} from "../../../libs/permissions";


const AWS = require('aws-sdk')
const cognito_client = new AWS.CognitoIdentityServiceProvider()

export async function users(event, context, callback) {

  try {

    let user_id = event.requestContext.identity.cognitoAuthenticationProvider.split('CognitoSignIn:')[1]


    let pool_id = process.env.COGNITO_POOL_ID

    var params = {
      UserPoolId: pool_id,
      Username: user_id,
      Limit: 0,
    };

    let users_data = await cognito_client.adminListDevices(params).promise()

    callback(null, success({
      users_data
    }))


  } catch (e) {
    bugsnagClient.notify(user_id, e)
    callback(null, failure({
      status: e
    }));
  }

}