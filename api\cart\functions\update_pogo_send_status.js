import * as dynamoDbLib from "../../../libs/dynamodb-lib";
import * as bugsnagClient from "../../../libs/bugsnag";

import {
  success,
  failure
} from "../../../libs/response-lib";

import {
  is_level_permitted
} from "../../../libs/permissions";

import {
  log_event
} from "../../../libs/logs";


export async function order(event, context, callback) {

  try {

    const access_scope = ['owner', 'manager', 'employee']

    const data = JSON.parse(event.body);

    var user_id = event.requestContext.identity.cognitoAuthenticationProvider.split('CognitoSignIn:')[1]

    const permissions = await is_level_permitted(user_id, access_scope);

    if (permissions.level_allowed) {

      let cart_id = event.pathParameters.id

      const order = await dynamoDbLib.call("scan", {
        TableName: process.env.cartTableName,
        FilterExpression: "cartId = :cartId",
        ExpressionAttributeValues: {
          ":cartId": cart_id
          //":companyId": permissions.user_company_id
        }
      });

      const result = await dynamoDbLib.call("update", {
        TableName: process.env.cartTableName,

        Key: {
          cartId: cart_id
        },

        UpdateExpression: "SET poweroffice_manual_send = :poweroffice_manual_send, sentManualPogoAt = :sentManualPogoAt, sentManualPogoBy = :sentManualPogoBy",
        ExpressionAttributeValues: {
          ":poweroffice_manual_send": true,
          ":sentManualPogoBy": user_id,
          ":sentManualPogoAt": new Date().getTime(),
        },
        ReturnValues: "ALL_NEW"
      });


      const shopInfo = await dynamoDbLib.call("scan", {
        TableName: process.env.objectsTableName,
        FilterExpression: "objectType = :objectType and objectId = :objectId",
        ExpressionAttributeValues: {
          ":objectType": 'shop',
          ":objectId": order.Items[0].shopId
        }
      });

      await log_event(
        user_id,
        permissions.user_full_name,
        permissions.user_company_name,
        shopInfo.Items[0].objectCompanyId,
        "order_sent_manual_pogo", {
          cartId: cart_id
        },
        order.Items[0].shopId,
        shopInfo.Items[0].objectName,
      )


      callback(null, success('done'));

    } else {

      callback(null, failure({
        status: 'you do not have access to this api call'
      }));

    }
  } catch (e) {
    bugsnagClient.notify(user_id, e)
    callback(null, failure({
      status: e
    }));
  }
}