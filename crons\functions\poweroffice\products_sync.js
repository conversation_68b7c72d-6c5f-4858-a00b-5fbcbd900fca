import {
  success,
  failure
} from "../../../libs/response-lib";

import {
  is_level_permitted
} from "../../../libs/permissions";
import * as bugsnagClient from "../../../libs/bugsnag";
import * as dynamoDbLib from "../../../libs/dynamodb-lib";

import PowerOffice from "../../../libs/poweroffice";

const AWS = require('aws-sdk')
const cognito_client = new AWS.CognitoIdentityServiceProvider()


export async function init(event, context, callback) {

  try {


    let product_id = event.Records[0].Sns.Message

    const productParams = {
      TableName: process.env.objectsTableName,
      FilterExpression: "objectId = :objectId",
      ExpressionAttributeValues: {
        ":objectId": product_id
      }
    };

    const products = await dynamoDbLib.call("scan", productParams);
    const shop_id = products.Items[0].objectShopId
    const category_id = products.Items[0].objectCategory


    const shopsSarams = {
      TableName: process.env.objectsTableName,
      FilterExpression: "objectType = :objectType and objectId = :objectId",
      ExpressionAttributeValues: {
        ":objectType": 'shop',
        ":objectId": shop_id
      }
    };

    const shops = await dynamoDbLib.call("scan", shopsSarams);


    if (shops.Items[0].objectPowerOfficeIntegration) {

      let company_id = shops.Items[0].objectCompanyId


      const integrationParams = {
        TableName: process.env.integrationsTableName,
        FilterExpression: "integrationId = :integrationId",
        ExpressionAttributeValues: {
          ":integrationId": shops.Items[0].objectPowerOfficeIntegration
        }
      };

      const integrations = await dynamoDbLib.call("scan", integrationParams);
      let poweroffice_client_key = integrations.Items[0].integrationPOAppKey
      let poweroffice_secrect_key = integrations.Items[0].integrationPOClientKey
      let poweroffice_0_mva_account = integrations.Items[0].integrationPO0MVA
      let poweroffice_15_mva_account = integrations.Items[0].integrationPO15MVA
      let poweroffice_25_mva_account = integrations.Items[0].integrationPO25MVA
      let migration_start_date = integrations.Items[0].createdAt

      let poweroffice = await new PowerOffice(process.env.POWEROFFICE_APP_KEY, poweroffice_secrect_key)


      const categoryParams = {
        TableName: process.env.objectsTableName,
        FilterExpression: "objectId = :objectId",
        ExpressionAttributeValues: {
          ":objectId": category_id
        }
      };

      const categories = await dynamoDbLib.call("scan", categoryParams);

      for (var x in products.Items) {

        let product_mva = (products.Items[x].objectMVA) ? products.Items[x].objectMVA : 0
        let product_mva_account = null
        if (product_mva == 0) {
          product_mva_account = poweroffice_0_mva_account
        }
        if (product_mva == 15) {
          product_mva_account = poweroffice_15_mva_account
        }
        if (product_mva == 25) {
          product_mva_account = poweroffice_25_mva_account
        }

        let group_id = null
        let category_name = ''
        for (var y in categories.Items) {
          if (categories.Items[y].objectId == products.Items[x].objectCategory) {
            group_id = categories.Items[y].powerOfficeCategoryId
            category_name = categories.Items[y].objectName

          }

        }

        if (products.Items[x].powerOfficeProductId) {
          // product already pushed to poweroffice

          let product = await poweroffice.updateProduct({
            id: products.Items[x].powerOfficeProductId,
            description: products.Items[x].objectDesc,
            isActive: true,
            name: products.Items[x].objectName + ' - ' + category_name,
            productGroupId: group_id,
            salesPrice: products.Items[x].objectPrice,
            costPrice: (products.Items[x].objectCost) ? products.Items[x].objectCost : null,
            salesAccount: product_mva_account
          })

          const result = await dynamoDbLib.call("update", {
            TableName: process.env.objectsTableName,

            Key: {
              objectCompanyId: company_id,
              objectId: products.Items[x].objectId
            },

            UpdateExpression: "SET powerOfficeProductId = :powerOfficeProductId,powerOfficeProductCode = :powerOfficeProductCode",
            ExpressionAttributeValues: {
              ":powerOfficeProductId": product.data.data.id,
              ":powerOfficeProductCode": product.data.data.code,
            },
            ReturnValues: "ALL_NEW"
          });


        } else {

          let product = await poweroffice.createProduct({
            description: products.Items[x].objectDesc,
            isActive: true,
            name: products.Items[x].objectName + ' - ' + category_name,
            productGroupId: group_id,
            salesPrice: products.Items[x].objectPrice,
            costPrice: (products.Items[x].objectCost) ? products.Items[x].objectCost : null,
            salesAccount: product_mva_account
          })

          const result = await dynamoDbLib.call("update", {
            TableName: process.env.objectsTableName,

            Key: {
              objectCompanyId: company_id,
              objectId: products.Items[x].objectId
            },

            UpdateExpression: "SET powerOfficeProductId = :powerOfficeProductId,powerOfficeProductCode = :powerOfficeProductCode",
            ExpressionAttributeValues: {
              ":powerOfficeProductId": product.data.data.id,
              ":powerOfficeProductCode": product.data.data.code,
            },
            ReturnValues: "ALL_NEW"
          });

        }

      }
    }


    callback(null, success(true))

  } catch (e) {
    //  bugsnagClient.notify(user_id, e)
    console.log(e);
    callback(null, failure({
      error: e
    }))
  }

}