import {
  success,
  failure
} from "../../../libs/response-lib";

import {
  is_level_permitted
} from "../../../libs/permissions";
import * as bugsnagClient from "../../../libs/bugsnag";
import * as dynamoDbLib from "../../../libs/dynamodb-lib";

import PowerOffice from "../../../libs/poweroffice";

const AWS = require('aws-sdk')
const cognito_client = new AWS.CognitoIdentityServiceProvider()


export async function init(event, context, callback) {

  try {

    let shop_id = event.Records[0].Sns.Message

    const shopsSarams = {
      TableName: process.env.objectsTableName,
      FilterExpression: "objectType = :objectType and objectId = :objectId",
      ExpressionAttributeValues: {
        ":objectType": 'shop',
        ":objectId": shop_id
      }
    };

    const shops = await dynamoDbLib.call("scan", shopsSarams);


    if (shops.Items[0].objectPowerOfficeIntegration) {

      let company_id = shops.Items[0].objectCompanyId
      let migration_mode = shops.Items[0].objectPowerOfficeMigrationMode


      const integrationParams = {
        TableName: process.env.integrationsTableName,
        FilterExpression: "integrationId = :integrationId",
        ExpressionAttributeValues: {
          ":integrationId": shops.Items[0].objectPowerOfficeIntegration
        }
      };

      const integrations = await dynamoDbLib.call("scan", integrationParams);
      let poweroffice_client_key = integrations.Items[0].integrationPOAppKey
      let poweroffice_secrect_key = integrations.Items[0].integrationPOClientKey
      let poweroffice_0_mva_account = integrations.Items[0].integrationPO0MVA
      let poweroffice_15_mva_account = integrations.Items[0].integrationPO15MVA
      let poweroffice_25_mva_account = integrations.Items[0].integrationPO25MVA
      let migration_start_date = integrations.Items[0].createdAt

      let poweroffice = await new PowerOffice(process.env.POWEROFFICE_APP_KEY, poweroffice_secrect_key)



      let shipping_product_id = null
      let shipping_product_code = null

      if (shops.Items[0].objectPowerOfficeShippingProductId) {

        shipping_product_id = shops.Items[0].objectPowerOfficeShippingProductId
        shipping_product_code = shops.Items[0].objectPowerOfficeShippingProductCode

        await poweroffice.updateProduct({
          id: shops.Items[0].objectPowerOfficeShippingProductId,
          description: 'Shipping Cost',
          isActive: true,
          name: 'Shipping Cost',
          //  productGroupId: product_group_id,
          salesPrice: (shops.Items[0].shippingCost) ? shops.Items[0].shippingCost : 0,
          salesAccount: poweroffice_25_mva_account
        })

      } else {

        let product = await poweroffice.createProduct({
          description: 'Shipping Cost',
          isActive: true,
          name: 'Shipping Cost',
          //    productGroupId: product_group_id,
          salesPrice: (shops.Items[0].shippingCost) ? shops.Items[0].shippingCost : 0,
          salesAccount: poweroffice_25_mva_account
        })


        shipping_product_id = product.data.data.id
        shipping_product_code = product.data.data.code

        const result = await dynamoDbLib.call("update", {
          TableName: process.env.objectsTableName,

          Key: {
            objectCompanyId: company_id,
            objectId: shop_id
          },

          UpdateExpression: "SET objectPowerOfficeShippingProductId = :objectPowerOfficeShippingProductId,objectPowerOfficeShippingProductCode = :objectPowerOfficeShippingProductCode",
          ExpressionAttributeValues: {
            ":objectPowerOfficeShippingProductId": product.data.data.id,
            ":objectPowerOfficeShippingProductCode": product.data.data.code,
          },
          ReturnValues: "ALL_NEW"
        });

      }

    }


    callback(null, success(true))

  } catch (e) {
    //  bugsnagClient.notify(user_id, e)
    console.log(e);
    callback(null, failure({
      error: e
    }))
  }

}