service: kompis-customers

plugins:
  - serverless-webpack

custom:
  webpackIncludeModules: true
  stage: ${opt:stage, self:provider.stage}
  stageSufix:
    prod: ""
    dev: -dev
  congnito_pool_id:
    dev: eu-central-1_o36r1nMGj
    prod: eu-central-1_E1nLfop0C

  poweroffice_auth_url:
    dev: https://api-demo.poweroffice.net/OAuth/Token
    prod: https://api.poweroffice.net/OAuth/Token
  poweroffice_api_url:
    dev: https://api-demo.poweroffice.net
    prod: https://api.poweroffice.net

  poweroffice_app_key:
    dev: 0fabc9dc-5477-4459-bd0c-7bc19a02caf3
    prod: 4ace3064-f987-48f1-9111-c96c896f36c6

provider:
  versionFunctions: false
  name: aws
  endpointType: REGIONAL
  runtime: nodejs16.x
  stage: dev
  region: eu-central-1
  environment:
    COGNITO_POOL_ID: ${self:custom.congnito_pool_id.${self:provider.stage}}
    POWEROFFICE_AUTH_URL: ${self:custom.poweroffice_auth_url.${self:provider.stage}}
    POWEROFFICE_API_URL: ${self:custom.poweroffice_api_url.${self:provider.stage}}
    POWEROFFICE_APP_KEY: ${self:custom.poweroffice_app_key.${self:provider.stage}}
    generateCompaniesTasksSNSTopic: arn:aws:sns:eu-central-1:589634798762:generate_companies_tasks${self:custom.stageSufix.${self:custom.stage}}
    cartTableName: cart${self:custom.stageSufix.${self:custom.stage}}
    exceptionsTableName: exceptions${self:custom.stageSufix.${self:custom.stage}}
    objectsTableName: objects${self:custom.stageSufix.${self:custom.stage}}
    sensorsDataLogsTableName: sensorsDataLogs${self:custom.stageSufix.${self:custom.stage}}
    tasksTableName: tasks${self:custom.stageSufix.${self:custom.stage}}
    tasksLogTableName: tasksLog${self:custom.stageSufix.${self:custom.stage}}
    tasksSchedulesTableName: tasksSchedules${self:custom.stageSufix.${self:custom.stage}}
    userPermissionsTableName: userPermissions${self:custom.stageSufix.${self:custom.stage}}
    integrationsTableName: integrations${self:custom.stageSufix.${self:custom.stage}}
    logsTableName: logs${self:custom.stageSufix.${self:custom.stage}}
    generatePowerOfficeCustomersSNSTopic: arn:aws:sns:eu-central-1:589634798762:generate_poweroffice_customers${self:custom.stageSufix.${self:custom.stage}}

  iamRoleStatements:
    - Effect: "Allow"
      Action:
        - "SNS:Publish"
      Resource: "arn:aws:sns:eu-central-1:589634798762:generate_companies_tasks${self:custom.stageSufix.${self:custom.stage}}"

    - Effect: "Allow"
      Action:
        - "SNS:Publish"
      Resource: "arn:aws:sns:eu-central-1:589634798762:generate_poweroffice_customers${self:custom.stageSufix.${self:custom.stage}}"

    - Effect: "Allow"
      Action:
        - "cognito-idp:*"
      Resource: "arn:aws:cognito-idp:eu-central-1:*:userpool/*"

    - Effect: Allow
      Action:
        - dynamodb:Query
        - dynamodb:Scan
        - dynamodb:GetItem
        - dynamodb:PutItem
        - dynamodb:UpdateItem
        - dynamodb:DeleteItem
      Resource: "arn:aws:dynamodb:eu-central-1:*:*"

functions:
  createCustomer:
    handler: functions/create.customer
    events:
      - http:
          path: /
          method: post
          cors: true
          authorizer: aws_iam

  registerCustomer:
    handler: functions/register.customer
    events:
      - http:
          path: /register/
          method: post
          cors: true

  setCustomerContact:
    handler: functions/set_customer_contact.customer
    events:
      - http:
          path: /contact/
          method: post
          cors: true
          authorizer: aws_iam

  updateCustomer:
    handler: functions/update.customer
    events:
      - http:
          path: /
          method: put
          cors: true
          authorizer: aws_iam

  deleteCustomerRelation:
    handler: functions/delete.customer
    events:
      - http:
          path: /{id}
          method: delete
          cors: true
          authorizer: aws_iam

  listCustomers:
    handler: functions/list.customers
    timeout: 300
    events:
      - http:
          path: /{id}
          method: get
          cors: true
          authorizer: aws_iam

  listPowerOfficeCustomers:
    handler: functions/list_poweroffice_customers.customers
    events:
      - http:
          path: /poweroffice/{id}
          method: get
          cors: true
          authorizer: aws_iam

  checkCustomer:
    handler: functions/check.customer
    events:
      - http:
          path: /check/
          method: post
          cors: true
          authorizer: aws_iam
