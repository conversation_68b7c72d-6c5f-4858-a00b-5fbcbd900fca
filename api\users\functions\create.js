import {
  success,
  failure
} from "../../../libs/response-lib";

import {
  is_level_permitted
} from "../../../libs/permissions";
import * as bugsnagClient from "../../../libs/bugsnag";

const AWS = require('aws-sdk')
const cognito_client = new AWS.CognitoIdentityServiceProvider()

export async function user(event, context, callback) {


  try {

    const data = JSON.parse(event.body);


    let access_scope = []

    if (data.user_type == 'manager') {
      access_scope = ['owner', 'manager']
    } else if (data.user_type == 'employee') {
      access_scope = ['owner', 'manager']

    } else {

      access_scope = ['owner']
    }

    var user_id = event.requestContext.identity.cognitoAuthenticationProvider.split('CognitoSignIn:')[1]
    //let user_id = '90117aa4-3e8a-495e-b6c6-b2238281fedf'
    var pool_id = process.env.COGNITO_POOL_ID


    const permissions = await is_level_permitted(user_id, access_scope);

    if (permissions.level_allowed) {

      var params = {
        UserPoolId: pool_id,
        Username: data.email,
        //DesiredDeliveryMediums: ['EMAIL'],
        //  MessageAction: 'SUPPRESS',
        TemporaryPassword: makeid(8),
        ForceAliasCreation: false,
        UserAttributes: [{
            Name: 'custom:first_name',
            Value: data.first_name
          },
          {
            Name: 'custom:last_name',
            Value: data.last_name
          },
          {
            Name: 'custom:user_phone',
            Value: data.phone
          },
          {
            Name: 'custom:user_type',
            Value: data.user_type
          },
          {
            Name: 'name',
            Value: (data.company_id) ? data.company_id : permissions.user_company_id
          },
          {
            Name: 'custom:company_id',
            Value: (data.company_id) ? data.company_id : permissions.user_company_id
          },

          {
            Name: 'email',
            Value: data.email
          },
          {
            Name: 'custom:created_as',
            Value: data.created_as
          },
          {
            Name: 'email_verified',
            Value: 'true'
          },
          {
            Name: 'custom:language',
            Value: 'no'
          }
        ]
      };

      let new_user = await cognito_client.adminCreateUser(params).promise()

      callback(null, success(new_user))


    } else {

      callback(null, failure({
        status: 'you do not have access to this api call'
      }));

    }

  } catch (e) {
    bugsnagClient.notify(user_id, e)
    callback(null, failure({
      error: e
    }))
  }

}

function makeid(length) {
  var result = '';
  var characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  var charactersLength = characters.length;
  for (var i = 0; i < length; i++) {
    result += characters.charAt(Math.floor(Math.random() * charactersLength));
  }
  return result;
}