import * as dynamoDbLib from "../../../libs/dynamodb-lib";
import * as bugsnagClient from "../../../libs/bugsnag";

import {
  success,
  failure
} from "../../../libs/response-lib";

import {
  is_level_permitted
} from "../../../libs/permissions";

const AWS = require('aws-sdk')
const SNS = new AWS.SNS();

export async function order(event, context, callback) {

  try {

    const access_scope = ['owner', 'manager', 'employee']

    const data = JSON.parse(event.body);

    var user_id = event.requestContext.identity.cognitoAuthenticationProvider.split('CognitoSignIn:')[1]

    const permissions = await is_level_permitted(user_id, access_scope);

    if (permissions.level_allowed) {

      let shop_id = event.pathParameters.id

      await SNS.publish({
        Message: shop_id,
        TopicArn: process.env.generatePowerOfficeOrdersSNSTopic
      }, function(err, data) {
        //console.log(data);
        if (err) {
          callback(null, err);
        }
        // sns sent. good job
      });


      callback(null, success('done'));

    } else {

      callback(null, failure({
        status: 'you do not have access to this api call'
      }));

    }
  } catch (e) {
    bugsnagClient.notify(user_id, e)
    callback(null, failure({
      status: e
    }));
  }
}