!function(e,t){for(var r in t)e[r]=t[r]}(exports,function(e){var t={};function r(n){if(t[n])return t[n].exports;var o=t[n]={i:n,l:!1,exports:{}};return e[n].call(o.exports,o,o.exports,r),o.l=!0,o.exports}return r.m=e,r.c=t,r.d=function(e,t,n){r.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:n})},r.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},r.t=function(e,t){if(1&t&&(e=r(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var n=Object.create(null);if(r.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var o in e)r.d(n,o,function(t){return e[t]}.bind(null,o));return n},r.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return r.d(t,"a",t),t},r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r.p="",r(r.s=2)}([function(e,t){e.exports=require("aws-sdk")},function(e,t){e.exports=require("@bugsnag/js")},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.user=void 0;var n=u(r(3)),o=u(r(4)),s=(t.user=function(){var e=(0,o.default)(n.default.mark(function e(t,r,o){var u,c;return n.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,u={UserAttributes:[{Name:"name",Value:"32d5e460-f027-11e9-ac4e-112968b6935c"},{Name:"custom:company_id",Value:"32d5e460-f027-11e9-ac4e-112968b6935c"}],UserPoolId:process.env.COGNITO_POOL_ID,Username:"52de05a4-b2ca-4705-b43f-ed252dbf27ee"},e.next=4,i.adminUpdateUserAttributes(u).promise();case 4:c=e.sent,o(null,(0,s.success)({users_data:c})),e.next=13;break;case 8:e.prev=8,e.t0=e.catch(0),console.log(e.t0),a.notify("",e.t0),o(null,(0,s.failure)({status:e.t0}));case 13:case"end":return e.stop()}},e,this,[[0,8]])}));return function(t,r,n){return e.apply(this,arguments)}}(),r(5)),a=function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t.default=e,t}(r(6));r(7);function u(e){return e&&e.__esModule?e:{default:e}}var i=new(r(0).CognitoIdentityServiceProvider)},function(e,t){e.exports=require("babel-runtime/regenerator")},function(e,t){e.exports=require("babel-runtime/helpers/asyncToGenerator")},function(e,t,r){"use strict";function n(e){return s(200,e)}function o(e){return s(500,e)}function s(e,t){return{statusCode:e,headers:{"Access-Control-Allow-Origin":"*","Access-Control-Allow-Credentials":!0},body:JSON.stringify(t)}}r.r(t),r.d(t,"success",function(){return n}),r.d(t,"failure",function(){return o})},function(e,t,r){"use strict";r.r(t),r.d(t,"notify",function(){return s});var n=r(1);const o=r.n(n)()({apiKey:"fd490dc0489374329842ddd3b0b568d7",releaseStage:"dev",appType:"backend",appVersion:"1.00"});function s(e=null,t){o.user={id:e},o.notify(t)}},function(e,t,r){"use strict";r.r(t);var n=r(0),o=r.n(n);o.a.config.update({region:"eu-central-1"});const s=new o.a.DynamoDB.DocumentClient;async function a(e,t){if("scan"==e){let e={Items:[]},n=await s.scan(t).promise();for(e.Items=n.Items;void 0!==n.LastEvaluatedKey;)for(var r in t.ExclusiveStartKey=n.LastEvaluatedKey,(n=await s.scan(t).promise()).Items)e.Items.push(n.Items[r]);return e}return s[e](t).promise()}function u(e,t){return new Promise(function(r,n){let s=new o.a.CognitoIdentityServiceProvider,u=(process.env.COGNITO_POOL_ID,""),i="",c="",l="",d="",f={UserPoolId:process.env.COGNITO_POOL_ID,Username:e};s.adminGetUser(f,async function(e,o){if(e)n(e);else{for(var s in o.UserAttributes)"custom:company_id"==o.UserAttributes[s].Name&&(u=o.UserAttributes[s].Value),"custom:user_type"==o.UserAttributes[s].Name&&(i=o.UserAttributes[s].Value),"custom:first_name"==o.UserAttributes[s].Name&&(l=o.UserAttributes[s].Value),"custom:last_name"==o.UserAttributes[s].Name&&(d=o.UserAttributes[s].Value),"email"==o.UserAttributes[s].Name&&(c=o.UserAttributes[s].Value);const e=await a("scan",{TableName:process.env.objectsTableName,FilterExpression:"objectType = :objectType and objectId = :objectId",ExpressionAttributeValues:{":objectType":"company",":objectId":u}});"company"==i||"owner"==i?r({level_allowed:!0,location_allowed:!0,user_company_id:u,user_company_name:e.Items[0].objectName?e.Items[0].objectName:null,user_type:i,user_email:c,user_full_name:l+" "+d}):-1!=t.indexOf(i)?r({level_allowed:!0,location_allowed:!1,user_company_id:u,user_company_name:e.Items[0].objectName?e.Items[0].objectName:null,user_type:i,user_full_name:l+" "+d}):r({level_allowed:!1,location_allowed:!1,user_company_id:u,user_company_name:e.Items[0].objectName?e.Items[0].objectName:null,user_type:i,user_full_name:l+" "+d})}})})}function i(e,t,r,n=null){return new Promise(async function(o,s){"company"!=n&&"owner"!=n||o({is_object_allowed:!0});const u={TableName:process.env.userPermissionsTableName,FilterExpression:"locationId = :locationId and companyId = :companyId and userId = :userId",ExpressionAttributeValues:{":userId":e,":locationId":r,":companyId":t}};1==(await a("scan",u)).Items.length?o({is_object_allowed:!0}):o({is_object_allowed:!1})})}r.d(t,"is_level_permitted",function(){return u}),r.d(t,"is_object_permitted",function(){return i}),o.a.config.update({region:"eu-central-1"})}]));