const axios = require('axios');
const oauth = require('axios-oauth-client');

export default class PowerOffice {

  constructor(client_id, client_secret) {

    return new Promise(async (resolve, reject) => {
      try {
        this.client_id = client_id;
        this.client_secret = client_secret;
        this.auth_url = process.env.POWEROFFICE_AUTH_URL;
        this.api_url = process.env.POWEROFFICE_API_URL;
        this.access_token = await this.getAuthorizationCode()

        this.axiosInstance = axios.create({
          baseURL: this.api_url,
          timeout: 300000,
          headers: {
            Authorization: 'Bearer ' + this.access_token,
            'Content-Type': 'application/json'
          }
        })

      } catch (ex) {
        return reject(ex);
      }
      resolve(this);
    });


  }

  async getGeneralLedgerAccounts() {


    let result = await this.axiosInstance.get('/GeneralLedgerAccount')
    return result.data

  }

  async getVatCodes() {
    let result = await this.axiosInstance.get('/VatCode')
    return result.data

  }

  async getCustomers($filter = null) {
    let result = ($filter == null) ? await this.axiosInstance.get('/customer') : await this.axiosInstance.get('/customer' + $filter)
    return result.data

  }

  async getCustomer(customer_id) {
    let result = await this.axiosInstance.get('/customer/' + customer_id)
    return result.data

  }

  async getCustomerContactPerson(customer_id) {
    let result = await this.axiosInstance.get('/customer/' + customer_id + '/contact')
    return result.data

  }

  async createCustomerContactPerson(customer_id, data) {
    let result = await this.axiosInstance.post('/customer/' + customer_id + '/contact', data)
    return result

  }
  async createCustomer(data) {
    let result = await this.axiosInstance.post('/customer', data)
    return result
  }
  async updateCustomer(data) {
    let result = await this.axiosInstance.post('/customer', data)
    return result
  }

  async createAddress(data) {
    let result = await this.axiosInstance.post('/address', data)
    return result
  }

  async createInvoice(data) {
    let result = await this.axiosInstance.post('/OutgoingInvoice', data)
    return result
  }

  async createBankJournalVoucher(data) {
    let result = await this.axiosInstance.post('/Voucher/BankJournalVoucher/', data)
    return result
  }

  async deleteInvoice(id) {
    let result = await this.axiosInstance.delete('/OutgoingInvoice/' + id + '/')
    return result
  }

  async getInvoice(id) {
    let result = await this.axiosInstance.get('/OutgoingInvoice/' + id + '/')
    return result.data

  }

  async getProductGroups() {
    let result = await this.axiosInstance.get('/ProductGroup')
    return result.data

  }

  async createProductGroup(data) {
    let result = await this.axiosInstance.post('/ProductGroup', data)
    return result
  }

  async updateProductGroup(data) {
    let result = await this.axiosInstance.post('/ProductGroup', data)
    return result
  }

  async deleteProductGroup(id) {
    let result = await this.axiosInstance.delete('/ProductGroup/' + id + '/')
    return result
  }

  async getProducts() {
    let result = await this.axiosInstance.get('/Product')
    return result.data

  }

  async createProduct(data) {
    let result = await this.axiosInstance.post('/Product', data)
    return result
  }

  async updateProduct(data) {
    let result = await this.axiosInstance.post('/Product', data)
    return result
  }


  sleep(d) {
    return new Promise(resolve => setTimeout(resolve, d))
  }

  async getAuthorizationCode() {

    const getAuthorizationCode = oauth.client(axios.create(), {
      url: this.auth_url,
      grant_type: 'client_credentials',
      client_id: this.client_id,
      client_secret: this.client_secret,

    });
    const auth = await getAuthorizationCode();
    return auth.access_token

  }
}