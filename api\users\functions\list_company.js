import {
  success,
  failure
} from "../../../libs/response-lib";
import * as dynamoDbLib from "../../../libs/dynamodb-lib";
import {
  is_level_permitted
} from "../../../libs/permissions";
import * as bugsnagClient from "../../../libs/bugsnag";

const AWS = require('aws-sdk')
const cognito_client = new AWS.CognitoIdentityServiceProvider()

export async function company(event, context, callback) {

  try {
    let access_scope = ['owner', 'manager', 'employee']
    //  const data = JSON.parse(event.body);
    var user_id = event.requestContext.identity.cognitoAuthenticationProvider.split('CognitoSignIn:')[1]
    let company_id = event.pathParameters.id
    const permissions = await is_level_permitted(user_id, access_scope);

    if (permissions.level_allowed) {


      const company = await dynamoDbLib.call("scan", {
        TableName: process.env.objectsTableName,
        FilterExpression: "objectType = :objectType and objectId = :objectId",
        ExpressionAttributeValues: {
          ":objectType": 'company',
          ":objectId": (company_id == 'null') ? permissions.user_company_id : company_id
        }
      });

      callback(null, success(company.Items[0]))

    } else {

      callback(null, failure({
        status: 'you do not have access to this api call'
      }));

    }

  } catch (e) {
    console.log(e);
    bugsnagClient.notify(user_id, e)
    callback(null, failure({
      status: e
    }));
  }

}