import * as dynamoDbLib from "../../../libs/dynamodb-lib";
import * as bugsnagClient from "../../../libs/bugsnag";

import {
  success,
  failure
} from "../../../libs/response-lib";

import {
  is_level_permitted
} from "../../../libs/permissions";


export async function initiate(event, context, callback) {


  try {

    const access_scope = ['owner', 'manager', 'employee']

    const data = JSON.parse(event.body);

    var user_id = event.requestContext.identity.cognitoAuthenticationProvider.split('CognitoSignIn:')[1]

    const permissions = await is_level_permitted(user_id, access_scope);

    if (permissions.level_allowed) {

      let shop_id = event.pathParameters.id

      const shopParams = {
        TableName: process.env.objectsTableName,
        FilterExpression: "objectType = :objectType and objectId = :objectId",
        ExpressionAttributeValues: {
          ":objectType": 'shop',
          ":objectId": shop_id
        }
      }


      const shopResult = await dynamoDbLib.call("scan", shopParams);

      let shop = shopResult.Items[0]
      let payment_method = shop.objectPaymentMethod
      let stripe_integration_id = shop.objectStripeIntegration

      if (payment_method == 'stripe' && stripe_integration_id) {


        const integrationParams = {
          TableName: process.env.integrationsTableName,
          FilterExpression: "integrationType = :integrationType and integrationId = :integrationId",
          ExpressionAttributeValues: {
            ":integrationType": 'stripe',
            ":integrationId": stripe_integration_id
          }
        }

        const stripeIntegrationResult = await dynamoDbLib.call("scan", integrationParams);
        let stripeIntegration = stripeIntegrationResult.Items[0]
        let pkey = stripeIntegration.integrationPKey
        let skey = stripeIntegration.integrationSKey


        let shipping_cost = (shop.shippingCost) ? shop.shippingCost : 0


        let params1 = {
          TableName: process.env.userPermissionsTableName,
          FilterExpression: "userId = :userId and locationId = :locationId",
          ExpressionAttributeValues: {
            ":userId": permissions.user_company_id,
            ":locationId": data.delivery_point
          }
        };

        const result1 = await dynamoDbLib.call("scan", params1);


        if (result1.Items.length == 1 || is_shop_owner) {




          let today16 = new Date();
          today16.setHours(16, 0, 0, 0);

          let today16_time_stamp = today16.getTime()
          let now_time_stamp = new Date().getTime()
          let delivery_date_time_stamp = new Date(data.delivery_date).getTime()


          if (
            (delivery_date_time_stamp >= today16_time_stamp) ||
            (delivery_date_time_stamp <= today16_time_stamp && now_time_stamp <= today16_time_stamp)
          ) {

            let customer_company_id = (data.company_id != null) ? data.company_id : permissions.user_company_id

            const customer_permission = await dynamoDbLib.call("scan", {
              TableName: process.env.userPermissionsTableName,
              FilterExpression: "shopId = :shopId and roleType = :roleType and isActive = :isActive and companyId = :companyId",
              ExpressionAttributeValues: {
                ":shopId": shop_id,
                ":roleType": 'shop_access_role',
                ":isActive": true,
                ":companyId": customer_company_id
              }
            });

            let discount = (customer_permission.Items[0].discount) ? customer_permission.Items[0].discount : 0
            let discount_value = 0
            let freight = shipping_cost
            let total = 0
            let total_quantity = 0

            for (var x in data.items) {

              total += (parseFloat(data.items[x].price) * data.items[x].quantity)
              total_quantity += data.items[x].quantity
            }

            if (discount > 0) {
              discount_value = total * (discount / 100)
              total = total - discount_value
            }

            total += parseFloat(freight)

            if (total_quantity > 0) {



              const stripe = require('stripe')(skey);

              const session = await stripe.checkout.sessions.create({
                payment_method_types: ['card'],
                line_items: [{
                  name: 'T-shirt',
                  description: 'Comfortable cotton t-shirt',
                  images: ['https://example.com/t-shirt.png'],
                  amount: 500,
                  currency: 'usd',
                  quantity: 1,
                }],
                success_url: 'https://example.com/success',
                cancel_url: 'https://example.com/cancel',
              });




              let cart_id = uuid.v1()


              const params = {
                TableName: process.env.cartTableName,
                Item: {
                  cartId: cart_id,
                  userId: user_id,
                  freight: freight,
                  total: parseFloat(Math.round(total * 100) / 100).toFixed(2),
                  discount: discount,
                  discount_value: discount_value,
                  shopId: shop_id,
                  schedule_dates: data.schedule_dates,
                  schedule_type: data.schedule_type,
                  orderStatus: 'pending',
                  companyId: (data.company_id != null) ? data.company_id : permissions.user_company_id,
                  on_behalf_of: data.customer_employee_id,
                  delivery_date: delivery_date_time_stamp,
                  delivery_point: data.delivery_point,
                  delivery_time: data.delivery_time,
                  comment: (data.comment) ? data.comment : null,
                  items: data.items,
                  tzo: (data.tzo) ? data.tzo : 0,
                  createdAt: new Date().getTime()
                }
              };

              await dynamoDbLib.call("put", params);



            } else {
              callback(null, failure({
                status: '0 products ordered'
              }));
            }


          } else {


            callback(null, failure({
              status: 'You are not allwed to place an order at this time'
            }));

          }

        } else {

          callback(null, failure({
            status: 'You do not have access to this delivery point'
          }));

        }

      } else {

        callback(null, failure({
          status: 'Stripe is not configured as a payment method for this shop.'
        }));

      }


    } else {

      callback(null, failure({
        status: 'you do not have access to this api call'
      }));

    }
  } catch (e) {
    bugsnagClient.notify(user_id, e)
    callback(null, failure({
      status: e
    }));
  }
}