import uuid from "uuid";
import * as dynamoDbLib from "../../../libs/dynamodb-lib";
import * as bugsnagClient from "../../../libs/bugsnag";

import {
  success,
  failure
} from "../../../libs/response-lib";

import {
  is_level_permitted
} from "../../../libs/permissions";

const AWS = require('aws-sdk')
const SNS = new AWS.SNS();
const cognito_client = new AWS.CognitoIdentityServiceProvider()

export async function asset(event, context, callback) {

  try {

    const access_scope = ['owner', 'manager', 'employee']
    const data = JSON.parse(event.body);
    let pool_id = process.env.COGNITO_POOL_ID
    var user_id = event.requestContext.identity.cognitoAuthenticationProvider.split('CognitoSignIn:')[1]

    const permissions = await is_level_permitted(user_id, access_scope);

    if (permissions.level_allowed) {

      let new_asset_id = uuid.v1()

      const params = {
        TableName: process.env.objectsTableName,
        Item: {
          objectCreatedBy: user_id,
          objectCompanyId: permissions.user_company_id,
          objectId: new_asset_id,
          objectName: data.objectName,
          objectLocation: data.objectLocation,
          objectType: "asset",
          objectAutoAssignTasks: {
            review: 'any-owner',
            making: 'any-owner',
            packing: 'any-owner',
            shipping: 'any-owner'
          },
          createdAt: new Date().getTime()
        }
      };

      await dynamoDbLib.call("put", params);


      let new_device_id1 = uuid.v1()

      const deviceParams1 = {
        TableName: process.env.objectsTableName,
        Item: {
          objectCreatedBy: user_id,
          objectId: new_device_id1,
          objectName: 'Refrigerator',
          objectCompanyId: permissions.user_company_id,
          objectParent: new_asset_id,
          objectKind: 'refrigerator',
          objectType: "device",
          createdAt: new Date().getTime()
        }
      };

      await dynamoDbLib.call("put", deviceParams1);


      // then attch a sensor for it.
      const sensorParam1 = {
        TableName: process.env.objectsTableName,
        Item: {
          objectCreatedBy: user_id,
          objectId: uuid.v1(),
          objectParent: new_device_id1,
          objectCompanyId: permissions.user_company_id,
          objectDataId: null,
          objectType: "sensor",
          createdAt: new Date().getTime()
        }
      };

      await dynamoDbLib.call("put", sensorParam1);

      //create sample daily clean task.
      /*
      let task_id = uuid.v1()

      const taskParams = {
        TableName: process.env.tasksTableName,
        Item: {
          taskId: task_id,
          assignerId: user_id,
          assignees: 'anyone',
          taskName: 'log temp',
          taskType: 'logtemp',
          taskDesc: 'Sample task created on account setup for logtemp devices.',
          taskRepeat: 'daily',
          locationId: new_asset_id,
          companyId: permissions.user_company_id,
          taskStatus: 'open',
          taskDates: [],
          taskTime: '20:00:00',
          createdAt: new Date().getTime()
        }
      };


      await dynamoDbLib.call("put", taskParams);

      SNS.publish({
        Message: task_id,
        TopicArn: process.env.generateCompaniesTasksSNSTopic
      }, function(err, data) {
        if (err) {
          //  callback(null, err);
        }
        // sns sent. good job
      });
      */

      let users_data = await cognito_client.listUsers({
        UserPoolId: pool_id,
        AttributesToGet: null,
        Limit: 0
      }).promise()

      for (var x in users_data.Users) {
        let is_in_company = false
        let is_owner = false
        for (var y in users_data.Users[x].Attributes) {
          if (users_data.Users[x].Attributes[y].Name == 'custom:company_id' && users_data.Users[x].Attributes[y].Value == permissions.user_company_id) {
            is_in_company = true
          }
          if (users_data.Users[x].Attributes[y].Name == 'custom:user_type' && users_data.Users[x].Attributes[y].Value == 'owner') {
            is_owner = true
          }
        }

        if (is_in_company && is_owner) {

          await dynamoDbLib.call("put", {

            TableName: process.env.userPermissionsTableName,
            Item: {
              grantedBy: user_id,
              userId: users_data.Users[x].Username,
              companyId: permissions.user_company_id,
              locationId: new_asset_id,
              permissionId: uuid.v1(),
              createdAt: new Date().getTime()
            }
          });

        }
      }

      callback(null, success(params.Item));

    } else {

      callback(null, failure({
        status: 'you do not have access to this api call'
      }));

    }

  } catch (e) {

    bugsnagClient.notify(user_id, e)

    callback(null, failure({
      status: e
    }));
  }

}