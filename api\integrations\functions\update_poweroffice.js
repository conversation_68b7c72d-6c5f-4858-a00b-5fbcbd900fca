import * as dynamoDbLib from "../../../libs/dynamodb-lib";
import * as bugsnagClient from "../../../libs/bugsnag";
import {
  success,
  failure
} from "../../../libs/response-lib";

import {
  is_level_permitted
} from "../../../libs/permissions";
const AWS = require('aws-sdk')
const SNS = new AWS.SNS();
export async function integration(event, context, callback) {

  try {

    const access_scope = ['owner', 'manager', 'employee']
    const data = JSON.parse(event.body);
    var user_id = event.requestContext.identity.cognitoAuthenticationProvider.split('CognitoSignIn:')[1]

    const permissions = await is_level_permitted(user_id, access_scope);
    if (permissions.level_allowed) {

      const params = {
        TableName: process.env.integrationsTableName,

        Key: {
          integrationId: event.pathParameters.id
        },

        UpdateExpression: "SET integrationName = :integrationName, integrationPO0MVA = :integrationPO0MVA, integrationPO15MVA = :integrationPO15MVA, integrationPO25MVA = :integrationPO25MVA, integrationPOBankFeesAccount = :integrationPOBankFeesAccount, integrationLastUpdatedBy = :integrationLastUpdatedBy",
        ExpressionAttributeValues: {
          ":integrationName": data.integrationName ? data.integrationName : null,
          ":integrationPO0MVA": data.integrationPO0MVA ? data.integrationPO0MVA : null,
          ":integrationPO15MVA": data.integrationPO15MVA ? data.integrationPO15MVA : null,
          ":integrationPO25MVA": data.integrationPO25MVA ? data.integrationPO25MVA : null,
          ":integrationPOBankFeesAccount": data.integrationPOBankFeesAccount ? data.integrationPOBankFeesAccount : null,
          ":integrationLastUpdatedBy": user_id
        },
        ReturnValues: "ALL_NEW"
      };

      /*
      let shop = await dynamoDbLib.call("scan", {
        TableName: process.env.objectsTableName,
        FilterExpression: "objectPowerOfficeIntegration = :objectPowerOfficeIntegration",
        ExpressionAttributeValues: {
          ":objectPowerOfficeIntegration": event.pathParameters.id
        }
      });

      if (shop.Items) {
        await SNS.publish({
          Message: shop.Items[0].objectId,
          TopicArn: process.env.generatePowerOfficeInvoicesSNSTopic
        }, function(err, data) {

          if (err) {
            callback(null, err);
          }
        });
      }

      */

      const result = await dynamoDbLib.call("update", params);
      callback(null, success({
        status: true
      }));

    } else {

      callback(null, failure({
        status: 'you do not have access to this api call'
      }));

    }


  } catch (e) {
    bugsnagClient.notify(user_id, e)
    callback(null, failure({
      status: false
    }));
  }
}