{"name": "serverless", "version": "1.0.0", "description": "", "main": "handler.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"aws-sdk": "^2.211.0", "babel-core": "^6.26.0", "babel-loader": "^7.1.4", "babel-plugin-transform-runtime": "^6.23.0", "babel-preset-env": "^1.6.1", "babel-preset-stage-3": "^6.24.1", "serverless-webpack": "^5.1.0", "webpack": "^4.1.1", "webpack-node-externals": "^1.6.0"}, "dependencies": {"uuid": "^3.2.1", "@bugsnag/js": "^6.1.0"}}