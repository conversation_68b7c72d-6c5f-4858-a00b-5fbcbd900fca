import * as dynamoDbLib from "../../../libs/dynamodb-lib";
import * as bugsnagClient from "../../../libs/bugsnag";
import {
  success,
  failure
} from "../../../libs/response-lib";

import {
  is_level_permitted
} from "../../../libs/permissions";

export async function integration(event, context, callback) {

  try {

    const access_scope = ['owner', 'manager', 'employee']
    const data = JSON.parse(event.body);
    var user_id = event.requestContext.identity.cognitoAuthenticationProvider.split('CognitoSignIn:')[1]

    const permissions = await is_level_permitted(user_id, access_scope);
    if (permissions.level_allowed) {

      const params = {
        TableName: process.env.integrationsTableName,

        Key: {
          integrationId: event.pathParameters.id
        },

        UpdateExpression: "SET integrationName = :integrationName, integrationPKey = :integrationPKey, integrationSKey =:integrationSKey, integrationLastUpdatedBy = :integrationLastUpdatedBy",
        ExpressionAttributeValues: {
          ":integrationName": data.integrationName ? data.integrationName : null,
          ":integrationPKey": data.integrationPKey ? data.integrationPKey : null,
          ":integrationSKey": data.integrationSKey ? data.integrationSKey : null,
          ":integrationLastUpdatedBy": user_id
        },
        ReturnValues: "ALL_NEW"
      };

      const result = await dynamoDbLib.call("update", params);
      callback(null, success({
        status: true
      }));

    } else {

      callback(null, failure({
        status: 'you do not have access to this api call'
      }));

    }


  } catch (e) {
    bugsnagClient.notify(user_id, e)
    callback(null, failure({
      status: false
    }));
  }
}