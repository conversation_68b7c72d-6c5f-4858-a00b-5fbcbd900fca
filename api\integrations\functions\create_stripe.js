import uuid from "uuid";
import * as dynamoDbLib from "../../../libs/dynamodb-lib";
import * as bugsnagClient from "../../../libs/bugsnag";

import {
  success,
  failure
} from "../../../libs/response-lib";

import {
  is_level_permitted
} from "../../../libs/permissions";

const AWS = require('aws-sdk')
const SNS = new AWS.SNS();
const cognito_client = new AWS.CognitoIdentityServiceProvider()

export async function integration(event, context, callback) {

  try {

    const access_scope = ['owner', 'manager', 'employee']
    const data = JSON.parse(event.body);
    let pool_id = process.env.COGNITO_POOL_ID
    var user_id = event.requestContext.identity.cognitoAuthenticationProvider.split('CognitoSignIn:')[1]

    const permissions = await is_level_permitted(user_id, access_scope);

    if (permissions.level_allowed) {

      let new_integration_id = uuid.v1()

      const params = {
        TableName: process.env.integrationsTableName,
        Item: {
          integrationCreatedBy: user_id,
          integrationCompanyId: permissions.user_company_id,
          integrationId: new_integration_id,
          integrationName: data.integrationName,
          integrationType: 'stripe',
          integrationPKey: data.integrationPKey,
          integrationSKey: data.integrationSKey,
          createdAt: new Date().getTime()
        }
      };

      await dynamoDbLib.call("put", params);
      callback(null, success(params.Item));

    } else {

      callback(null, failure({
        status: 'you do not have access to this api call'
      }));

    }

  } catch (e) {

    bugsnagClient.notify(user_id, e)

    callback(null, failure({
      status: e
    }));
  }

}