service: kompis-crons

plugins:
  - serverless-webpack

custom:
  webpackIncludeModules: true
  stage: ${opt:stage, self:provider.stage}
  stageSufix:
    prod: ""
    dev: -dev
  congnito_pool_id:
    dev: eu-central-1_o36r1nMGj
    prod: eu-central-1_E1nLfop0C
  shop_domain_name:
    dev: devshops.kompis.app
    prod: shops.kompis.app
  shop_cloudfront_dist:
    dev: E3LF0LR3XFNVP4
    prod: E2IUXGMYQJH84L
  stripe_publishable_key:
    dev: sk_test_WGGioCRxEnyu3wXcaPQ0KcFS00qnNm4u3t
    prod: ******************************************
  stripe_integration_id:
    dev: 18072560-3f5-11e9-9b43-6db913a7fae0
    prod: 18072560-3f5-11e9-9b43-6db913a7fae0
  poweroffice_auth_url:
    dev: https://api-demo.poweroffice.net/OAuth/Token
    prod: https://api.poweroffice.net/OAuth/Token
  poweroffice_api_url:
    dev: https://api-demo.poweroffice.net
    prod: https://api.poweroffice.net

  poweroffice_app_key:
    dev: 0fabc9dc-5477-4459-bd0c-7bc19a02caf3
    prod: 4ace3064-f987-48f1-9111-c96c896f36c6

provider:
  versionFunctions: false
  name: aws
  endpointType: REGIONAL
  runtime: nodejs16.x
  stage: prod
  region: eu-central-1
  environment:
    COGNITO_POOL_ID: ${self:custom.congnito_pool_id.${self:provider.stage}}
    POWEROFFICE_AUTH_URL: ${self:custom.poweroffice_auth_url.${self:provider.stage}}
    POWEROFFICE_API_URL: ${self:custom.poweroffice_api_url.${self:provider.stage}}
    POWEROFFICE_APP_KEY: ${self:custom.poweroffice_app_key.${self:provider.stage}}
    STRIPE_PUBLISHABLE_KEY: ${self:custom.stripe_publishable_key.${self:provider.stage}}
    STRIPE_INTEGRATION_ID: ${self:custom.stripe_integration_id.${self:provider.stage}}
    SHOP_DOMAIN_NAME: ${self:custom.shop_domain_name.${self:provider.stage}}
    SHOP_CLOUDFRONT_DIST: ${self:custom.shop_cloudfront_dist.${self:provider.stage}}
    generateCompaniesTasksSNSTopic: arn:aws:sns:eu-central-1:589634798762:generate_companies_tasks${self:custom.stageSufix.${self:custom.stage}}
    generateRecurringOrdersSNSTopic: arn:aws:sns:eu-central-1:589634798762:generate_recurring_orders${self:custom.stageSufix.${self:custom.stage}}
    generateBillsSNSTopic: arn:aws:sns:eu-central-1:589634798762:generate_bills${self:custom.stageSufix.${self:custom.stage}}
    generatePowerOfficeInvoicesSNSTopic: arn:aws:sns:eu-central-1:589634798762:generate_poweroffice_invoices${self:custom.stageSufix.${self:custom.stage}}
    generatePowerOfficeCategoriesSNSTopic: arn:aws:sns:eu-central-1:589634798762:generate_poweroffice_categories${self:custom.stageSufix.${self:custom.stage}}
    generatePowerOfficeProductsSNSTopic: arn:aws:sns:eu-central-1:589634798762:generate_poweroffice_products${self:custom.stageSufix.${self:custom.stage}}
    generatePowerOfficeShippingSNSTopic: arn:aws:sns:eu-central-1:589634798762:generate_poweroffice_shipping${self:custom.stageSufix.${self:custom.stage}}
    generatePowerOfficeCustomersSNSTopic: arn:aws:sns:eu-central-1:589634798762:generate_poweroffice_customers${self:custom.stageSufix.${self:custom.stage}}
    generatePowerOfficeOrdersSNSTopic: arn:aws:sns:eu-central-1:589634798762:generate_poweroffice_orders${self:custom.stageSufix.${self:custom.stage}}
    handelRecurringOrdersPaymentsSNSTopic: arn:aws:sns:eu-central-1:589634798762:handel_recurring_orders_payments${self:custom.stageSufix.${self:custom.stage}}
    updateCloudFrontCnamesSNSTopic: arn:aws:sns:eu-central-1:589634798762:update_cloudfront_cnames${self:custom.stageSufix.${self:custom.stage}}
    cartTableName: cart${self:custom.stageSufix.${self:custom.stage}}
    exceptionsTableName: exceptions${self:custom.stageSufix.${self:custom.stage}}
    objectsTableName: objects${self:custom.stageSufix.${self:custom.stage}}
    sensorsDataLogsTableName: sensorsDataLogs${self:custom.stageSufix.${self:custom.stage}}
    tasksTableName: tasks${self:custom.stageSufix.${self:custom.stage}}
    tasksLogTableName: tasksLog${self:custom.stageSufix.${self:custom.stage}}
    tasksSchedulesTableName: tasksSchedules${self:custom.stageSufix.${self:custom.stage}}
    userPermissionsTableName: userPermissions${self:custom.stageSufix.${self:custom.stage}}
    logsTableName: logs${self:custom.stageSufix.${self:custom.stage}}
    domainsTableName: domains${self:custom.stageSufix.${self:custom.stage}}
    integrationsTableName: integrations${self:custom.stageSufix.${self:custom.stage}}
    transactionsTableName: transactions${self:custom.stageSufix.${self:custom.stage}}
    billsTableName: bills${self:custom.stageSufix.${self:custom.stage}}

  iam:
    role:
      statments:
        - Effect: "Allow"
          Action:
            - "SNS:Publish"
          Resource: "arn:aws:sns:eu-central-1:589634798762:generate_companies_tasks${self:custom.stageSufix.${self:custom.stage}}"

        - Effect: "Allow"
          Action:
            - "SNS:Publish"
          Resource: "arn:aws:sns:eu-central-1:589634798762:generate_recurring_orders${self:custom.stageSufix.${self:custom.stage}}"

        - Effect: "Allow"
          Action:
            - "SNS:Publish"
          Resource: "arn:aws:sns:eu-central-1:589634798762:generate_bills${self:custom.stageSufix.${self:custom.stage}}"

        - Effect: "Allow"
          Action:
            - "SNS:Publish"
          Resource: "arn:aws:sns:eu-central-1:589634798762:handel_recurring_orders_payments${self:custom.stageSufix.${self:custom.stage}}"

        - Effect: "Allow"
          Action:
            - "SNS:Publish"
          Resource: "arn:aws:sns:eu-central-1:589634798762:generate_poweroffice_invoices${self:custom.stageSufix.${self:custom.stage}}"

        - Effect: "Allow"
          Action:
            - "SNS:Publish"
          Resource: "arn:aws:sns:eu-central-1:589634798762:generate_poweroffice_orders${self:custom.stageSufix.${self:custom.stage}}"

        - Effect: "Allow"
          Action:
            - "cognito-idp:*"
          Resource: "arn:aws:cognito-idp:eu-central-1:*:userpool/*"

        - Effect: Allow
          Action:
            - dynamodb:Query
            - dynamodb:Scan
            - dynamodb:GetItem
            - dynamodb:PutItem
            - dynamodb:UpdateItem
            - dynamodb:DeleteItem
          Resource: "arn:aws:dynamodb:eu-central-1:*:*"

functions:
  cleanTasks:
    handler: functions/clean.init
    timeout: 300
    memorySize: 2048

  powerofficeSync:
    handler: functions/poweroffice.init
    events:
      - sns: generate_poweroffice_invoices${self:custom.stageSufix.${self:custom.stage}}
    timeout: 900
    memorySize: 2048

  powerofficeCategoriesSync:
    handler: functions/poweroffice/categories_sync.init
    events:
      - sns: generate_poweroffice_categories${self:custom.stageSufix.${self:custom.stage}}

  powerofficeProductsSync:
    handler: functions/poweroffice/products_sync.init
    events:
      - sns: generate_poweroffice_products${self:custom.stageSufix.${self:custom.stage}}

  powerofficeShippingSync:
    handler: functions/poweroffice/shipping_sync.init
    events:
      - sns: generate_poweroffice_shipping${self:custom.stageSufix.${self:custom.stage}}

  powerofficeCustomersSync:
    handler: functions/poweroffice/customers_sync.init
    events:
      - sns: generate_poweroffice_customers${self:custom.stageSufix.${self:custom.stage}}

  powerofficeOrdersSync:
    handler: functions/poweroffice/orders_sync.init
    events:
      - sns: generate_poweroffice_orders${self:custom.stageSufix.${self:custom.stage}}

  acm:
    handler: functions/acm.init
    timeout: 300
    memorySize: 2048

  validateCustomDomain:
    handler: functions/validate_custom_domain_ssl.validate
    timeout: 300
    memorySize: 2048

  intiCompaniesTasks:
    handler: functions/init_compaines_tasks.init
    timeout: 300

  generateCompaniesBills:
    handler: functions/generate_companies_bills.generate
    events:
      - sns: generate_bills${self:custom.stageSufix.${self:custom.stage}}

  sendOrdersDailyReport:
    handler: functions/send_orders_daily_report.send

  generateCompaniesTasksAll:
    handler: functions/generate_companies_tasks.generate
    timeout: 300
    memorySize: 2048

  updateCloudFrontCnames:
    handler: functions/update_cloudfront_cnames.update
    timeout: 300
    memorySize: 2048
    events:
      - sns: update_cloudfront_cnames${self:custom.stageSufix.${self:custom.stage}}

  generateCompaniesTasks:
    handler: functions/generate_companies_tasks_by_id.generate
    timeout: 300
    memorySize: 2048
    events:
      - sns: generate_companies_tasks${self:custom.stageSufix.${self:custom.stage}}

  generateRecurringOrders:
    handler: functions/generate_recurring_orders.generate
    timeout: 300
    memorySize: 2048
    events:
      - sns: generate_recurring_orders${self:custom.stageSufix.${self:custom.stage}}

  handelRecurringOrdersPayments:
    handler: functions/handel_recurring_orders_payments.pay
    timeout: 300
    memorySize: 2048
    events:
      - sns: handel_recurring_orders_payments${self:custom.stageSufix.${self:custom.stage}}
