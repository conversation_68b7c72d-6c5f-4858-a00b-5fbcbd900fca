import * as dynamoDbLib from "../../../libs/dynamodb-lib";
import * as bugsnagClient from "../../../libs/bugsnag";
import {
  success,
  failure
} from "../../../libs/response-lib";

import {
  is_level_permitted,
  is_object_permitted
} from "../../../libs/permissions";


export async function permissions(event, context, callback) {


  try {
    const access_scope = ['owner', 'manager', 'employee']

    var user_id = event.requestContext.identity.cognitoAuthenticationProvider.split('CognitoSignIn:')[1]


    const permissions = await is_level_permitted(user_id, access_scope);

    if (permissions.level_allowed) {

      const permissionsParam = {
        TableName: process.env.userPermissionsTableName,
        FilterExpression: "userId = :userId ",
        ExpressionAttributeValues: {
          ":userId": event.pathParameters.id
          //":roleType": null
        }
      };

      let permissionsResult = await dynamoDbLib.call("scan", permissionsParam);

      let locations = []

      for (var x in permissionsResult.Items) {

        locations.push(permissionsResult.Items[x].locationId)
      }

      callback(null, success(locations))


    } else {

      callback(null, failure({
        status: 'you do not have access to this api call'
      }));

    }

  } catch (e) {
    bugsnagClient.notify(user_id, e)
    callback(null, failure({
      error: e
    }))
  }
}