import * as dynamoDbLib from "../../../libs/dynamodb-lib";
import * as bugsnagClient from "../../../libs/bugsnag";
import {
  success,
  failure
} from "../../../libs/response-lib";

import {
  is_level_permitted
} from "../../../libs/permissions";

const groupArray = require('group-array');
const arraySort = require('array-sort')
var in_array = require('in_array');

import unique from '@arr/unique';


export async function tasks(event, context, callback) {


  try {

    let access_scope = ['owner', 'manager', 'employee']
    var user_id = event.requestContext.identity.cognitoAuthenticationProvider.split('CognitoSignIn:')[1]

    const permissions = await is_level_permitted(user_id, access_scope);

    if (permissions.level_allowed) {

      let tasks_period = event.pathParameters.id;
      let user_type = permissions.user_type

      // get the timestamp for the last second on this week.
      let week_day = new Date();
      let end_of_the_week_day_index = 7 // 7 means Sunday
      week_day.setDate(week_day.getDate() + (end_of_the_week_day_index + 7 - week_day.getDay()) % 7);

      let end_of_this_week_timestamp = new Date(week_day.getFullYear() + '-' + (week_day.getMonth() + 1) + '-' + week_day.getDate() + ' 23:59:59').getTime()

      // get the timestamp for the last second of today.
      let end_of_today_timestamp = (new Date()).setHours(23, 59, 59)

      let tasksParams = {}

      switch (tasks_period) {
        case 'today':
          {
            tasksParams = {
              TableName: process.env.tasksSchedulesTableName,
              FilterExpression: "companyId = :companyId and taskStatus = :taskStatus and tasksScheduleTime <= :tasksScheduleTime",
              ExpressionAttributeValues: {
                ":companyId": permissions.user_company_id,
                ":taskStatus": 'open',
                ":tasksScheduleTime": end_of_today_timestamp
              }
            }
            break;
          }
        case 'week':
          {
            tasksParams = {
              TableName: process.env.tasksSchedulesTableName,
              FilterExpression: "companyId = :companyId and taskStatus = :taskStatus and tasksScheduleTime >= :tasksScheduleTime1 and tasksScheduleTime <= :tasksScheduleTime2",
              ExpressionAttributeValues: {
                ":companyId": permissions.user_company_id,
                ":taskStatus": 'open',
                ":tasksScheduleTime1": end_of_today_timestamp,
                ":tasksScheduleTime2": end_of_this_week_timestamp,
              }
            }
            break;
          }
        case 'all':
          {
            tasksParams = {
              TableName: process.env.tasksSchedulesTableName,
              FilterExpression: "companyId = :companyId and taskStatus = :taskStatus and tasksScheduleTime > :tasksScheduleTime",
              ExpressionAttributeValues: {
                ":companyId": permissions.user_company_id,
                ":taskStatus": 'open',
                ":tasksScheduleTime": end_of_this_week_timestamp,
              }
            }
            break;
          }

      }

      let tasksResult = await dynamoDbLib.call("scan", tasksParams);
      let tasks = tasksResult.Items
      /*
      callback(null, success({
        tasksResult,
        dd: (typeof tasksResult.LastEvaluatedKey != "undefined")
      }))
      */

      tasks = arraySort(tasks, 'tasksScheduleTime')


      let my_tasks = []
      let assinged_to_others_tasks = []
      let unassined_tasks = []

      const permissionsParam = {
        TableName: process.env.userPermissionsTableName,
        FilterExpression: "userId = :userId",
        ExpressionAttributeValues: {
          ":userId": user_id
        }
      };

      let permissionsResult = await dynamoDbLib.call("scan", permissionsParam);
      let user_permissions = []

      for (var x in permissionsResult.Items) {


        user_permissions.push(permissionsResult.Items[x].locationId)
      }

      let objects_ids = []
      let objects = []

      for (var x in tasks) {

        if (user_permissions.indexOf(tasks[x].locationId) != -1) {

          // push the locations ids to get thier names later
          objects_ids.push(tasks[x].locationId)

          if (tasks[x].deviceId) {
            // if this is a temp log task, we get the device id to get it's name later
            objects_ids.push(tasks[x].deviceId)
          }

          tasks[x].tasksScheduleByTime = timeDifference(new Date, new Date(tasks[x].tasksScheduleTime))

          if (tasks[x].tasksScheduleTime <= end_of_today_timestamp) {

            tasks[x].tasksSchedulePeriod = 'today'

          } else if (tasks[x].tasksScheduleTime <= end_of_this_week_timestamp) {
            tasks[x].tasksSchedulePeriod = 'week'
          } else {
            tasks[x].tasksSchedulePeriod = 'all'
          }

        }

      }
      objects_ids = unique(objects_ids)

      for (var x in objects_ids) {

        var objectParms = {
          TableName: process.env.objectsTableName,
          FilterExpression: "objectId = :objectId",
          ExpressionAttributeValues: {
            ":objectId": objects_ids[x]
          }
        };


        let object = await dynamoDbLib.call("scan", objectParms);
        if (object.Items[0] != null) {
          objects.push(object.Items[0])
        }
      }

      let any_my_type = 'any-' + user_type

      for (var x in tasks) {

        if (in_array(user_id, tasks[x].assignees)) {
          my_tasks.push(tasks[x])
        }

        if (tasks[x].assignerId == user_id && !in_array(user_id, tasks[x].assignees) && tasks[x].assignees != 'anyone' && tasks[x].assignees != 'anyone-employee' && tasks[x].assignees != 'anyone-manager' && tasks[x].assignees != 'anyone-owner') {
          assinged_to_others_tasks.push(tasks[x])
        }

        if ((tasks[x].assignees == 'anyone' || tasks[x].assignees == any_my_type) && user_permissions.indexOf(tasks[x].locationId) != -1) {
          unassined_tasks.push(tasks[x])
        }

      }

      my_tasks = groupArray(my_tasks, 'tasksSchedulePeriod', 'taskType', 'locationId', 'tasksScheduleTime')
      assinged_to_others_tasks = groupArray(assinged_to_others_tasks, 'tasksSchedulePeriod', 'taskType', 'locationId', 'tasksScheduleTime')
      unassined_tasks = groupArray(unassined_tasks, 'tasksSchedulePeriod', 'taskType', 'locationId', 'tasksScheduleTime')

      callback(null, success({
        my_tasks,
        assinged_to_others_tasks,
        unassined_tasks,
        objects

      }))

    } else {

      callback(null, failure({
        status: 'you do not have access to this api call'
      }));

    }

  } catch (e) {
    bugsnagClient.notify(user_id, e)
    callback(null, failure({
      status: e
    }));
  }


  function timeDifference(current, previous) {

    var msPerMinute = 60 * 1000;
    var msPerHour = msPerMinute * 60;
    var msPerDay = msPerHour * 24;
    var msPerMonth = msPerDay * 30;
    var msPerYear = msPerDay * 365;

    if (current > previous) {

      var elapsed = current - previous;

      if (elapsed < msPerMinute) {
        return Math.round(elapsed / 1000) + ' seconds ago';
      } else if (elapsed < msPerHour) {
        return Math.round(elapsed / msPerMinute) + ' minutes ago';
      } else if (elapsed < msPerDay) {
        return Math.round(elapsed / msPerHour) + ' hours ago';
      } else if (elapsed < msPerMonth) {
        return Math.round(elapsed / msPerDay) + ' days ago';
      } else if (elapsed < msPerYear) {
        return Math.round(elapsed / msPerMonth) + ' months ago';
      } else {
        return Math.round(elapsed / msPerYear) + ' years ago';
      }
    } else {

      var elapsed = previous - current;

      if (elapsed < msPerMinute) {
        return 'in ' + Math.round(elapsed / 1000) + ' seconds';
      } else if (elapsed < msPerHour) {
        return 'in ' + Math.round(elapsed / msPerMinute) + ' minutes';
      } else if (elapsed < msPerDay) {
        return 'in ' + Math.round(elapsed / msPerHour) + ' hours';
      } else if (elapsed < msPerMonth) {
        return 'in ' + Math.round(elapsed / msPerDay) + ' days';
      } else if (elapsed < msPerYear) {
        return 'in ' + Math.round(elapsed / msPerMonth) + ' months';
      } else {
        return 'in ' + Math.round(elapsed / msPerYear) + ' years';
      }

    }
  }

}