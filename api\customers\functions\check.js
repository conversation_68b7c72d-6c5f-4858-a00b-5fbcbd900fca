import {
  success,
  failure
} from "../../../libs/response-lib";

import {
  is_level_permitted
} from "../../../libs/permissions";

import * as dynamoDbLib from "../../../libs/dynamodb-lib";
import * as bugsnagClient from "../../../libs/bugsnag";

const AWS = require('aws-sdk')
const cognito_client = new AWS.CognitoIdentityServiceProvider()

export async function customer(event, context, callback) {

  try {

    let access_scope = ['owner', 'manager', 'employee']
    const data = JSON.parse(event.body);
    var user_id = event.requestContext.identity.cognitoAuthenticationProvider.split('CognitoSignIn:')[1]

    var pool_id = process.env.COGNITO_POOL_ID

    const permissions = await is_level_permitted(user_id, access_scope);

    let company = {}
    let email = data.email

    if (permissions.level_allowed) {

      let params = {
        UserPoolId: pool_id,
        Filter: "email = \"" + email + "\""
      };

      let new_user = await cognito_client.listUsers(params).promise()


      let company_id = ''
      let first_name = ''
      let last_name = ''
      let business_name = ''
      let company_organization_id = ''
      let user_type = ''
      let phone = ''

      if (new_user.Users.length > 0) {
        for (var x in new_user.Users[0].Attributes) {

          if (new_user.Users[0].Attributes[x].Name == 'custom:company_id') {
            company_id = new_user.Users[0].Attributes[x].Value
          }

          if (new_user.Users[0].Attributes[x].Name == 'custom:user_type') {
            user_type = new_user.Users[0].Attributes[x].Value
          }

          if (new_user.Users[0].Attributes[x].Name == 'custom:first_name') {
            first_name = new_user.Users[0].Attributes[x].Value
          }

          if (new_user.Users[0].Attributes[x].Name == 'custom:last_name') {
            last_name = new_user.Users[0].Attributes[x].Value
          }
          if (new_user.Users[0].Attributes[x].Name == 'custom:user_phone') {
            phone = new_user.Users[0].Attributes[x].Value
          }

          if (new_user.Users[0].Attributes[x].Name == 'custom:company_name') {
            business_name = new_user.Users[0].Attributes[x].Value
          }

          if (new_user.Users[0].Attributes[x].Name == 'custom:organization_id') {
            company_organization_id = new_user.Users[0].Attributes[x].Value
          }

        }


        //if (user_type == 'owner' && permissions.user_company_id != company_id) {
        if (user_type == 'owner') {

          company.company_id = company_id
          company.first_name = first_name
          company.last_name = last_name
          company.phone = phone

          let customer = await dynamoDbLib.call("scan", {
            TableName: process.env.objectsTableName,
            FilterExpression: "objectType = :objectType and objectId = :objectId",
            ExpressionAttributeValues: {
              ":objectType": 'company',
              ":objectId": company_id
            }
          })

          company.business_name = customer.Items[0].objectName
          company.company_organization_id = customer.Items[0].objectOrganizationId

        }
      }
      callback(null, success(company))

    } else {

      callback(null, failure({
        status: 'you do not have access to this api call'
      }));

    }

  } catch (e) {
    bugsnagClient.notify(user_id, e)
    callback(null, failure({
      error: e
    }))
  }

}