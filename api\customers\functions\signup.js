const AWS = require('aws-sdk')
import uuid from "uuid";
import * as dynamoDbLib from "../../../libs/dynamodb-lib";
import * as bugsnagClient from "../../../libs/bugsnag";
import * as time from '../../../libs/time'
import {
  success,
  failure
} from "../../../libs/response-lib";

exports.send = (event, context, callback) => {

  let cognito_client = new AWS.CognitoIdentityServiceProvider()
  let pool_id = process.env.COGNITO_POOL_ID
  let user_id = event.request.userAttributes.sub
  let code = event.request.codeParameter


  if (event.userPoolId === pool_id) {

    if (event.triggerSource === "CustomMessage_SignUp") {

      event.response.smsMessage = "Welcome to the service. Your confirmation code is " + code;
      event.response.emailSubject = "Din kode for registrering av ny konto";
      event.response.emailMessage = "Takk for din registrering!<br />Logg inn på kontrollpanelet ved å trykke på denne <a href=http://metamat-app.s3-website.eu-central-1.amazonaws.com/auth/confirm/" + user_id + "/" + code + ">lenken</a>.<br /><br />Har du spørsmål eller problemer?Ta kontakt med oss på <EMAIL>";


      let params = {
        UserPoolId: pool_id,
        Username: user_id
      };
      cognito_client.adminGetUser(params, function(err, data) {
        if (err) {

        } else {

          let company_id = ''
          let company_name = ''
          for (var x in data.UserAttributes) {

            if (data.UserAttributes[x].Name == 'custom:company_id') {
              company_id = data.UserAttributes[x].Value
            }

            if (data.UserAttributes[x].Name == 'custom:company_name') {
              company_name = data.UserAttributes[x].Value
            }

          }

          if (company_id == '') {

            let params = {
              UserAttributes: [{
                Name: 'custom:company_id',
                Value: user_id
              }],
              UserPoolId: pool_id,
              Username: user_id
            };
            cognito_client.adminUpdateUserAttributes(params, function(err, data) {
              if (err) {

              } else {
                var params = {
                  GroupName: 'Company',
                  UserPoolId: pool_id,
                  Username: user_id
                };
                cognito_client.adminAddUserToGroup(params, async function(err, data) {
                  if (err) {

                  } else {

                    // create permissions.


                    const permissionParams = {

                      TableName: process.env.userPermissionsTableName,
                      Item: {
                        grantedBy: 'signup',
                        companyId: user_id,
                        roleType: 'access_role',
                        accessType: 'company',
                        isActive: true,
                        permissionId: uuid.v1(),
                        createdAt: time.getNowTimeStamp()
                      }
                    };

                    await dynamoDbLib.call("put", permissionParams);
                    callback(null, success(params.Item));


                    // create shop.

                    const shopParams = {
                      TableName: process.env.objectsTableName,
                      Item: {
                        objectCreatedBy: user_id,
                        objectCompanyId: user_id,
                        objectId: uuid.v1(),
                        objectName: company_name,
                        objectType: "shop",
                        createdAt: time.getNowTimeStamp()
                      }
                    };

                    await dynamoDbLib.call("put", shopParams);

                    callback(null, success(params.Item));

                  }
                });
              }
            });
          } else {

          }
        }
      });

    }
  }
  callback(null, event);
};