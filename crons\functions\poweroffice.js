import { success, failure } from "../../libs/response-lib";

import { is_level_permitted } from "../../libs/permissions";
import * as bugsnagClient from "../../libs/bugsnag";
import * as dynamoDbLib from "../../libs/dynamodb-lib";

import PowerOffice from "../../libs/poweroffice";

const AWS = require("aws-sdk");
const cognito_client = new AWS.CognitoIdentityServiceProvider();

export async function init(event, context, callback) {
  try {
    //let shop_id = "dfea4f90-c455-11e9-9ab0-7fc92b1e522d";
    //let shop_id = 'e3336010-c455-11e9-9ab0-7fc92b1e522d'
    let shop_id = event.Records[0].Sns.Message;
    //let shop_id = 'dfea4f90-c455-11e9-9ab0-7fc92b1e522d'

    const shopsSarams = {
      TableName: process.env.objectsTableName,
      FilterExpression: "objectType = :objectType and objectId = :objectId",
      ExpressionAttributeValues: {
        ":objectType": "shop",
        ":objectId": shop_id,
      },
    };

    const shops = await dynamoDbLib.call("scan", shopsSarams);

    let orderDeadline = shops.Items[0].orderDeadline
      ? parseInt(shops.Items[0].orderDeadline)
      : 0;
    let orderDeadlineTime = shops.Items[0].orderDeadlineTime
      ? parseInt(shops.Items[0].orderDeadlineTime)
      : 0;

    if (shops.Items[0].objectPowerOfficeIntegration) {
      let company_id = shops.Items[0].objectCompanyId;
      let migration_mode = shops.Items[0].objectPowerOfficeMigrationMode;

      const integrationParams = {
        TableName: process.env.integrationsTableName,
        FilterExpression: "integrationId = :integrationId",
        ExpressionAttributeValues: {
          ":integrationId": shops.Items[0].objectPowerOfficeIntegration,
        },
      };

      const integrations = await dynamoDbLib.call("scan", integrationParams);
      let poweroffice_client_key = integrations.Items[0].integrationPOAppKey;
      let poweroffice_secrect_key =
        integrations.Items[0].integrationPOClientKey;
      let poweroffice_0_mva_account = integrations.Items[0].integrationPO0MVA;
      let poweroffice_15_mva_account = integrations.Items[0].integrationPO15MVA;
      let poweroffice_25_mva_account = integrations.Items[0].integrationPO25MVA;
      let migration_start_date = integrations.Items[0].createdAt;

      let poweroffice = await new PowerOffice(
        process.env.POWEROFFICE_APP_KEY,
        poweroffice_secrect_key
      );
      console.log(process.env.POWEROFFICE_APP_KEY, poweroffice_secrect_key);
      // Product Group Creationg

      let categoriesParams = {
        TableName: process.env.objectsTableName,
        FilterExpression:
          "objectType = :objectType and objectShopId = :objectShopId",
        ExpressionAttributeValues: {
          ":objectType": "category",
          ":objectShopId": shop_id,
        },
      };

      let categories = await dynamoDbLib.call("scan", categoriesParams);

      for (var x in categories.Items) {
        if (categories.Items[x].powerOfficeCategoryId) {
          await poweroffice.updateProductGroup({
            id: categories.Items[x].powerOfficeCategoryId,
            name: categories.Items[x].objectName + " - Kompis app",
          });
        } else {
          let category = await poweroffice.createProductGroup({
            name: categories.Items[x].objectName + " - Kompis app",
          });

          const result = await dynamoDbLib.call("update", {
            TableName: process.env.objectsTableName,

            Key: {
              objectCompanyId: company_id,
              objectId: categories.Items[x].objectId,
            },

            UpdateExpression:
              "SET powerOfficeCategoryId = :powerOfficeCategoryId",
            ExpressionAttributeValues: {
              ":powerOfficeCategoryId": category.data.data.id,
            },
            ReturnValues: "ALL_NEW",
          });
        }
      }

      // end of Product Group Creationg

      // Product Sync

      let productsParams = {
        TableName: process.env.objectsTableName,
        FilterExpression:
          "objectType = :objectType and objectShopId = :objectShopId",
        ExpressionAttributeValues: {
          ":objectType": "product",
          ":objectShopId": shop_id,
        },
      };

      categories = await dynamoDbLib.call("scan", categoriesParams);
      let products = await dynamoDbLib.call("scan", productsParams);

      for (var x in products.Items) {
        let product_mva = products.Items[x].objectMVA
          ? products.Items[x].objectMVA
          : 0;
        let product_mva_account = null;
        if (product_mva == 0) {
          product_mva_account = poweroffice_0_mva_account;
        }
        if (product_mva == 15) {
          product_mva_account = poweroffice_15_mva_account;
        }
        if (product_mva == 25) {
          product_mva_account = poweroffice_25_mva_account;
        }

        let group_id = null;
        let category_name = "";
        for (var y in categories.Items) {
          if (
            categories.Items[y].objectId == products.Items[x].objectCategory
          ) {
            group_id = categories.Items[y].powerOfficeCategoryId;
            category_name = categories.Items[y].objectName;
          }
        }

        if (products.Items[x].powerOfficeProductId) {
          // product already pushed to poweroffice

          await poweroffice.updateProduct({
            id: products.Items[x].powerOfficeProductId,
            description: products.Items[x].objectDesc,
            isActive: true,
            name: products.Items[x].objectName + " - " + category_name,
            productGroupId: group_id,
            salesPrice: products.Items[x].objectPrice,
            costPrice: products.Items[x].objectCost
              ? products.Items[x].objectCost
              : null,
            salesAccount: product_mva_account,
          });
        } else {
          let product = await poweroffice.createProduct({
            description: products.Items[x].objectDesc,
            isActive: true,
            name: products.Items[x].objectName + " - " + category_name,
            productGroupId: group_id,
            salesPrice: products.Items[x].objectPrice,
            costPrice: products.Items[x].objectCost
              ? products.Items[x].objectCost
              : null,
            salesAccount: product_mva_account,
          });

          const result = await dynamoDbLib.call("update", {
            TableName: process.env.objectsTableName,

            Key: {
              objectCompanyId: company_id,
              objectId: products.Items[x].objectId,
            },

            UpdateExpression:
              "SET powerOfficeProductId = :powerOfficeProductId,powerOfficeProductCode = :powerOfficeProductCode",
            ExpressionAttributeValues: {
              ":powerOfficeProductId": product.data.data.id,
              ":powerOfficeProductCode": product.data.data.code,
            },
            ReturnValues: "ALL_NEW",
          });
        }
      }

      // set the shipping as products

      let shipping_product_id = null;
      let shipping_product_code = null;

      if (shops.Items[0].objectPowerOfficeShippingProductId) {
        shipping_product_id = shops.Items[0].objectPowerOfficeShippingProductId;
        shipping_product_code =
          shops.Items[0].objectPowerOfficeShippingProductCode;

        await poweroffice.updateProduct({
          id: shops.Items[0].objectPowerOfficeShippingProductId,
          description: "Shipping Cost",
          isActive: true,
          name: "Shipping Cost",
          //  productGroupId: product_group_id,
          salesPrice: shops.Items[0].shippingCost
            ? shops.Items[0].shippingCost
            : 0,
          salesAccount: poweroffice_25_mva_account,
        });
      } else {
        let product = await poweroffice.createProduct({
          description: "Shipping Cost",
          isActive: true,
          name: "Shipping Cost",
          //    productGroupId: product_group_id,
          salesPrice: shops.Items[0].shippingCost
            ? shops.Items[0].shippingCost
            : 0,
          salesAccount: poweroffice_25_mva_account,
        });

        shipping_product_id = product.data.data.id;
        shipping_product_code = product.data.data.code;

        const result = await dynamoDbLib.call("update", {
          TableName: process.env.objectsTableName,

          Key: {
            objectCompanyId: company_id,
            objectId: shop_id,
          },

          UpdateExpression:
            "SET objectPowerOfficeShippingProductId = :objectPowerOfficeShippingProductId,objectPowerOfficeShippingProductCode = :objectPowerOfficeShippingProductCode",
          ExpressionAttributeValues: {
            ":objectPowerOfficeShippingProductId": product.data.data.id,
            ":objectPowerOfficeShippingProductCode": product.data.data.code,
          },
          ReturnValues: "ALL_NEW",
        });
      }

      // end of products sync

      // customers sync

      let shops_permissions = await dynamoDbLib.call("scan", {
        TableName: process.env.userPermissionsTableName,
        FilterExpression:
          "grantedBy = :grantedBy and roleType = :roleType and isActive = :isActive and shopId = :shopId",
        ExpressionAttributeValues: {
          ":grantedBy": company_id,
          ":roleType": "shop_access_role",
          ":isActive": true,
          ":shopId": shop_id,
        },
      });

      let customers = [];

      let shopDeliveryPoints = await dynamoDbLib.call("scan", {
        TableName: process.env.objectsTableName,
        FilterExpression:
          "objectType = :objectType and objectShopId = :objectShopId",
        ExpressionAttributeValues: {
          ":objectType": "delivery_point",
          ":objectShopId": shop_id,
        },
      });

      for (var x in shops_permissions.Items) {
        let customer = await dynamoDbLib.call("scan", {
          TableName: process.env.objectsTableName,
          FilterExpression: "objectType = :objectType and objectId = :objectId",
          ExpressionAttributeValues: {
            ":objectType": "company",
            ":objectId": shops_permissions.Items[x].companyId,
          },
        });

        let pool_id = process.env.COGNITO_POOL_ID;
        let users = [];
        let permission_id = shops_permissions.Items[x].permissionId;
        let poweroffice_customer_id = shops_permissions.Items[x]
          .powerofficeCustomerId
          ? shops_permissions.Items[x].powerofficeCustomerId
          : null;

        let user_pool = await cognito_client
          .describeUserPool({
            UserPoolId: pool_id,
          })
          .promise();

        let number_of_users = user_pool.UserPool.EstimatedNumberOfUsers;

        let loops = Math.ceil(number_of_users / 60);

        let token = "";

        for (var i = 1; i <= loops; i++) {
          let ownersParams = {};

          if (token != "") {
            ownersParams = {
              UserPoolId: pool_id,
              AttributesToGet: null,
              Filter: 'name = "' + shops_permissions.Items[x].companyId + '"',
              Limit: 0,
              PaginationToken: token,
            };
          } else {
            ownersParams = {
              UserPoolId: pool_id,
              AttributesToGet: null,
              Filter: 'name = "' + shops_permissions.Items[x].companyId + '"',
              Limit: 0,
            };
          }

          let users_data = await cognito_client
            .listUsers(ownersParams)
            .promise();

          token = users_data.PaginationToken;

          for (var z in users_data.Users) {
            let user_type = "";
            let user_company_id = "";
            let email_verified = false;
            let owner_set = false;

            for (var y in users_data.Users[z].Attributes) {
              if (
                users_data.Users[z].Attributes[y].Name == "custom:user_type"
              ) {
                user_type = users_data.Users[z].Attributes[y].Value;
              }

              if (
                users_data.Users[z].Attributes[y].Name == "custom:company_id"
              ) {
                user_company_id = users_data.Users[z].Attributes[y].Value;
              }

              if (users_data.Users[z].Attributes[y].Name == "email_verified") {
                email_verified = users_data.Users[z].Attributes[y].Value;
              }
            }

            let contact_set = shops_permissions.Items[x].contactId
              ? true
              : false;

            if (
              !owner_set &&
              user_type == "owner" &&
              user_company_id == shops_permissions.Items[x].companyId
            ) {
              owner_set = true;
              let first_name = "";
              let last_name = "";
              let email = "";
              let phone = "";
              let address = customer.Items[0].objectAddress;

              let post_code = customer.Items[0].objectPostCode;
              let company_name = customer.Items[0].objectName;
              let organization_id = customer.Items[0].objectOrganizationId;
              let invoice_delivery_type = shops_permissions.Items[x]
                .invoice_delivery_type
                ? shops_permissions.Items[x].invoice_delivery_type
                : 1;
              let city = customer.Items[0].objectCity;
              let customer_company_id = "";

              if (invoice_delivery_type == "mail") {
                invoice_delivery_type = 1;
              }

              if (invoice_delivery_type == "ehf") {
                invoice_delivery_type = 3;
              }
              if (invoice_delivery_type == "avtale_giro") {
                invoice_delivery_type = 4;
              }

              for (var y in users_data.Users[z].Attributes) {
                if (
                  users_data.Users[z].Attributes[y].Name == "custom:first_name"
                ) {
                  first_name = users_data.Users[z].Attributes[y].Value;
                }
                if (
                  users_data.Users[z].Attributes[y].Name == "custom:last_name"
                ) {
                  last_name = users_data.Users[z].Attributes[y].Value;
                }
                if (
                  users_data.Users[z].Attributes[y].Name == "custom:user_phone"
                ) {
                  phone = users_data.Users[z].Attributes[y].Value;
                }
                if (
                  users_data.Users[z].Attributes[y].Name == "custom:post_code"
                ) {
                  //  post_code = users_data.Users[z].Attributes[y].Value
                }
                if (
                  users_data.Users[z].Attributes[y].Name == "custom:company_id"
                ) {
                  customer_company_id = users_data.Users[z].Attributes[y].Value;
                }
                if (
                  users_data.Users[z].Attributes[y].Name ==
                  "custom:company_name"
                ) {
                  //company_name = users_data.Users[z].Attributes[y].Value
                }
                if (
                  users_data.Users[z].Attributes[y].Name ==
                  "custom:organization_id"
                ) {
                  //organization_id = users_data.Users[z].Attributes[y].Value
                }
                if (users_data.Users[z].Attributes[y].Name == "custom:city") {
                  //city = users_data.Users[z].Attributes[y].Value
                }
                if (users_data.Users[z].Attributes[y].Name == "email") {
                  email = users_data.Users[z].Attributes[y].Value;
                }

                if (users_data.Users[z].Attributes[y].Name == "address") {
                  //address = users_data.Users[z].Attributes[y].Value
                }
              }

              // get customer delivery points

              let customer_address = [];

              const deliveryPointsPermissions = await dynamoDbLib.call("scan", {
                TableName: process.env.userPermissionsTableName,
                FilterExpression: "userId = :userId",
                ExpressionAttributeValues: {
                  ":userId": customer_company_id,
                },
              });

              for (var w in deliveryPointsPermissions.Items) {
                for (var z in shopDeliveryPoints.Items) {
                  if (
                    deliveryPointsPermissions.Items[w].locationId ==
                    shopDeliveryPoints.Items[z].objectId
                  ) {
                    if (
                      deliveryPointsPermissions.Items[w].powerofficeAddressId
                    ) {
                      customer_address.push({
                        Id: deliveryPointsPermissions.Items[w]
                          .powerofficeAddressId,
                        Address1: shopDeliveryPoints.Items[z].objectAddress,
                        CountryCode: "NO",
                        IsPrimary: false,
                      });
                    } else {
                      customer_address.push({
                        Address1: shopDeliveryPoints.Items[z].objectAddress,
                        CountryCode: "NO",
                        IsPrimary: false,
                      });
                    }
                  }
                }
              }

              for (var z in users_data.Users) {
                if (
                  users_data.Users[z].Username ==
                  shops_permissions.Items[x].contactId
                ) {
                  for (var y in users_data.Users[z].Attributes) {
                    if (
                      users_data.Users[z].Attributes[y].Name ==
                      "custom:first_name"
                    ) {
                      first_name = users_data.Users[z].Attributes[y].Value;
                    }
                    if (
                      users_data.Users[z].Attributes[y].Name ==
                      "custom:last_name"
                    ) {
                      last_name = users_data.Users[z].Attributes[y].Value;
                    }
                    if (
                      users_data.Users[z].Attributes[y].Name ==
                      "custom:user_phone"
                    ) {
                      phone = users_data.Users[z].Attributes[y].Value;
                    }

                    if (users_data.Users[z].Attributes[y].Name == "email") {
                      email = users_data.Users[z].Attributes[y].Value;
                    }
                  }
                }
              }

              // end get customer delivery points

              customers.push({
                first_name,
                last_name,
                phone,
                email,
                address,
                post_code,
                company_name,
                organization_id,
                city,
                customer_company_id,
                invoice_delivery_type,
                customer_address,
                poweroffice_customer_id,
                permission_id,
              });
            }
          }
        }
      }

      customers = removeDuplicates(customers, "customer_company_id");

      let poweroffice_customers = await poweroffice.getCustomers(
        "?$filter=IsArchived%20eq%20false"
      );

      for (var x in customers) {
        if (customers[x].poweroffice_customer_id != 16056071) {
          if (
            customers[x].poweroffice_customer_id &&
            customers[x].poweroffice_customer_id != 0
          ) {
            let contact = await poweroffice.getCustomerContactPerson(
              customers[x].poweroffice_customer_id
            );

            if (contact.data.length == 0) {
              await poweroffice.createCustomerContactPerson(
                customers[x].poweroffice_customer_id,
                {
                  FirstName: customers[x].first_name,
                  LastName: customers[x].last_name,
                  EmailAddress: customers[x].email,
                }
              );
            } else {
              await poweroffice.createCustomerContactPerson(
                customers[x].poweroffice_customer_id,
                {
                  id: contact.data[0].id,
                  FirstName: customers[x].first_name,
                  LastName: customers[x].last_name,
                  EmailAddress: customers[x].email,
                }
              );
            }

            let ccc = await poweroffice.updateCustomer({
              id: customers[x].poweroffice_customer_id,
              emailAddress: customers[x].email,
              firstName: customers[x].first_name,
              lastName: customers[x].last_name,
              invoiceDeliveryType: customers[x].invoice_delivery_type,
              invoiceEmailAddress: customers[x].email,
              isPerson: false,
              isArchived: false,
              legalName: customers[x].company_name,
              name: customers[x].company_name,
              phoneNumber: customers[x].phone,
              vatNumber: customers[x].organization_id,
              streetAddresses: customers[x].customer_address,
              mailAddress: {
                address1: customers[x].address,
                city: customers[x].city,
                zipCode: customers[x].post_code,
                countryCode: "NO",
              },
            });
          } else {
            let cus = await poweroffice.createCustomer({
              EmailAddress: customers[x].email,
              FirstName: customers[x].first_name,
              LastName: customers[x].last_name,
              InvoiceDeliveryType: customers[x].invoice_delivery_type,
              InvoiceEmailAddress: customers[x].email,
              IsPerson: false,
              IsArchived: false,
              LegalName: customers[x].company_name,
              Name: customers[x].company_name,
              PhoneNumber: customers[x].phone,
              VatNumber: customers[x].organization_id,
              streetAddresses: customers[x].customer_address,
              MailAddress: {
                Address1: customers[x].address,
                City: customers[x].city,
                ZipCode: customers[x].post_code,
                CountryCode: "NO",
              },
            });

            if (cus.data.data) {
              await poweroffice.createCustomerContactPerson(cus.data.data.id, {
                FirstName: customers[x].first_name,
                LastName: customers[x].last_name,
                EmailAddress: customers[x].email,
              });

              await dynamoDbLib.call("update", {
                TableName: process.env.userPermissionsTableName,

                Key: {
                  permissionId: customers[x].permission_id,
                },

                UpdateExpression:
                  "SET powerofficeCustomerId = :powerofficeCustomerId",
                ExpressionAttributeValues: {
                  ":powerofficeCustomerId": cus.data.data.id,
                },
                ReturnValues: "ALL_NEW",
              });
            }
          }
        }
      }

      poweroffice_customers = await poweroffice.getCustomers(
        "?$filter=IsArchived%20eq%20false"
      );

      for (var n in poweroffice_customers.data) {
        let streetAddresses = poweroffice_customers.data[n].streetAddresses;
        for (var k in customers) {
          for (var s in streetAddresses) {
            for (var z in shopDeliveryPoints.Items) {
              console.log("vat" + poweroffice_customers.data[n].vatNumber);

              if (
                poweroffice_customers.data[n].vatNumber ==
                customers[k].organization_id
              ) {
                let deliveryPointsPermission = await dynamoDbLib.call("scan", {
                  TableName: process.env.userPermissionsTableName,
                  FilterExpression:
                    "userId = :userId and locationId = :locationId",
                  ExpressionAttributeValues: {
                    ":userId": customers[k].customer_company_id,
                    ":locationId": shopDeliveryPoints.Items[z].objectId,
                  },
                });

                if (
                  streetAddresses[s].address1 ==
                    shopDeliveryPoints.Items[z].objectAddress &&
                  deliveryPointsPermission.Items.length > 0
                ) {
                  await dynamoDbLib.call("update", {
                    TableName: process.env.userPermissionsTableName,

                    Key: {
                      permissionId:
                        deliveryPointsPermission.Items[0].permissionId,
                    },

                    UpdateExpression:
                      "SET powerofficeAddressId = :powerofficeAddressId",
                    ExpressionAttributeValues: {
                      ":powerofficeAddressId": streetAddresses[s].id,
                    },
                    ReturnValues: "ALL_NEW",
                  });
                }
              }
            }
          }
        }
      }

      /*


      let dealdine_date = new Date();
      dealdine_date.setDate(dealdine_date.getDate() + orderDeadline);
      //dealdine_date.setHours(parseInt(orderDeadlineTime));
      dealdine_date.setHours(23);
      dealdine_date.setMinutes(0);
      dealdine_date.setSeconds(0);

      // send orders as invoices
      let ordersParams = {}
      if (migration_mode == 'all') {
        ordersParams = {
          TableName: process.env.cartTableName,
          FilterExpression: "schedule_type = :schedule_type and orderStatus <> :orderStatus and delivery_date < :delivery_date and shopId = :shopId and (attribute_not_exists(powerOfficeInvoiceId) or powerOfficeInvoiceId = :powerOfficeInvoiceId ) and (paymentMethod = :paymentMethod or PaymentMethodId = :PaymentMethodId)",
          ExpressionAttributeValues: {
            ":orderStatus": 'canceled',
            ":shopId": shop_id,
            ":paymentMethod": 'manual',
            ":PaymentMethodId": 'manual',
            ":schedule_type": 'once',
            ":powerOfficeInvoiceId": null,
            ":delivery_date": dealdine_date.getTime()

          }
        };
      } else {
        ordersParams = {
          TableName: process.env.cartTableName,
          FilterExpression: "schedule_type = :schedule_type and orderStatus <> :orderStatus and delivery_date < :delivery_date and shopId = :shopId and createdAt > :createdAt and (attribute_not_exists(powerOfficeInvoiceId) or powerOfficeInvoiceId = :powerOfficeInvoiceId ) and (paymentMethod = :paymentMethod or PaymentMethodId = :PaymentMethodId)",
          ExpressionAttributeValues: {
            ":orderStatus": 'canceled',
            ":shopId": shop_id,
            ":createdAt": migration_start_date,
            ":paymentMethod": 'manual',
            ":PaymentMethodId": 'manual',
            ":schedule_type": 'once',
            ":powerOfficeInvoiceId": null,
            ":delivery_date": dealdine_date.getTime()

          }
        };
      }

      let ordersResult = await dynamoDbLib.call("scan", ordersParams);

      for (var x in ordersResult.Items) {

        let invoice_lines = []
        let discount = (ordersResult.Items[x].discount) ? (ordersResult.Items[x].discount / 100) : 0
        console.log(ordersResult.Items[x].discount);
        let sort = 0
        for (var y in ordersResult.Items[x].items) {
          sort++
          let this_product = await dynamoDbLib.call("scan", {
            TableName: process.env.objectsTableName,
            FilterExpression: "objectType = :objectType and objectId = :objectId",
            ExpressionAttributeValues: {
              ":objectType": 'product',
              ":objectId": ordersResult.Items[x].items[y].id
            }
          });
          if (this_product.Items.length == 1) {

            let category_name = ''
            for (var z in categories.Items) {
              if (categories.Items[z].objectId == this_product.Items[0].objectCategory) {
                category_name = categories.Items[z].objectName

              }

            }

            invoice_lines.push({
              SortOrder: sort,
              //ExemptVat: (this_product.Items[0].objectMVA == 0) ? true : false,
              ProductCode: this_product.Items[0].powerOfficeProductCode,
              Description: this_product.Items[0].objectName + ' - ' + category_name,
              Quantity: ordersResult.Items[x].items[y].quantity,
              DiscountPercent: discount,
              UnitPrice: parseFloat(ordersResult.Items[x].items[y].price),
              NetAmount: parseFloat(ordersResult.Items[x].items[y].price) * ordersResult.Items[x].items[y].quantity
            })
          }

        }

        invoice_lines.push({
          SortOrder: invoice_lines.length + 1,
          ProductCode: shipping_product_code,
          Description: 'Shipping Cost',
          Quantity: 1,
          UnitPrice: parseFloat(ordersResult.Items[x].freight),
          TotalAmount: parseFloat(ordersResult.Items[x].freight),
          NetAmount: parseFloat(ordersResult.Items[x].freight),
          DiscountPercent: 0,
        })


        let this_comapny_id = ordersResult.Items[x].companyId
        let this_customer_id = null
        let address_id = null

        for (var z in customers) {

          if (customers[z].customer_company_id == this_comapny_id) {
            this_customer_id = customers[z].poweroffice_customer_id
          }
        }
        let customer_id = null
        let this_customer_code = null
        let this_customer_email = null
        let delivery_type = null
        let customer_refrence = ''
        let delivery_point = ordersResult.Items[x].delivery_point


        for (var n in poweroffice_customers.data) {
          if (poweroffice_customers.data[n].id == this_customer_id) {
            customer_id = poweroffice_customers.data[n].id
            this_customer_code = poweroffice_customers.data[n].code
            this_customer_email = poweroffice_customers.data[n].emailAddress
            delivery_type = poweroffice_customers.data[n].invoiceDeliveryType

            let poweroffice_customer_contact_persons = await poweroffice.getCustomerContactPerson(customer_id)
            if (poweroffice_customer_contact_persons.data.length > 0) {
              customer_refrence = poweroffice_customer_contact_persons.data[0].firstName + ' ' + poweroffice_customer_contact_persons.data[0].lastName
            }

            for (var k in poweroffice_customers.data[n].streetAddresses) {
              for (var l in shopDeliveryPoints.Items) {

                let this_delivery_point = null

                if (delivery_point == shopDeliveryPoints.Items[l].objectId && shopDeliveryPoints.Items[l].objectAddress == poweroffice_customers.data[n].streetAddresses[k].address1) {
                  address_id = poweroffice_customers.data[n].streetAddresses[k].id
                }

              }
            }


          }
        }

        console.log(this_customer_code);

        let order = await poweroffice.createInvoice({
          OrderDate: convertToDate(ordersResult.Items[x].createdAt),
          CustomerCode: this_customer_code,
          CustomerEmail: this_customer_email,
          CustomerReference: customer_refrence,
          DeliveryAddressId: address_id,
          DeliveryDate: convertToDate(ordersResult.Items[x].delivery_date + 10800000),
          InvoiceDeliveryType: delivery_type,
          //TotalAmount: ordersResult.Items[x].total,
          //NetAmount: ordersResult.Items[x].total,
          PurchaseOrderNo: ordersResult.Items[x].cartId,
          Status: 0,
          OutgoingInvoiceLines: invoice_lines
        })


        let updateOrderParams = {
          TableName: process.env.cartTableName,
          Key: {
            cartId: ordersResult.Items[x].cartId,
          },

          UpdateExpression: "SET powerOfficeInvoiceId = :powerOfficeInvoiceId,powerOfficeInvoiceIdentifier = :powerOfficeInvoiceIdentifier",
          ExpressionAttributeValues: {
            ":powerOfficeInvoiceId": order.data.data.orderNo,
            ":powerOfficeInvoiceIdentifier": order.data.data.id
          },
          ReturnValues: "ALL_NEW"
        };


        await dynamoDbLib.call("update", updateOrderParams);


      }

      */
    }

    console.log("DONE!");
  } catch (e) {
    //  bugsnagClient.notify(user_id, e)
    console.log("Hwllo");

    callback(
      null,
      failure({
        error: e,
      })
    );
  }
}

function removeDuplicates(myArr, prop) {
  return myArr.filter((obj, pos, arr) => {
    return arr.map((mapObj) => mapObj[prop]).indexOf(obj[prop]) === pos;
  });
}

function convertToDate(timestamp) {
  let date = new Date(timestamp);
  return (
    date.getFullYear() + "-" + (date.getMonth() + 1) + "-" + date.getDate()
  );
}
