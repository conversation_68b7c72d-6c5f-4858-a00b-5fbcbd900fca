import {
  success,
  failure
} from "../../libs/response-lib";
import * as dynamoDbLib from "../../libs/dynamodb-lib";
import * as bugsnagClient from "../../libs/bugsnag";
const AWS = require('aws-sdk')

const acm = new AWS.ACM({
  region: "us-east-1",
  accessKeyId: '********************',
  secretAccessKey: '6bB750d1OHP1Efz2f1AXrXO8kPZhAOpKf22xsyVk'
});

const cloudfront = new AWS.CloudFront({
  apiVersion: '2018-11-05',
  accessKeyId: '********************',
  secretAccessKey: '6bB750d1OHP1Efz2f1AXrXO8kPZhAOpKf22xsyVk',
});

export async function validate(event, context, callback) {

  let time_period_to = new Date().getTime() - (3 * 24 * 60 * 60 * 1000)


  const shops_result = await dynamoDbLib.call("scan", {
    TableName: process.env.objectsTableName,
    FilterExpression: "objectType = :objectType and domainStatus = :domainStatus and domainUpdatedAt > :domainUpdatedAt",
    ExpressionAttributeValues: {
      ":objectType": 'shop',
      ":domainStatus": 'submitted',
      ":domainUpdatedAt": time_period_to
    }
  });

  for (var x in shops_result.Items) {

    let shop = shops_result.Items[x]
    let domain_id = shops_result.Items[x].domainId


    const domain_result = await dynamoDbLib.call("scan", {
      TableName: process.env.domainsTableName,
      FilterExpression: "domainId = :domainId",
      ExpressionAttributeValues: {
        ":domainId": domain_id,
      }
    });

    let domain = domain_result.Items[0]

    let sslArn = (domain.sslArn) ? domain.sslArn : null
    let cloudFrontId = (domain.cloudFrontId) ? domain.cloudFrontId : null
    let sll_status = (domain.domainStatus) ? domain.domainStatus : null
    let shop_domain_name = (domain.generalDomainName) ? domain.generalDomainName : null

    console.log(shop_domain_name);

    let cert = await acm.describeCertificate({
      CertificateArn: sslArn
    }).promise()

    if (cert.Certificate.Status == 'ISSUED') {


      let cf = await cloudfront.getDistribution({
        Id: cloudFrontId
      }).promise()

      if (cf.Distribution.Status == 'Deployed') {

        let shop_cloudfront = await cloudfront.getDistributionConfig({
          Id: cloudFrontId
        }).promise();

        let ETag = shop_cloudfront.ETag

        let DistributionConfig = shop_cloudfront.DistributionConfig
        DistributionConfig.Aliases.Items = [shop_domain_name]
        DistributionConfig.Aliases.Quantity = 1
        DistributionConfig.ViewerCertificate.ACMCertificateArn = sslArn
        DistributionConfig.ViewerCertificate.Certificate = sslArn

        await cloudfront.updateDistribution({
          DistributionConfig: DistributionConfig,
          Id: cloudFrontId,
          IfMatch: ETag
        }).promise();

        const params = {
          TableName: process.env.objectsTableName,

          Key: {
            objectCompanyId: shop.objectCompanyId,
            objectId: shop.objectId
          },

          UpdateExpression: "SET domainStatus = :domainStatus",
          ExpressionAttributeValues: {

            ":domainStatus": 'completed'
          },
          ReturnValues: "ALL_NEW"
        };

        const result = await dynamoDbLib.call("update", params);

      }

    }

  }
  callback(null, success(true));

}