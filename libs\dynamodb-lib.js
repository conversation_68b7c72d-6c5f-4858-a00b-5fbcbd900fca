import AWS from "aws-sdk";
AWS.config.update({
  region: "eu-central-1"
});
const dynamoDb = new AWS.DynamoDB.DocumentClient();

export async function call(action, params) {


  if (action == 'scan') {

    let final_results = {}
    final_results.Items = []


    let result = await dynamoDb.scan(params).promise();
    final_results.Items = result.Items

    while (typeof result.LastEvaluatedKey != "undefined") {

      params.ExclusiveStartKey = result.LastEvaluatedKey;
      result = await dynamoDb.scan(params).promise();
      for (var x in result.Items) {
        final_results.Items.push(result.Items[x])
      }

    }

    return final_results

  } else {
    return dynamoDb[action](params).promise();
  }
}