import * as dynamoDbLib from "../../../libs/dynamodb-lib";
import * as bugsnagClient from "../../../libs/bugsnag";
import {
  success,
  failure
} from "../../../libs/response-lib";

export async function task(event, context, callback) {


  try {

    var user_id = event.requestContext.identity.cognitoAuthenticationProvider.split('CognitoSignIn:')[1]
    let task_id = event.pathParameters.id

    let params = {
      TableName: process.env.tasksSchedulesTableName,
      FilterExpression: "taskId = :taskId and taskStatus = :taskStatus",
      ExpressionAttributeValues: {
        ":taskId": task_id,
        ":taskStatus": 'open'
      }
    };

    const task = await dynamoDbLib.call("scan", params);
    callback(null, success(task.Items[0]))

  } catch (e) {
    bugsnagClient.notify(user_id, e)
    callback(null, failure({
      status: e
    }));
  }

}