import * as dynamoDbLib from "../../../libs/dynamodb-lib";
import * as bugsnagClient from "../../../libs/bugsnag";
import {
  success,
  failure
} from "../../../libs/response-lib";

import {
  is_level_permitted
} from "../../../libs/permissions";

const groupArray = require('group-array');
const arraySort = require('array-sort')
import unique from '@arr/unique';



export async function reports(event, context, callback) {


  try {
    let access_scope = ['owner', 'manager', 'employee']
    const data = JSON.parse(event.body);

    var user_id = event.requestContext.identity.cognitoAuthenticationProvider.split('CognitoSignIn:')[1]

    const permissions = await is_level_permitted(user_id, access_scope);

    if (permissions.level_allowed) {


      let week_day = new Date();

      let start_of_the_week_day_index = 1 // 1 means Monday
      let end_of_the_week_day_index = 7 // 7 means Sunday

      week_day.setDate(week_day.getDate() + (start_of_the_week_day_index - 7 - week_day.getDay()) % 7);
      let start_of_this_week_timestamp = new Date(week_day.getFullYear() + '-' + (week_day.getMonth() + 1) + '-' + week_day.getDate() + ' 00:00:01').getTime()

      week_day.setDate(week_day.getDate() + (end_of_the_week_day_index + 7 - week_day.getDay()) % 7);
      let end_of_this_week_timestamp = new Date(week_day.getFullYear() + '-' + (week_day.getMonth() + 1) + '-' + week_day.getDate() + ' 23:59:59').getTime()

      let start_of_today_timestamp = (new Date()).setHours(0, 0, 1)
      let end_of_today_timestamp = (new Date()).setHours(23, 59, 59)


      let from = ''
      let to = ''

      if (data.mode == 'today') {
        from = start_of_today_timestamp
        to = end_of_today_timestamp

      }

      if (data.mode == 'week') {

        from = start_of_this_week_timestamp
        to = end_of_this_week_timestamp

      }

      if (data.mode == 'all') {

        from = data.filters.from
        to = data.filters.to

      }

      let task_types = []
      let locations = []
      let users = []
      let deviatios = ''

      for (var x in data.filters.filters) {
        if (data.filters.filters[x].type.value == "taskTypes") {
          task_types.push(data.filters.filters[x].value.value)
        }
        if (data.filters.filters[x].type.value == "Locations") {
          locations.push(data.filters.filters[x].value.value)
        }
        if (data.filters.filters[x].type.value == "CompletedByList") {
          users.push(data.filters.filters[x].value.value)
        }
        if (data.filters.filters[x].type.value == "Deviations") {
          deviatios = data.filters.filters[x].value.value
        }
      }


      let reports_list = []



      const tasksLogsParams = {
        TableName: process.env.tasksLogTableName,
        FilterExpression: "companyId = :companyId and task_type <> :deviation_log and task_type <> :deviation_review and task_type <> :cooldown_log and task_type <> :warmkeeping_log and logged_at >= :logged_from and logged_at <= :logged_to",
        ExpressionAttributeValues: {
          ":companyId": permissions.user_company_id,
          ":deviation_log": 'deviation_log',
          ":deviation_review": 'deviation_review',
          ":cooldown_log": 'cooldown_log',
          ":warmkeeping_log": 'warmkeeping_log',
          ":logged_from": from,
          ":logged_to": to
        }
      };




      let logsResults = await dynamoDbLib.call("scan", tasksLogsParams);
      let logs = logsResults.Items

      let tasksLogsResult = []

      let logs1 = []

      if (users.length > 0) {

        for (var x in logs) {
          for (var y in users) {
            if (logs[x].taksLoggerId == users[y]) {
              logs1.push(logs[x])
            }
          }
        }
      } else {
        logs1 = logs
      }

      let logs2 = []

      if (locations.length > 0) {

        for (var x in logs1) {
          for (var y in locations) {
            if (logs1[x].locationId == locations[y]) {
              logs2.push(logs1[x])
            }
          }
        }
      } else {

        logs2 = logs1
      }

      let logs3 = []

      if (task_types.length > 0) {

        for (var x in logs2) {
          for (var y in task_types) {
            if (logs2[x].task_type == task_types[y]) {
              logs3.push(logs2[x])
            }
          }
        }
      } else {
        logs3 = logs2
      }


      tasksLogsResult = logs3

      for (var x in tasksLogsResult) {

        let task_schedule_id = tasksLogsResult[x].taskScheduleId

        let cooldown_log = null
        let warmkeeping_log = null

        if (tasksLogsResult[x].task_type == 'cooldown') {

          let coolDownLogsParams = {
            TableName: process.env.tasksLogTableName,
            FilterExpression: "companyId = :companyId and task_type = :cooldown_log  and taskId = :taskId",
            ExpressionAttributeValues: {
              ":companyId": permissions.user_company_id,
              ":cooldown_log": 'cooldown_log',
              ":taskId": tasksLogsResult[x].taskId
            }
          };

          let coolDownLogsResult = await dynamoDbLib.call("scan", coolDownLogsParams);
          cooldown_log = coolDownLogsResult.Items

          if (cooldown_log[0]) {
            task_schedule_id = cooldown_log[0].taskScheduleId
          }

        }

        if (tasksLogsResult[x].task_type == 'keepwarming') {
          let warmKeepingLogsParams = {
            TableName: process.env.tasksLogTableName,
            FilterExpression: "companyId = :companyId and task_type = :warmkeeping_log  and taskId = :taskId",
            ExpressionAttributeValues: {
              ":companyId": permissions.user_company_id,
              ":warmkeeping_log": 'warmkeeping_log',
              ":taskId": tasksLogsResult[x].taskId
            }
          };


          let warmKeepingLogsResult = await dynamoDbLib.call("scan", warmKeepingLogsParams);
          warmkeeping_log = warmKeepingLogsResult.Items

          if (arraySort(warmkeeping_log, 'logged_at')[0]) {
            task_schedule_id = arraySort(warmkeeping_log, 'logged_at').reverse()[0].taskScheduleId
          }

        }


        let taskDeviationParams = {
          TableName: process.env.tasksLogTableName,
          FilterExpression: "companyId = :companyId and (task_type = :deviation_log or task_type = :deviation_review) and taskDeviationFor = :taskDeviationFor",
          ExpressionAttributeValues: {
            ":companyId": permissions.user_company_id,
            ":deviation_log": 'deviation_log',
            ":deviation_review": 'deviation_review',
            ":taskDeviationFor": task_schedule_id
          }
        };

        let taskDeviationsResult = await dynamoDbLib.call("scan", taskDeviationParams);


        let deviation_log = null
        let deviation_review = null

        for (var z in taskDeviationsResult.Items) {
          if (taskDeviationsResult.Items[z].task_type == 'deviation_log') {
            deviation_log = taskDeviationsResult.Items[z]
          }
          if (taskDeviationsResult.Items[z].task_type == 'deviation_review') {
            deviation_review = taskDeviationsResult.Items[z]
          }
        }


        reports_list.push({
          type: tasksLogsResult[x].task_type,
          logged_at: tasksLogsResult[x].logged_at,
          logged_at_day: convert(tasksLogsResult[x].logged_at),
          logged_by: tasksLogsResult[x].taksLoggerId,
          name: tasksLogsResult[x].taskName,
          device_name: tasksLogsResult[x].deviceName,
          device_type: (tasksLogsResult[x].deviceType) ? tasksLogsResult[x].deviceType : null,
          logged_data: tasksLogsResult[x].logged_data,
          location_id: tasksLogsResult[x].locationId,
          log_id: tasksLogsResult[x].taskLogId,
          logData: (tasksLogsResult[x].logData) ? tasksLogsResult[x].logData : null,
          task_type: (tasksLogsResult[x].task_type) ? tasksLogsResult[x].task_type : null,
          cartId: (tasksLogsResult[x].cartId) ? tasksLogsResult[x].cartId : null,
          exception_id: tasksLogsResult[x].exception_id,
          deviation_log: deviation_log,
          deviation_review: deviation_review,
          cooldown_log: cooldown_log,
          warmkeeping_log: arraySort(warmkeeping_log, 'logged_at').reverse()
        })

      }

      let objects_ids = []
      let objects = []
      let reports_list_final = []

      if (deviatios == 'yes') {
        for (var x in reports_list) {
          if (reports_list[x].deviation_log != null) {
            reports_list_final.push(reports_list[x])
          }
        }
      } else if (deviatios == 'no') {
        for (var x in reports_list) {
          if (reports_list[x].deviation_log == null) {
            reports_list_final.push(reports_list[x])
          }
        }
      } else {
        reports_list_final = reports_list
      }


      reports_list = reports_list_final

      let all_reports = arraySort(reports_list, 'logged_at')



      for (var x in all_reports) {
        objects_ids.push(reports_list[x].location_id)
      }

      objects_ids = unique(objects_ids)

      for (var x in objects_ids) {

        var objectParms = {
          TableName: process.env.objectsTableName,
          FilterExpression: "objectId = :objectId",
          ExpressionAttributeValues: {
            ":objectId": objects_ids[x]
          }
        };


        let object = await dynamoDbLib.call("scan", objectParms);
        objects.push(object.Items[0])
      }


      for (var x in reports_list) {

        if (reports_list[x].logged_at <= end_of_today_timestamp && reports_list[x].logged_at >= start_of_today_timestamp) {

          reports_list[x].logging_period = 'today'

        } else if (reports_list[x].logged_at <= end_of_this_week_timestamp && reports_list[x].logged_at >= start_of_this_week_timestamp) {

          reports_list[x].logging_period = 'week'

        } else {


        }

      }

      let today_week_reports = arraySort(reports_list, 'logged_at')
      today_week_reports = groupArray(today_week_reports, 'logging_period', 'type', 'location_id', 'logged_at_day')

      all_reports = groupArray(all_reports, 'type', 'location_id', 'logged_at_day')

      callback(null, success({
        today_week_reports,
        all_reports,
        objects
      }));

    } else {

      callback(null, failure({
        status: 'you do not have access to this api call'
      }));

    }

  } catch (e) {
    bugsnagClient.notify(user_id, e)

    callback(null, failure({
      status: e
    }));
  }

  function convert(unixtimestamp) {
    var date = new Date(unixtimestamp);
    var year = date.getFullYear();
    var month = (date.getMonth() + 1 <= 9) ? '0' + (date.getMonth() + 1) : date.getMonth() + 1;
    var day = date.getDate();
    var convdataTime = day + '-' + month + '-' + year;
    return convdataTime
  }
}