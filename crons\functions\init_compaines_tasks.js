import uuid from "uuid";
import * as dynamoDbLib from "../../libs/dynamodb-lib";
import * as bugsnagClient from "../../libs/bugsnag";

const AWS = require("aws-sdk");

export async function init(event, context, callback) {
  let cognito_client = new AWS.CognitoIdentityServiceProvider();
  let pool_id = process.env.COGNITO_POOL_ID;

  console.log(pool_id);

  const SNS = new AWS.SNS();

  try {
    const compainesSarams = {
      TableName: process.env.objectsTableName,
      FilterExpression: "objectType = :objectType",
      ExpressionAttributeValues: {
        ":objectType": "company",
      },
    };

    const compaines = await dynamoDbLib.call("scan", compainesSarams);

    for (var x in compaines.Items) {
      await SNS.publish(
        {
          Message: compaines.Items[x].objectId,
          TopicArn: process.env.generateBillsSNSTopic,
        },
        function (err, data) {
          //console.log(data);
          if (err) {
            callback(null, err);
          }
          // sns sent. good job
        }
      );
    }

    const shopsSarams = {
      TableName: process.env.objectsTableName,
      FilterExpression: "objectType = :objectType",
      ExpressionAttributeValues: {
        ":objectType": "shop",
      },
    };

    const shops = await dynamoDbLib.call("scan", shopsSarams);

    for (var x in shops.Items) {
      await SNS.publish(
        {
          Message: shops.Items[x].objectId,
          TopicArn: process.env.generateRecurringOrdersSNSTopic,
        },
        function (err, data) {
          //console.log(data);
          if (err) {
            callback(null, err);
          }
          // sns sent. good job
        }
      );

      if (shops.Items[x].objectPowerOfficeIntegration) {
        await SNS.publish(
          {
            Message: shops.Items[x].objectId,
            TopicArn: process.env.generatePowerOfficeOrdersSNSTopic,
          },
          function (err, data) {
            //console.log(data);
            if (err) {
              callback(null, err);
            }
            // sns sent. good job
          }
        );
      }

      // collect patments
      let order_deadline = shops.Items[x].orderDeadline
        ? shops.Items[x].orderDeadline
        : 0;
      let cut_time =
        new Date().getTime() + order_deadline * 24 * 60 * 60 * 1000;

      let ordersParams = {
        TableName: process.env.cartTableName,
        FilterExpression:
          "delivery_date > :delivery_date and shopId = :shopId and paymentStatus =:paymentStatus and schedule_type <> :schedule_type and paymentMethod = :paymentMethod",
        ExpressionAttributeValues: {
          ":shopId": shops.Items[x].objectId,
          ":paymentStatus": "unpaid",
          ":schedule_type": "weekly",
          ":paymentMethod": "stripe",
          ":delivery_date": cut_time,
        },
      };

      let orders = await dynamoDbLib.call("scan", ordersParams);

      for (var y in orders.Items) {
        if (false) {
          await SNS.publish(
            {
              Message: orders.Items[y].cartId,
              TopicArn: process.env.handelRecurringOrdersPaymentsSNSTopic,
            },
            function (err, data) {
              //console.log(data);
              if (err) {
                callback(null, err);
              }
              // sns sent. good job
            }
          );
        }
      }
    }

    callback(null, "done");
  } catch (e) {
    bugsnagClient.notify(null, e);
    callback(null, e);
  }
}
