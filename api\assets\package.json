{"name": "serverless", "version": "1.0.0", "description": "", "main": "handler.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"aws-sdk": "^2.211.0", "@babel/core": "^7.12.0", "@babel/preset-env": "^7.12.0", "babel-loader": "^8.2.0", "serverless-webpack": "^5.1.0", "webpack": "^5.99.9", "webpack-node-externals": "^1.6.0"}, "dependencies": {"@bugsnag/js": "^6.1.0", "array-sort": "^1.0.0", "uuid": "^3.2.1"}}