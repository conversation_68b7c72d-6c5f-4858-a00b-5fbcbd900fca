{"name": "serverless", "version": "1.0.0", "description": "", "main": "handler.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"aws-sdk": "^2.211.0", "babel-core": "^5.8.38", "babel-loader": "^10.0.0", "babel-plugin-transform-runtime": "^6.23.0", "babel-preset-env": "^0.0.0", "babel-preset-stage-3": "^6.16.0", "serverless-webpack": "^5.1.0", "webpack": "^5.99.9", "webpack-node-externals": "^1.6.0"}, "dependencies": {"@bugsnag/js": "^6.1.0", "array-sort": "^1.0.0", "uuid": "^3.2.1"}}