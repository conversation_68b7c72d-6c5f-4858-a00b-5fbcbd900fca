import * as dynamoDbLib from "../../../libs/dynamodb-lib";
import * as bugsnagClient from "../../../libs/bugsnag";
import {
  success,
  failure
} from "../../../libs/response-lib";

import {
  is_level_permitted
} from "../../../libs/permissions";

const AWS = require('aws-sdk')
const cognito_client = new AWS.CognitoIdentityServiceProvider()

export async function cart(event, context, callback) {

  try {

    const access_scope = ['owner', 'manager', 'employee']

    var user_id = event.requestContext.identity.cognitoAuthenticationProvider.split('CognitoSignIn:')[1]
    let pool_id = process.env.COGNITO_POOL_ID

    const permissions = await is_level_permitted(user_id, access_scope);
    if (permissions.level_allowed) {

      let cart_id = event.pathParameters.id

      const params = {
        TableName: process.env.cartTableName,
        FilterExpression: "cartId = :cartId",
        ExpressionAttributeValues: {
          ":cartId": cart_id
        }
      };

      const result = await dynamoDbLib.call("scan", params);


      callback(null, success(result.Items[0]))

    } else {

      callback(null, failure({
        status: 'you do not have access to this api call'
      }));

    }

  } catch (e) {
    bugsnagClient.notify(user_id, e)
    callback(null, failure({
      status: e
    }));
  }

}