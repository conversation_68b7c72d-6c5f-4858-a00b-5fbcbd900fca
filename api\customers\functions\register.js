import {
  success,
  failure
} from "../../../libs/response-lib";


import {
  is_level_permitted
} from "../../../libs/permissions";

import * as dynamoDbLib from "../../../libs/dynamodb-lib";
import * as bugsnagClient from "../../../libs/bugsnag";
import uuid from "uuid";

const AWS = require('aws-sdk')
const SNS = new AWS.SNS();
const cognito_client = new AWS.CognitoIdentityServiceProvider()

export async function customer(event, context, callback) {

  try {

    const data = JSON.parse(event.body);

    var pool_id = process.env.COGNITO_POOL_ID

    let shop_id = data.shop_id
    let email = data.email
    let first_name = data.first_name
    let last_name = data.last_name
    let phone = data.phone
    let password = data.password
    let organization_name = (data.organization_name) ? data.organization_name : first_name + ' ' + last_name
    let organization_id = (data.organization_id) ? data.organization_id : null
    let address = data.address
    let post_code = data.post_code
    let floor_number = data.floor_number
    let allowed_payment_options = []


    const shopInfo = await dynamoDbLib.call("scan", {
      TableName: process.env.objectsTableName,
      FilterExpression: "objectType = :objectType and objectId = :objectId",
      ExpressionAttributeValues: {
        ":objectType": 'shop',
        ":objectId": shop_id
      }
    });

    let guestCheckout = (shopInfo.Items[0].guestCheckout) ? shopInfo.Items[0].guestCheckout : false
    let shopCompanyId = shopInfo.Items[0].objectCompanyId

    if (guestCheckout) {

      if (shopInfo.Items[0].objectPaymentMethod == 'stripe') {
        allowed_payment_options.push('credit_card');
      }
      if (shopInfo.Items[0].objectPaymentMethod == 'manual') {
        allowed_payment_options.push('manual');
      }

      let params = {
        UserPoolId: pool_id,
        Filter: "email = \"" + data.email + "\""
      };

      let new_user = await cognito_client.listUsers(params).promise()

      let new_user_id = ''
      let company_id = ''

      if (new_user.Users.length == 0) {

        company_id = uuid.v1()

        const companyParams = {
          TableName: process.env.objectsTableName,
          Item: {
            objectCreatedBy: shopCompanyId,
            objectCompanyId: company_id,
            objectId: company_id,
            objectName: organization_name,
            objectAddress: address,
            objectPostCode: post_code,
            objectCity: null,
            objectOrganizationId: organization_id,
            objectType: "company",
            createdAt: new Date().getTime()
          }
        };

        await dynamoDbLib.call("put", companyParams);
        let p1 = uuid.v1()
        let permissionParams = {

          TableName: process.env.userPermissionsTableName,
          Item: {
            grantedBy: shopCompanyId,
            companyId: company_id,
            roleType: 'shop_access_role',
            shopId: shop_id,
            discount: 0,
            shipping: null,
            powerofficeCustomerId: false,
            allow_volume_orders: false,
            allow_recurring_orders: false,
            invoice_delivery_type: 'mail',
            allowed_payment_options: allowed_payment_options,
            isActive: true,
            permissionId: p1,
            createdAt: new Date().getTime()
          }
        };

        await dynamoDbLib.call("put", permissionParams);

        params = {
          UserPoolId: pool_id,
          Username: data.email,
          ForceAliasCreation: false,
          UserAttributes: [{
              Name: 'custom:company_name',
              Value: organization_name
            },
            {
              Name: 'custom:organization_id',
              Value: "" + organization_id
            },
            {
              Name: 'custom:first_name',
              Value: first_name
            },
            {
              Name: 'custom:last_name',
              Value: last_name
            },
            {
              Name: 'custom:user_phone',
              Value: phone
            },
            {
              Name: 'custom:user_type',
              Value: 'owner'
            },
            {
              Name: 'name',
              Value: company_id
            },
            {
              Name: 'custom:company_id',
              Value: company_id
            },
            {
              Name: 'email_verified',
              Value: 'true'
            },
            {
              Name: 'custom:created_as',
              Value: 'company_customer'
            },
            {
              Name: 'email',
              Value: data.email
            },
            {
              Name: 'custom:language',
              Value: 'no'
            }
          ]
        };

        new_user = await cognito_client.adminCreateUser(params).promise()
        new_user_id = new_user.User.Username


        let params2 = {
          UserAttributes: [{
            Name: 'custom:company_id',
            Value: company_id
          }],
          UserPoolId: pool_id,
          Username: new_user_id
        };

        await cognito_client.adminUpdateUserAttributes(params2).promise()

        // set comapny id
        var params3 = {
          GroupName: 'Owner',
          UserPoolId: pool_id,
          Username: new_user_id
        };

        // add user to company group
        await cognito_client.adminAddUserToGroup(params3).promise()




        let location_id = uuid.v1()
        const locationParams = {
          TableName: process.env.objectsTableName,
          Item: {
            objectCreatedBy: new_user.User.Username,
            objectCompanyId: company_id,
            objectId: location_id,
            objectName: 'Location 1',
            //  objectLocation: '',
            objectType: "asset",
            createdAt: new Date().getTime()
          }
        };

        await dynamoDbLib.call("put", locationParams);

        const locationPermissionParams = {

          TableName: process.env.userPermissionsTableName,
          Item: {
            grantedBy: 'signup',
            userId: new_user_id,
            companyId: company_id,
            locationId: location_id,
            permissionId: uuid.v1(),
            createdAt: new Date().getTime()
          }
        };

        await dynamoDbLib.call("put", locationPermissionParams);

        await SNS.publish({
          Message: p1,
          TopicArn: process.env.generatePowerOfficeCustomersSNSTopic
        }, function(err, data) {

          if (err) {
            callback(null, err);
          }

        });


      } else {

        for (var x in new_user.Users[0].Attributes) {

          if (new_user.Users[0].Attributes[x].Name == 'custom:company_id') {
            company_id = new_user.Users[0].Attributes[x].Value
          }

        }

        let customerParams = {
          TableName: process.env.userPermissionsTableName,
          FilterExpression: "grantedBy = :grantedBy and roleType = :roleType and isActive = :isActive and companyId = :companyId and shopId = :shopId",
          ExpressionAttributeValues: {
            ":grantedBy": 'shop-register',
            ":roleType": 'shop_access_role',
            ":isActive": true,
            ":companyId": company_id,
            ":shopId": shop_id
          }
        };

        let customer = await dynamoDbLib.call("scan", customerParams)

        if (customer.Items.length == 0) {

          new_user_id = new_user.Users[0].Username
          let p2 = uuid.v1()

          let permissionParams = {

            TableName: process.env.userPermissionsTableName,
            Item: {
              grantedBy: 'shop-register',
              companyId: company_id,
              roleType: 'shop_access_role',
              discount: 0,
              shipping: false,

              allow_volume_orders: false,
              allow_recurring_orders: false,
              invoice_delivery_type: 'mail',
              allowed_payment_options: allowed_payment_options,
              shopId: shop_id,
              isActive: true,
              permissionId: p2,
              createdAt: new Date().getTime()
            }
          };

          await dynamoDbLib.call("put", permissionParams);

          await SNS.publish({
            Message: p2,
            TopicArn: process.env.generatePowerOfficeCustomersSNSTopic
          }, function(err, data) {

            if (err) {
              callback(null, err);
            }

          });

        } else {

          callback(null, failure({
            status: 'you already have relation with this customer'
          }));

        }
      }
      // create delivery point
      let delivery_point_id = uuid.v1();
      await dynamoDbLib.call("put", {
        TableName: process.env.objectsTableName,
        Item: {
          objectCreatedBy: new_user_id,
          objectCompanyId: shopCompanyId,
          objectId: delivery_point_id,
          objectName: organization_name,
          objectShopId: shop_id,
          objectAddress: address,
          objectFloor: floor_number,
          objectDesc: null,
          objectType: "delivery_point",
          createdAt: new Date().getTime()
        }
      });

      await dynamoDbLib.call("put", {

        TableName: process.env.userPermissionsTableName,
        Item: {
          grantedBy: new_user_id,
          userId: company_id,
          companyId: shopCompanyId,
          locationId: delivery_point_id,
          permissionId: uuid.v1(),
          createdAt: new Date().getTime()
        }
      });

      callback(null, success(company_id))


    } else {
      callback(null, failure({
        status: 'you already have relation with this customer'
      }));
    }



  } catch (e) {
    console.log(e);
    bugsnagClient.notify(e)
    callback(null, failure({
      error: e
    }))
  }

}