import uuid from "uuid";
import * as dynamoDbLib from "../../../libs/dynamodb-lib";
import * as bugsnagClient from "../../../libs/bugsnag";

import {
  success,
  failure
} from "../../../libs/response-lib";

import {
  is_level_permitted
} from "../../../libs/permissions";

export async function payment_method(event, context, callback) {

  try {
    const access_scope = ['owner', 'manager', 'employee']

    const data = JSON.parse(event.body);
    var user_id = event.requestContext.identity.cognitoAuthenticationProvider.split('CognitoSignIn:')[1]
    const permissions = await is_level_permitted(user_id, access_scope);
    if (permissions.level_allowed) {

      let params = {}
      let shop_id = event.pathParameters.id

      let stripe_integration_id = process.env.STRIPE_INTEGRATION_ID

      if (shop_id != 'kompis_app') {

        const shopInfo = await dynamoDbLib.call("scan", {
          TableName: process.env.objectsTableName,
          FilterExpression: "objectType = :objectType and objectId = :objectId",
          ExpressionAttributeValues: {
            ":objectType": 'shop',
            ":objectId": shop_id
          }
        });

        let stripe_integration_id = shopInfo.Items[0].objectStripeIntegration
      }

      params = {
        TableName: process.env.objectsTableName,
        FilterExpression: "objectType = :objectType and objectCompanyId = :objectCompanyId and objectIntegrationId = :objectIntegrationId",
        ExpressionAttributeValues: {
          ":objectType": 'payment_method',
          ":objectCompanyId": permissions.user_company_id,
          ":objectIntegrationId": stripe_integration_id
        }
      };

      const result = await dynamoDbLib.call("scan", params);


      for (var x in result.Items) {

        let is_defualt = false

        if (data.objectId == result.Items[x].objectId) {
          is_defualt = true
        }

        await dynamoDbLib.call("update", {
          TableName: process.env.objectsTableName,

          Key: {
            objectCompanyId: permissions.user_company_id,
            objectId: result.Items[x].objectId
          },

          UpdateExpression: "SET objectIsDefault = :objectIsDefault",
          ExpressionAttributeValues: {
            ":objectIsDefault": is_defualt
          },
          ReturnValues: "ALL_NEW"
        });
      }
      callback(null, success({
        status: true
      }));

    } else {

      callback(null, failure({
        status: 'you do not have access to this api call'
      }));

    }

  } catch (e) {
    bugsnagClient.notify(user_id, e)
    callback(null, failure({
      status: e
    }));
  }

}