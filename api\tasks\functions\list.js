import * as dynamoDbLib from "../../../libs/dynamodb-lib";
import * as bugsnagClient from "../../../libs/bugsnag";
import {
  success,
  failure
} from "../../../libs/response-lib";

import {
  is_level_permitted
} from "../../../libs/permissions";

var arraySort = require('array-sort')

export async function tasks(event, context, callback) {

  try {

    const access_scope = ['owner', 'manager', 'employee']
    const data = JSON.parse(event.body);
    var user_id = event.requestContext.identity.cognitoAuthenticationProvider.split('CognitoSignIn:')[1]

    const permissions = await is_level_permitted(user_id, access_scope);
    if (permissions.level_allowed) {

      let task_types = []
      let repeat_types = []
      let task_statues = []
      let created_by = []
      let assigned_to = []
      let task_locations = []


      let queryExpression = "companyId = :companyId and taskType <> :taskType_1 and taskType <> :taskType_2 and taskType <> :taskType_3 and taskType <> :taskType_4 and taskType <> :taskType_5 and taskType <> :taskType_6 and taskType <> :taskType_7 and taskType <> :taskType_8"
      let queryExpressionValues = {
        ":companyId": permissions.user_company_id,
        ":taskType_1": 'deviation_review',
        ":taskType_2": 'manufacturer',
        ":taskType_3": 'deviation_log',
        ":taskType_4": 'ship',
        ":taskType_5": 'package',
        ":taskType_6": 'delivery',
        ":taskType_7": 'warmkeeping',
        ":taskType_8": 'cooldown'
      }


      for (var x in data.filters) {

        if (data.filters[x].type.value == 'taskTypes') {
          task_types.push(data.filters[x].value.value)
        }
        if (data.filters[x].type.value == 'repeatTypes') {
          repeat_types.push(data.filters[x].value.value)
        }
        if (data.filters[x].type.value == 'statusTypes') {
          task_statues.push(data.filters[x].value.value)
        }
        if (data.filters[x].type.value == 'createdBy') {
          created_by.push(data.filters[x].value.value)
        }
        if (data.filters[x].type.value == 'assignedTo') {
          assigned_to.push(data.filters[x].value.value)
        }
        if (data.filters[x].type.value == 'locations') {
          task_locations.push(data.filters[x].value.value)
        }
      }

      if (assigned_to.length > 0) {

        queryExpression += ' and ( '

        for (var x in assigned_to) {
          if (assigned_to[x] == 'anyone' || assigned_to[x] == 'any-owner' || assigned_to[x] == 'any-manager' || assigned_to[x] == 'any-employee') {

            queryExpression += ' assignees = :assignees_' + x
            queryExpressionValues[':assignees_' + x] = assigned_to[x]

          } else {
            queryExpression += ' contains (assignees, :assignees_' + x + ')'
            queryExpressionValues[':assignees_' + x] = assigned_to[x]
          }

          if (x < assigned_to.length - 1) {
            queryExpression += ' or '
          }
        }

        queryExpression += ' )'

      }


      if (task_types.length > 0) {

        queryExpression += ' and ( '

        for (var x in task_types) {
          queryExpression += ' taskType = :taskType_' + x
          queryExpressionValues[':taskType_' + x] = task_types[x]
          if (x < task_types.length - 1) {
            queryExpression += ' or '
          }
        }

        queryExpression += ' )'

      }

      if (repeat_types.length > 0) {

        queryExpression += ' and ( '

        for (var x in repeat_types) {
          queryExpression += ' taskRepeat = :taskRepeat_' + x
          queryExpressionValues[':taskRepeat_' + x] = repeat_types[x]
          if (x < repeat_types.length - 1) {
            queryExpression += ' or '
          }
        }

        queryExpression += ' )'

      }

      if (task_statues.length > 0) {

        queryExpression += ' and ( '

        for (var x in task_statues) {
          queryExpression += ' taskStatus = :taskStatus_' + x
          queryExpressionValues[':taskStatus_' + x] = task_statues[x]
          if (x < task_statues.length - 1) {
            queryExpression += ' or '
          }
        }

        queryExpression += ' )'

      }

      if (created_by.length > 0) {

        queryExpression += ' and ( '

        for (var x in created_by) {
          queryExpression += ' assignerId = :assignerId_' + x
          queryExpressionValues[':assignerId_' + x] = created_by[x]
          if (x < created_by.length - 1) {
            queryExpression += ' or '
          }
        }

        queryExpression += ' )'

      }

      if (task_locations.length > 0) {

        queryExpression += ' and ( '

        for (var x in task_locations) {
          queryExpression += ' locationId = :locationId_' + x
          queryExpressionValues[':locationId_' + x] = task_locations[x]
          if (x < task_locations.length - 1) {
            queryExpression += ' or '
          }
        }

        queryExpression += ' )'

      }



      const params = {
        TableName: process.env.tasksTableName,
        FilterExpression: queryExpression,
        ExpressionAttributeValues: queryExpressionValues
      };

      const result = await dynamoDbLib.call("scan", params);

      callback(null, success(result.Items));

    } else {

      callback(null, failure({
        status: 'you do not have access to this api call'
      }));

    }

  } catch (e) {
    bugsnagClient.notify(user_id, e)
    callback(null, failure({
      status: e
    }));
  }
}