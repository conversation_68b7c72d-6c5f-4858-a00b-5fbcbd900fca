import {
  success,
  failure
} from "../../../libs/response-lib";

import {
  is_level_permitted
} from "../../../libs/permissions";
import * as bugsnagClient from "../../../libs/bugsnag";

const AWS = require('aws-sdk')
const cognito_client = new AWS.CognitoIdentityServiceProvider()

export async function user(event, context, callback) {

  try {
    let access_scope = ['owner', 'manager', 'employee']
    const data = JSON.parse(event.body);
    let user_id = event.requestContext.identity.cognitoAuthenticationProvider.split('CognitoSignIn:')[1]

    const permissions = await is_level_permitted(user_id, access_scope);

    if (permissions.level_allowed) {


      var pool_id = process.env.COGNITO_POOL_ID

      var params = {
        UserPoolId: pool_id,
        Username: data.email,
        TemporaryPassword: makeid(8),
        //DesiredDeliveryMediums: ['EMAIL'],
        MessageAction: 'RESEND',
        ForceAliasCreation: false,
      };

      await cognito_client.adminCreateUser(params).promise()

      callback(null, success('done'))

    } else {

      callback(null, failure({
        status: 'you do not have access to this api call'
      }));

    }

  } catch (e) {
    bugsnagClient.notify(user_id, e)
    callback(null, failure({
      status: e
    }));
  }

}

function makeid(length) {
  var result = '';
  var characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  var charactersLength = characters.length;
  for (var i = 0; i < length; i++) {
    result += characters.charAt(Math.floor(Math.random() * charactersLength));
  }
  return result;
}