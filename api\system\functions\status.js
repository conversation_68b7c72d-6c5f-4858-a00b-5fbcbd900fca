import * as dynamoDbLib from "../../../libs/dynamodb-lib";
import {
  success,
  failure
} from "../../../libs/response-lib";

import {
  is_level_permitted
} from "../../../libs/permissions";

export async function status(event, context, callback) {

  const access_scope = ['owner', 'manager', 'employee']

  var user_id = event.requestContext.identity.cognitoAuthenticationProvider.split('CognitoSignIn:')[1]
  var states = []

  const data = JSON.parse(event.body);

  var asset_id = data.assetId
  var device_id = data.deviceId


  try {

    const permissions = await is_level_permitted(user_id, access_scope);
    if (permissions.level_allowed) {

      // get status for locations view

      if (asset_id == null && device_id == null) {


        var params = {
          TableName: process.env.objectsTableName,
          //KeyConditionExpression: "userId = :userId",
          FilterExpression: "objectType = :objectType and objectCompanyId = :objectCompanyId",
          ExpressionAttributeValues: {
            ":objectCompanyId": permissions.user_company_id,
            ":objectType": 'device'
          }
        };


        var devicesResult = await dynamoDbLib.call("scan", params);
        var devices = devicesResult.Items;

        for (var key in devices) {

          var sensorParam = {
            TableName: process.env.objectsTableName,
            //  KeyConditionExpression: "userId = :userId",
            FilterExpression: "objectType = :objectType and objectParent = :objectParent",
            ExpressionAttributeValues: {
              //  ":userId": user_id,
              ":objectType": 'sensor',
              ":objectParent": devices[key].objectId
            }
          };


          let sensorsResult = await dynamoDbLib.call("scan", sensorParam);
          var sensors = sensorsResult.Items;

          for (var skey in sensors) {

            let sensorId = sensors[skey].objectId

            var sensorDataParam = {
              TableName: process.env.sensorsDataLogsTableName,
              ScanIndexForward: false,
              Limit: 1,
              KeyConditionExpression: "sensorId = :sensorId",
              ExpressionAttributeValues: {
                ":sensorId": sensorId
              }
            };

            let sensorsDataResult = await dynamoDbLib.call("query", sensorDataParam);
            let sensorFinalState = sensorsDataResult.Items[0].sensorData.state.reported.temp


            if (devices[key].objectKind == 'refrigerator') {

              if (sensorFinalState < 0) {
                states.push('danger')
              } else if (sensorFinalState >= 0 && sensorFinalState <= 4) {
                states.push('normal')
              } else {
                states.push('danger')
              }

            } else if (devices[key].objectKind == 'freezer') {

              if (sensorFinalState <= -18) {
                states.push('normal')
              } else {
                states.push('danger')
              }
            }

          }

        }
      }

      // get status for devices view
      else if (asset_id != null && device_id == null) {


        var params = {
          TableName: process.env.objectsTableName,
          //  KeyConditionExpression: "userId = :userId",
          FilterExpression: "objectType = :objectType and objectParent = :objectParent",
          ExpressionAttributeValues: {
            //  ":userId": user_id,
            ":objectType": 'device',
            ":objectParent": asset_id
          }
        }

        var devicesResult = await dynamoDbLib.call("scan", params);
        var devices = devicesResult.Items;

        for (var key in devices) {

          var sensorParam = {
            TableName: process.env.objectsTableName,
            //  KeyConditionExpression: "userId = :userId",
            FilterExpression: "objectType = :objectType and objectParent = :objectParent",
            ExpressionAttributeValues: {
              //  ":userId": user_id,
              ":objectType": 'sensor',
              ":objectParent": devices[key].objectId
            }
          };


          let sensorsResult = await dynamoDbLib.call("scan", sensorParam);
          var sensors = sensorsResult.Items;

          for (var skey in sensors) {

            let sensorId = sensors[skey].objectId

            var sensorDataParam = {
              TableName: process.env.sensorsDataLogsTableName,
              ScanIndexForward: false,
              Limit: 1,
              KeyConditionExpression: "sensorId = :sensorId",
              ExpressionAttributeValues: {
                ":sensorId": sensorId
              }
            };

            let sensorsDataResult = await dynamoDbLib.call("query", sensorDataParam);
            let sensorFinalState = sensorsDataResult.Items[0].sensorData.state.reported.temp


            if (devices[key].objectKind == 'refrigerator') {

              if (sensorFinalState < 0) {
                states.push('danger')
              } else if (sensorFinalState >= 0 && sensorFinalState <= 4) {
                states.push('normal')
              } else {
                states.push('danger')
              }

            } else if (devices[key].objectKind == 'freezer') {

              if (sensorFinalState <= -18) {
                states.push('normal')
              } else {
                states.push('danger')
              }
            }

          }

        }

      } else if (asset_id == null && device_id != null) {

        var params = {
          TableName: process.env.objectsTableName,
          //  KeyConditionExpression: "userId = :userId  and objectId = :objectId",
          FilterExpression: "objectType = :objectType and objectCompanyId = :objectCompanyId",
          ExpressionAttributeValues: {
            ":objectCompanyId": permissions.user_company_id,
            ":objectType": 'device',
            ":objectId": device_id
          }
        };
        var devicesResult = await dynamoDbLib.call("scan", params);
        var deviceData = devicesResult.Items[0];

        var sensorParam = {
          TableName: process.env.objectsTableName,
          //  KeyConditionExpression: "userId = :userId",
          FilterExpression: "objectType = :objectType and objectParent = :objectParent",
          ExpressionAttributeValues: {
            //  ":userId": user_id,
            ":objectType": 'sensor',
            ":objectParent": device_id
          }
        };


        let sensorsResult = await dynamoDbLib.call("scan", sensorParam);
        var sensors = sensorsResult.Items;

        for (var skey in sensors) {

          let sensorId = sensors[skey].objectId

          var sensorDataParam = {
            TableName: process.env.sensorsDataLogsTableName,
            ScanIndexForward: false,
            Limit: 1,
            KeyConditionExpression: "sensorId = :sensorId",
            ExpressionAttributeValues: {
              ":sensorId": sensorId
            }
          };

          let sensorsDataResult = await dynamoDbLib.call("query", sensorDataParam);
          let sensorFinalState = sensorsDataResult.Items[0].sensorData.state.reported.temp


          if (deviceData.objectKind == 'refrigerator') {

            if (sensorFinalState < 0) {
              states.push('danger')
            } else if (sensorFinalState >= 0 && sensorFinalState <= 4) {
              states.push('normal')
            } else {
              states.push('danger')
            }

          } else if (deviceData.objectKind == 'freezer') {

            if (sensorFinalState <= -18) {
              states.push('normal')
            } else {
              states.push('danger')
            }
          }

        }


      } else {

      }

      var finalState = 'normal'

      for (var i = 0; i < states.length; i++) {
        if (states[i] == 'danger') {
          finalState = 'danger'
        }
      }



      var assetsParams = {
        TableName: process.env.objectsTableName,
        //KeyConditionExpression: "userId = :userId",
        FilterExpression: "objectType = :objectType and objectCompanyId = :objectCompanyId",
        ExpressionAttributeValues: {
          ":objectCompanyId": permissions.user_company_id,
          ":objectType": 'asset'
        }
      };

      var assetsResult = await dynamoDbLib.call("scan", assetsParams);
      var assets = assetsResult.Items;

      callback(null, success({
        state: finalState,
        assets: assets
      }));

    } else {

      callback(null, failure({
        status: 'you do not have access to this api call'
      }));

    }
  } catch (e) {
    callback(null, failure({
      status: e
    }));
  }
}