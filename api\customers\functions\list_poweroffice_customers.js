import {
  success,
  failure
} from "../../../libs/response-lib";

import {
  is_level_permitted
} from "../../../libs/permissions";

import * as dynamoDbLib from "../../../libs/dynamodb-lib";
import * as bugsnagClient from "../../../libs/bugsnag";
import PowerOffice from "../../../libs/poweroffice";

export async function customers(event, context, callback) {

  let access_scope = ['owner', 'manager', 'employee']
  let user_id = event.requestContext.identity.cognitoAuthenticationProvider.split('CognitoSignIn:')[1]
  //let user_id = "62f6d0d4-4d4c-4ce3-82c9-0d92b4b21e3e"

  try {

    const permissions = await is_level_permitted(user_id, access_scope);

    if (permissions.level_allowed) {

      let shop_id = event.pathParameters.id
      //let shop_id = '58340300-2eb6-11e9-96c1-f3dbad797ef9'


      const shopsSarams = {
        TableName: process.env.objectsTableName,
        FilterExpression: "objectType = :objectType and objectId = :objectId",
        ExpressionAttributeValues: {
          ":objectType": 'shop',
          ":objectId": shop_id
        }
      };

      const shops = await dynamoDbLib.call("scan", shopsSarams);



      if (shops.Items[0].objectPowerOfficeIntegration) {

        const integrationParams = {
          TableName: process.env.integrationsTableName,
          FilterExpression: "integrationId = :integrationId",
          ExpressionAttributeValues: {
            ":integrationId": shops.Items[0].objectPowerOfficeIntegration
          }
        };

        const integrations = await dynamoDbLib.call("scan", integrationParams);
        let poweroffice_client_key = integrations.Items[0].integrationPOAppKey
        let poweroffice_secrect_key = integrations.Items[0].integrationPOClientKey

        let poweroffice = await new PowerOffice(process.env.POWEROFFICE_APP_KEY, poweroffice_secrect_key)

        let poweroffice_customers = await poweroffice.getCustomers("?$filter=IsArchived%20eq%20false")
        //let poweroffice_customers = await poweroffice.getCustomers()

        let customers = []

        for (var x in poweroffice_customers.data) {
          customers.push({
            id: poweroffice_customers.data[x].id,
            code: poweroffice_customers.data[x].code,
            name: poweroffice_customers.data[x].name
          })
        }

        callback(null, success({
          poweroffice: true,
          customers: customers
        }))

      } else {
        callback(null, success({
          poweroffice: false
        }))
      }


    } else {

      callback(null, failure({
        status: 'you do not have access to this api call'
      }));

    }

  } catch (e) {
    bugsnagClient.notify(user_id, e)
    callback(null, failure({
      status: e
    }));
  }

}