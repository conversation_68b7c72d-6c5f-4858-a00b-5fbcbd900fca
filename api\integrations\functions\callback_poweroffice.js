import uuid from "uuid";
import * as dynamoDbLib from "../../../libs/dynamodb-lib";
import * as bugsnagClient from "../../../libs/bugsnag";

import {
  success,
  failure
} from "../../../libs/response-lib";


export async function integration(event, context, callback) {

  try {

    const data = JSON.parse(event.body);

    const integration_id = event.pathParameters.id

    if (data.success) {

      await dynamoDbLib.call("update", {
        TableName: process.env.integrationsTableName,

        Key: {
          integrationId: integration_id
        },

        UpdateExpression: "SET integrationPOClientKey = :integrationPOClientKey, client_organization_number = :client_organization_number, client_name = :client_name, integrationStatus = :integrationStatus",
        ExpressionAttributeValues: {
          ":integrationPOClientKey": data.client_key,
          ":client_organization_number": data.client_organization_number ? data.client_organization_number : null,
          ":client_name": data.client_name ? data.client_name : null,
          ":integrationStatus": 'completed'
        },
        ReturnValues: "ALL_NEW"
      });


    } else {


      await dynamoDbLib.call("update", {
        TableName: process.env.integrationsTableName,

        Key: {
          integrationId: integration_id
        },
        UpdateExpression: "SET reason = :reason, reasonCode = :reasonCode, integrationStatus = :integrationStatus",
        ExpressionAttributeValues: {
          ":reason": data.reason,
          ":reasonCode": data.reasonCode ? data.reasonCode : null,
          ":integrationStatus": 'faild'
        },
        ReturnValues: "ALL_NEW"
      });

    }

    callback(null, success('done'));


  } catch (e) {
    console.log(e);
    bugsnagClient.notify(user_id, e)

    callback(null, failure({
      status: e
    }));
  }

}