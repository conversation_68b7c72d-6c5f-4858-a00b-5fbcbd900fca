import * as dynamoDbLib from "../../../libs/dynamodb-lib";
import * as bugsnagClient from "../../../libs/bugsnag";
import {
  success,
  failure
} from "../../../libs/response-lib";

import {
  is_level_permitted
} from "../../../libs/permissions";

const AWS = require('aws-sdk')
const cognito_client = new AWS.CognitoIdentityServiceProvider()
var arraySort = require('array-sort')
export async function check(event, context, callback) {

  try {

    const access_scope = ['owner', 'manager', 'employee']
    const data = JSON.parse(event.body);


    var user_id = event.requestContext.identity.cognitoAuthenticationProvider.split('CognitoSignIn:')[1]

    let pool_id = process.env.COGNITO_POOL_ID

    const permissions = await is_level_permitted(user_id, access_scope);
    if (permissions.level_allowed) {

      let schedule_dates = data.schedule_dates
      let schedule_duration_type = data.schedule_duration_type
      let tzo = data.tzo
      let shop_id = event.pathParameters.id
      let company_id = permissions.user_company_id

      let weekly_orders = await dynamoDbLib.call("scan", {
        TableName: process.env.cartTableName,
        FilterExpression: "shopId = :shopId and companyId = :companyId and orderStatus <> :orderStatus and schedule_type = :schedule_type and attribute_not_exists(parent_order)",
        ExpressionAttributeValues: {
          ":shopId": shop_id,
          ":schedule_type": 'weekly',
          ":orderStatus": 'canceled',
          ":companyId": company_id

        }
      });

      let orders_exsits = false

      for (var x in weekly_orders.Items) {

        for (var y in weekly_orders.Items[x].schedule_dates) {

          for (var z in schedule_dates) {

            if (schedule_dates[z] == weekly_orders.Items[x].schedule_dates[y]) {
              orders_exsits = true
            }

          }

        }

      }




      // get next delivery date2

      const shopParams = {
        TableName: process.env.objectsTableName,
        FilterExpression: "objectType = :objectType and objectId = :objectId",
        ExpressionAttributeValues: {
          ":objectType": 'shop',
          ":objectId": shop_id
        }
      };

      const shopResult = await dynamoDbLib.call("scan", shopParams);
      let shopDetails = shopResult.Items[0]
      let orderDeadline = (shopDetails.orderDeadline) ? parseInt(shopDetails.orderDeadline) : 0
      let orderDeadlineTime = (shopDetails.orderDeadlineTime) ? parseInt(shopDetails.orderDeadlineTime) : 24


      let scheduled_orders_list = []
      let weeks_array = [7, 14, 21, 28] // for 1 and 2 weeks.

      for (var y in schedule_dates) {
        for (var w in weeks_array) {

          let schedule_day = parseInt(schedule_dates[y])

          let schedule_date = nextDay(schedule_day, weeks_array[w])
          //schedule_date.setDate(schedule_date.getDate() + (schedule_day + weeks_array[w] - schedule_date.getDay()) % weeks_array[w]);

          let day = nextDay(schedule_day, weeks_array[w])
          //day.setDate(day.getDate() + (schedule_day + weeks_array[w] - day.getDay()) % weeks_array[w]);

          let order_time_stamp = new Date(day.getFullYear() + '-' + (day.getMonth() + 1) + '-' + day.getDate())

          let today = new Date();
          let current_hour = today.getHours()

          const diffDays = Math.round(((schedule_date - today) / (24 * 60 * 60 * 1000)));

          if ((diffDays > orderDeadline) || ((diffDays == orderDeadline) && (current_hour < (orderDeadlineTime - (tzo / 60) - 1)))) {

            let timestamp = schedule_date.getTime()

            var days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
            var d = new Date(schedule_date);
            var dayName = days[d.getDay()];
            var date = d.getFullYear() + '-' + pad((d.getMonth() + 1), 2) + '-' + pad(d.getDate(), 2)


            scheduled_orders_list.push({
              text: dayName + ' - ' + date,
              value: timestamp

            })

          }
        }
      }

      scheduled_orders_list = arraySort(scheduled_orders_list, 'value')

      callback(null, success({
        orders_exsits,
        scheduled_orders_list
      }));

    } else {

      callback(null, failure({
        status: 'you do not have access to this api call'
      }));

    }

  } catch (e) {
    console.log(e);
    bugsnagClient.notify(user_id, e)
    callback(null, failure({
      status: e
    }));
  }

}

function nextDay(x, a) {
  var now = new Date();
  now.setDate(now.getDate() + (x + (7 - now.getDay())) % 7);
  now.setDate(now.getDate() + (a - 7));
  return now
}

function pad(num, size) {
  var s = num + "";
  while (s.length < size) s = "0" + s;
  return s;
}