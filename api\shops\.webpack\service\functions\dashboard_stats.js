!function(e,t){for(var r in t)e[r]=t[r]}(exports,function(e){var t={};function r(n){if(t[n])return t[n].exports;var a=t[n]={i:n,l:!1,exports:{}};return e[n].call(a.exports,a,a.exports,r),a.l=!0,a.exports}return r.m=e,r.c=t,r.d=function(e,t,n){r.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:n})},r.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},r.t=function(e,t){if(1&t&&(e=r(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var n=Object.create(null);if(r.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var a in e)r.d(n,a,function(t){return e[t]}.bind(null,a));return n},r.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return r.d(t,"a",t),t},r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r.p="",r(r.s=3)}([function(e,t){e.exports=require("aws-sdk")},function(e,t,r){"use strict";r.r(t),r.d(t,"call",function(){return o});var n=r(0),a=r.n(n);a.a.config.update({region:"eu-central-1"});const s=new a.a.DynamoDB.DocumentClient;async function o(e,t){if("scan"==e){let e={Items:[]},n=await s.scan(t).promise();for(e.Items=n.Items;void 0!==n.LastEvaluatedKey;)for(var r in t.ExclusiveStartKey=n.LastEvaluatedKey,(n=await s.scan(t).promise()).Items)e.Items.push(n.Items[r]);return e}return s[e](t).promise()}},function(e,t){e.exports=require("@bugsnag/js")},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.list=void 0;var n=d(r(4)),a=d(r(5)),s=d(r(6)),o=(t.list=function(){var e=(0,s.default)(n.default.mark(function e(t,r,s){var c,d,y,g,w,x,O,j,T,D,N,A,E,M,F,S,P,k,q,C,U,V,B,W,Y,z,K,G,H,L,J,Q,R,X,Z,$,ee,te,re,ne,ae,se,oe,ue,ie,le,ce,de,pe,me,fe,ye,_e,Ie,be,ve,he,ge,we,xe,Oe,je,Te,De,Ne;return n.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,c=["owner","manager","employee"],d=t.requestContext.identity.cognitoAuthenticationProvider.split("CognitoSignIn:")[1],y=JSON.parse(t.body),e.next=6,(0,l.is_level_permitted)(d,c);case 6:if(!(g=e.sent).level_allowed){e.next=90;break}return w=y.period,x=y.shop_id,e.next=12,o.call("scan",{TableName:process.env.objectsTableName,FilterExpression:"objectType = :objectType and objectCompanyId = :objectCompanyId and objectId = :objectId",ExpressionAttributeValues:{":objectType":"shop",":objectCompanyId":g.user_company_id,":objectId":x}});case 12:if(1!=(O=e.sent).Items.length){e.next=87;break}return j=O.Items[0].shopCurrency?O.Items[0].shopCurrency:"NOK",T=y.orderMode,D=y.revenueMode,N=y.selectedYear,A=null,E=null,M=null,F=null,"week"==w&&(S=b(),P=b(-1),A=S[0],E=S[1],M=P[0],F=P[1]),"month"==w&&(k=I(),q=I(-1),A=k[0],E=k[1],M=q[0],F=q[1]),C=new Date,U=_(0,U=C.getFullYear()),V=_(-1,N),"year"==w&&(A=U[0],E=U[1],M=V[0],F=V[1]),B=U[0],W=U[1],Y="delivery"==T?"delivery_date":"createdAt",z={TableName:process.env.cartTableName,FilterExpression:Y+" BETWEEN :startOfMonth AND :endOfMonth and shopId = :shopId and orderStatus = :orderStatus and schedule_type = :schedule_type",ExpressionAttributeValues:{":startOfMonth":A,":endOfMonth":E,":shopId":x,":orderStatus":"pending",":schedule_type":"once"}},K={TableName:process.env.cartTableName,FilterExpression:Y+" BETWEEN :startOfMonth AND :endOfMonth and shopId = :shopId and orderStatus = :orderStatus and schedule_type = :schedule_type",ExpressionAttributeValues:{":startOfMonth":M,":endOfMonth":F,":shopId":x,":orderStatus":"pending",":schedule_type":"once"}},G={TableName:process.env.cartTableName,FilterExpression:Y+" BETWEEN :startOfMonth AND :endOfMonth and shopId = :shopId and orderStatus = :orderStatus and schedule_type = :schedule_type",ExpressionAttributeValues:{":startOfMonth":B,":endOfMonth":W,":shopId":x,":orderStatus":"pending",":schedule_type":"once"}},e.next=37,o.call("scan",z);case 37:return H=e.sent,e.next=40,o.call("scan",K);case 40:return L=e.sent,e.next=43,o.call("scan",G);case 43:for(ie in J=e.sent,Q=new Date(A).setHours(0,0,0),R=new Date(E).setHours(23,59,59),X=["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],Z=new Date(Q),$=new Date(R),(ee=new Object)["Product Name"]="product_name",te=function(e,t){for(var r=new Array,n=new Date(e);n<=t;){var a=n.getFullYear()+"-"+h(n.getMonth()+1,2)+"-"+h(n.getDate(),2);r.push({index:n.getDay(),name:X[n.getDay()],date:a}),ee[a]=a,n.setDate(n.getDate()+1)}return r}(Z,$),re=0,ne=H.Items.length,ae=0,se=0,oe=[],ue=[],te)ue.push({time:te[ie].date,value:0});for(ie in le=[0,0,0,0,0,0,0,0,0,0,0,0],ce=[0,0,0,0,0,0,0,0,0,0,0,0],J.Items)de="delivery"==T?J.Items[ie].delivery_date+108e5:J.Items[ie].createdAt,pe=new Date(de),me=pe.getMonth(),"net"==D&&J.Items[ie].mva?de<=(new Date).getTime()?le[me]+=parseInt(J.Items[ie].total)-parseInt(J.Items[ie].mva):ce[me]+=parseInt(J.Items[ie].total)-parseInt(J.Items[ie].mva):de<=(new Date).getTime()?le[me]+=parseInt(J.Items[ie].total):ce[me]+=parseInt(J.Items[ie].total);for(ie in H.Items)for(Ie in fe="delivery"==T?H.Items[ie].delivery_date+108e5:H.Items[ie].createdAt,"net"==D&&H.Items[ie].mva?re+=parseInt(H.Items[ie].total)-parseInt(H.Items[ie].mva):re+=parseInt(H.Items[ie].total),ye=new Date(fe),_e=ye.getFullYear()+"-"+h(ye.getMonth()+1,2)+"-"+h(ye.getDate(),2),ue.push({time:_e,value:"net"==D?parseFloat(H.Items[ie].total)-parseFloat(H.Items[ie].mva):parseFloat(H.Items[ie].total)}),oe.push(H.Items[ie].companyId),H.Items[ie].items)ae+=(0,a.default)(parseInt(H.Items[ie].items[Ie].quantity))?parseInt(H.Items[ie].items[Ie].quantity):0;for(ie in oe=m(oe),se=oe.length,re=Math.round(100*parseFloat(re))/100,be=0,ve=L.Items.length,he=0,ge=0,we=[],L.Items)for(Ie in"net"==D&&L.Items[ie].mva?be+=parseFloat(L.Items[ie].total)-parseFloat(L.Items[ie].mva):be+=parseFloat(L.Items[ie].total),we.push(L.Items[ie].companyId),L.Items[ie].items)he+=(0,a.default)(parseInt(L.Items[ie].items[Ie].quantity))?parseInt(L.Items[ie].items[Ie].quantity):0;for(ie in we=m(we),ge=we.length,be=be.toFixed(2),xe=v(be,re).toFixed(0),Oe=v(ve,ne).toFixed(0),je=v(he,ae).toFixed(0),Te=v(ge,se).toFixed(0),De=[],ue=f(ue,"time")){for(Ie in Ne=0,ue[ie])Ne+=ue[ie][Ie].value;De.push({time:new Date(ie).getTime()+108e5,value:Ne})}De=p(De,"time"),s(null,(0,i.success)({current_from:A,current_to:E,last_from:M,last_to:F,current_total_revenue:re,last_total_revenue:be,revenue_percent:xe,current_total_orders:ne,orders_percent:Oe,current_total_products:ae,products_percent:je,current_total_customers:se,customers_percent:Te,current_revenu_list:De,year_revenue_list_confirmed:le,year_revenue_list_unconfirmed:ce,shop_currency:j})),e.next=88;break;case 87:s(null,(0,i.failure)({status:"you do not have access to this api call"}));case 88:e.next=91;break;case 90:s(null,(0,i.failure)({status:"you do not have access to this api call"}));case 91:e.next=98;break;case 93:e.prev=93,e.t0=e.catch(0),u.notify(d,e.t0),console.log(e.t0),s(null,(0,i.failure)({status:e.t0}));case 98:case"end":return e.stop()}},e,this,[[0,93]])}));return function(t,r,n){return e.apply(this,arguments)}}(),c(r(1))),u=c(r(7)),i=r(8),l=r(9);function c(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t.default=e,t}function d(e){return e&&e.__esModule?e:{default:e}}var p=r(10),m=r(11),f=r(12),y=r(13);function _(e,t){var r,n,a=(new Date).getFullYear()-parseInt(t);return r=y().subtract(a,"years").startOf("year"),n=y().subtract(a,"years").subtract(1,"days").endOf("year"),[new Date(r).getTime(),new Date(n).getTime()]}function I(e){var t=new Date;return-1==e&&t.setDate(t.getDate()-30),[new Date(t.getFullYear(),t.getMonth(),1).setHours(0,0,1),new Date(t.getFullYear(),t.getMonth()+1,0).setHours(23,59,59)]}function b(){var e=null,t=null;return-1==(arguments.length>0&&void 0!==arguments[0]?arguments[0]:0)?(e=y().subtract(1,"weeks").startOf("isoWeek"),e=y(e).tz("Europe/Berlin"),t=y().subtract(1,"weeks").endOf("isoWeek"),t=y(t).tz("Europe/Berlin")):(e=y().startOf("isoWeek"),e=y(e).tz("Europe/Berlin"),t=y().endOf("isoWeek"),t=y(t).tz("Europe/Berlin")),[new Date(e).getTime()-108e5,new Date(t).getTime()-108e5]}function v(e,t){return(t-e)/t*100}function h(e,t){for(var r=e+"";r.length<t;)r="0"+r;return r}},function(e,t){e.exports=require("babel-runtime/regenerator")},function(e,t){e.exports=require("babel-runtime/core-js/number/is-integer")},function(e,t){e.exports=require("babel-runtime/helpers/asyncToGenerator")},function(e,t,r){"use strict";r.r(t),r.d(t,"notify",function(){return s});var n=r(2);const a=r.n(n)()({apiKey:"fd490dc0489374329842ddd3b0b568d7",releaseStage:"dev",appType:"backend",appVersion:"1.00"});function s(e=null,t){a.user={id:e},a.notify(t)}},function(e,t,r){"use strict";function n(e){return s(200,e)}function a(e){return s(500,e)}function s(e,t){return{statusCode:e,headers:{"Access-Control-Allow-Origin":"*","Access-Control-Allow-Credentials":!0},body:JSON.stringify(t)}}r.r(t),r.d(t,"success",function(){return n}),r.d(t,"failure",function(){return a})},function(e,t,r){"use strict";r.r(t),r.d(t,"is_level_permitted",function(){return o}),r.d(t,"is_object_permitted",function(){return u});var n=r(0),a=r.n(n),s=r(1);function o(e,t){return new Promise(function(r,n){let o=new a.a.CognitoIdentityServiceProvider,u=(process.env.COGNITO_POOL_ID,""),i="",l="",c="",d="",p={UserPoolId:process.env.COGNITO_POOL_ID,Username:e};o.adminGetUser(p,async function(e,a){if(e)n(e);else{for(var o in a.UserAttributes)"custom:company_id"==a.UserAttributes[o].Name&&(u=a.UserAttributes[o].Value),"custom:user_type"==a.UserAttributes[o].Name&&(i=a.UserAttributes[o].Value),"custom:first_name"==a.UserAttributes[o].Name&&(c=a.UserAttributes[o].Value),"custom:last_name"==a.UserAttributes[o].Name&&(d=a.UserAttributes[o].Value),"email"==a.UserAttributes[o].Name&&(l=a.UserAttributes[o].Value);const e=await s.call("scan",{TableName:process.env.objectsTableName,FilterExpression:"objectType = :objectType and objectId = :objectId",ExpressionAttributeValues:{":objectType":"company",":objectId":u}});"company"==i||"owner"==i?r({level_allowed:!0,location_allowed:!0,user_company_id:u,user_company_name:e.Items[0].objectName?e.Items[0].objectName:null,user_type:i,user_email:l,user_full_name:c+" "+d}):-1!=t.indexOf(i)?r({level_allowed:!0,location_allowed:!1,user_company_id:u,user_company_name:e.Items[0].objectName?e.Items[0].objectName:null,user_type:i,user_full_name:c+" "+d}):r({level_allowed:!1,location_allowed:!1,user_company_id:u,user_company_name:e.Items[0].objectName?e.Items[0].objectName:null,user_type:i,user_full_name:c+" "+d})}})})}function u(e,t,r,n=null){return new Promise(async function(a,o){"company"!=n&&"owner"!=n||a({is_object_allowed:!0});const u={TableName:process.env.userPermissionsTableName,FilterExpression:"locationId = :locationId and companyId = :companyId and userId = :userId",ExpressionAttributeValues:{":userId":e,":locationId":r,":companyId":t}};1==(await s.call("scan",u)).Items.length?a({is_object_allowed:!0}):a({is_object_allowed:!1})})}a.a.config.update({region:"eu-central-1"})},function(e,t){e.exports=require("array-sort")},function(e,t){e.exports=require("array-unique")},function(e,t){e.exports=require("group-array")},function(e,t){e.exports=require("moment-timezone")}]));