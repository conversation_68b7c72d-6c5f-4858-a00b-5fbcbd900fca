import * as dynamoDbLib from "../../../libs/dynamodb-lib";
import * as bugsnagClient from "../../../libs/bugsnag";
import {
  success,
  failure
} from "../../../libs/response-lib";

import {
  is_level_permitted
} from "../../../libs/permissions";

var arraySort = require('array-sort')

export async function shops(event, context, callback) {


  try {

    const access_scope = ['owner', 'manager', 'employee']

    var user_id = event.requestContext.identity.cognitoAuthenticationProvider.split('CognitoSignIn:')[1]


    const permissions = await is_level_permitted(user_id, access_scope);
    if (permissions.level_allowed) {

      const params = {
        TableName: process.env.objectsTableName,
        FilterExpression: "objectType = :objectType and objectCompanyId = :objectCompanyId",
        ExpressionAttributeValues: {
          ":objectType": 'shop',
          ":objectCompanyId": permissions.user_company_id
        }
      };


      const result = await dynamoDbLib.call("scan", params);

      for (var x in result.Items) {

        let orderDeadline = (result.Items[x].orderDeadline) ? parseInt(result.Items[x].orderDeadline) : 0
        let orderDeadlineTime = (result.Items[x].orderDeadlineTime) ? parseInt(result.Items[x].orderDeadlineTime) : 0

        let dealdine_date = new Date();
        dealdine_date.setDate(dealdine_date.getDate() + orderDeadline);
        dealdine_date.setHours(parseInt(orderDeadlineTime) - 1);
        //dealdine_date.setHours(23);
        dealdine_date.setMinutes(0);
        dealdine_date.setSeconds(0);

        if ((dealdine_date.getTime() - 3 * 60 * 60 * 1000) < new Date().getTime()) {
          dealdine_date.setDate(dealdine_date.getDate() + 1);
        }

        result.Items[x].poweroffice_deadline = dealdine_date.getTime()

      }



      callback(null, success(arraySort(result.Items, 'objectName')));

    } else {

      callback(null, failure({
        status: 'you do not have access to this api call'
      }));

    }

  } catch (e) {
    bugsnagClient.notify(user_id, e)
    callback(null, failure({
      status: e
    }));
  }
}