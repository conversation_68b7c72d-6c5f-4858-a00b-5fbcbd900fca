import uuid from "uuid";
import * as dynamoDbLib from "../../libs/dynamodb-lib";
import * as bugsnagClient from "../../libs/bugsnag";

const AWS = require('aws-sdk')


export async function pay(event, context, callback) {

  try {


    let cart_id = event.Records[0].Sns.Message

    let ordersParams = {
      TableName: process.env.cartTableName,
      FilterExpression: "cartId = :cartId",
      ExpressionAttributeValues: {
        ":cartId": cart_id
      }
    };

    let ordersResult = await dynamoDbLib.call("scan", ordersParams);
    let order = ordersResult.Items[0]


    const payment_method_details = await dynamoDbLib.call("scan", {
      TableName: process.env.objectsTableName,
      FilterExpression: "objectType = :objectType and objectId = :objectId",
      ExpressionAttributeValues: {
        ":objectType": 'payment_method',
        ":objectId": order.PaymentMethodId
      }
    });

    const integrationParams = {
      TableName: process.env.integrationsTableName,
      FilterExpression: "integrationType = :integrationType and integrationId = :integrationId",
      ExpressionAttributeValues: {
        ":integrationType": 'stripe',
        ":integrationId": payment_method_details.Items[0].objectIntegrationId
      }
    }

    const stripeIntegrationResult = await dynamoDbLib.call("scan", integrationParams);
    let stripeIntegration = stripeIntegrationResult.Items[0]
    let pkey = stripeIntegration.integrationPKey
    let skey = stripeIntegration.integrationSKey

    const stripe = require('stripe')(skey);

    const paymentIntent = await stripe.paymentIntents.create({
      amount: parseInt(order.total * 100),
      currency: 'nok',
      payment_method_types: ['card'],
      customer: payment_method_details.Items[0].objectStripeCustomerId,
      payment_method: payment_method_details.Items[0].objectPaymentMethodId,
      off_session: true,
      confirm: true,
    });

    if (paymentIntent.status == 'succeeded') {

      let transaction_id = uuid.v1()

      await dynamoDbLib.call("put", {
        TableName: process.env.transactionsTableName,
        Item: {
          transactionId: transaction_id,
          transactionDetails: paymentIntent,
          createdAt: new Date().getTime(),
          PaymentMethodId: order.PaymentMethodId
        }
      });

      const updateOrderParams = {
        TableName: process.env.cartTableName,
        Key: {
          cartId: cart_id,
        },

        UpdateExpression: "SET transactionId = :transactionId, paymentStatus = :paymentStatus",
        ExpressionAttributeValues: {
          ":transactionId": transaction_id,
          ":paymentStatus": 'paid'
        },
        ReturnValues: "ALL_NEW"
      };

      await dynamoDbLib.call("update", updateOrderParams);

    }

    callback(null, 'done');

  } catch (e) {
    bugsnagClient.notify(null, e)
    callback(null, e);
  }
}