import uuid from "uuid";
import * as dynamoDbLib from "../../../libs/dynamodb-lib";
import * as bugsnagClient from "../../../libs/bugsnag";

import {
  success,
  failure
} from "../../../libs/response-lib";

import {
  is_level_permitted
} from "../../../libs/permissions";

const AWS = require('aws-sdk')
const cognito_client = new AWS.CognitoIdentityServiceProvider()

export async function shop(event, context, callback) {


  try {

    const access_scope = ['owner', 'manager', 'employee']

    const data = JSON.parse(event.body);
    let pool_id = process.env.COGNITO_POOL_ID
    var user_id = event.requestContext.identity.cognitoAuthenticationProvider.split('CognitoSignIn:')[1]


    const permissions = await is_level_permitted(user_id, access_scope);
    if (permissions.level_allowed) {

      let shop_id = uuid.v1()

      const params = {
        TableName: process.env.objectsTableName,
        Item: {
          objectCreatedBy: user_id,
          objectCompanyId: permissions.user_company_id,
          objectId: shop_id,
          objectName: data.objectName,
          objectType: "shop",
          createdAt: new Date().getTime()
        }
      };

      await dynamoDbLib.call("put", params);
      /*
      await dynamoDbLib.call("put", {
        TableName: process.env.objectsTableName,
        Item: {
          objectCreatedBy: user_id,
          objectCompanyId: permissions.user_company_id,
          objectId: uuid.v1(),
          objectName: 'Test',
          objectShopId: shop_id,
          objectType: "category",
          objectStatus: 'hidden',
          createdAt: new Date().getTime()
        }
      });
      */

      callback(null, success(params.Item));

    } else {

      callback(null, failure({
        status: 'you do not have access to this api call'
      }));

    }

  } catch (e) {
    bugsnagClient.notify(user_id, e)
    callback(null, failure({
      status: e
    }));
  }

}