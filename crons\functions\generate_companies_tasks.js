import uuid from "uuid";
import * as dynamoDbLib from "../../libs/dynamodb-lib";
import * as bugsnagClient from "../../libs/bugsnag";
import * as time from "../../libs/time";
export async function generate(event, context, callback) {


  try {

    const allTasksParams = {
      TableName: process.env.tasksTableName,
      FilterExpression: "taskStatus = :taskStatus",
      ExpressionAttributeValues: {
        ":taskStatus": 'open'
      }
    };

    let all_tasks = await dynamoDbLib.call("scan", allTasksParams);

    for (var j in all_tasks.Items) {

      //let task_id = event.Records[0].Sns.Message
      let task_id = all_tasks.Items[j].taskId

      const tasksParams = {
        TableName: process.env.tasksTableName,
        FilterExpression: "taskId = :taskId and taskStatus = :taskStatus",
        ExpressionAttributeValues: {
          ":taskId": task_id,
          ":taskStatus": 'open'
        }
      };

      let tasks = await dynamoDbLib.call("scan", tasksParams);

      let company_id = tasks.Items[0].companyId

      let scheduled_tasks_list = []

      for (var x in tasks.Items) {

        let tzo = (tasks.Items[x].tzo) ? tasks.Items[x].tzo : 0

        if (tasks.Items[x].taskRepeat == 'never') {

          tasks.Items[x].tasksScheduleTime = new Date(tasks.Items[x].taskDates[0] + ' ' + tasks.Items[x].taskTime).getTime()

          scheduled_tasks_list.push({
            companyId: tasks.Items[x].companyId,
            taskId: tasks.Items[x].taskId,
            taskType: tasks.Items[x].taskType,
            assignees: tasks.Items[x].assignees,
            assignerId: tasks.Items[x].assignerId,
            taskStatus: 'open',
            locationId: tasks.Items[x].locationId,
            taskName: tasks.Items[x].taskName,
            taskDesc: tasks.Items[x].taskDesc,
            tasksScheduleTime: time.adjustTimeDifference(new Date(tasks.Items[x].taskDates[0] + ' ' + tasks.Items[x].taskTime).getTime(), tzo),
            cartId: (tasks.Items[x].cartId) ? tasks.Items[x].cartId : null,
            productId: (tasks.Items[x].productId) ? tasks.Items[x].productId : null,
            quantity: (tasks.Items[x].quantity) ? tasks.Items[x].quantity : null,
            shopId: (tasks.Items[x].shopId) ? tasks.Items[x].shopId : null,
            TaskexceptionId: (tasks.Items[x].TaskexceptionId) ? tasks.Items[x].TaskexceptionId : null,
            taskDeviationFor: (tasks.Items[x].taskDeviationFor) ? tasks.Items[x].taskDeviationFor : null

          })
        }

        if (tasks.Items[x].taskRepeat == 'daily') {

          let todayTimeStamp = +new Date;

          for (var i = 0; i < 7; i++) {

            let toAddTimeStamp = 1000 * 60 * 60 * 24 * i;
            let theDayTime = new Date(todayTimeStamp + toAddTimeStamp)

            scheduled_tasks_list.push({
              companyId: tasks.Items[x].companyId,
              taskId: tasks.Items[x].taskId,
              taskType: tasks.Items[x].taskType,
              assignees: tasks.Items[x].assignees,
              assignerId: tasks.Items[x].assignerId,
              taskStatus: 'open',
              locationId: tasks.Items[x].locationId,
              taskName: tasks.Items[x].taskName,
              taskDesc: tasks.Items[x].taskDesc,
              tasksScheduleTime: time.adjustTimeDifference(new Date(theDayTime.getFullYear() + '-' + (theDayTime.getMonth() + 1) + '-' + theDayTime.getDate() + ' ' + tasks.Items[x].taskTime).getTime(), tzo)
            })
          }
        }

        if (tasks.Items[x].taskRepeat == 'weekly') {

          for (var y in tasks.Items[x].taskDates) {

            let day = new Date();
            day.setDate(day.getDate() + (tasks.Items[x].taskDates[y] + 7 - day.getDay()) % 7);

            scheduled_tasks_list.push({
              companyId: tasks.Items[x].companyId,
              taskId: tasks.Items[x].taskId,
              taskType: tasks.Items[x].taskType,
              assignees: tasks.Items[x].assignees,
              assignerId: tasks.Items[x].assignerId,
              taskStatus: 'open',
              locationId: tasks.Items[x].locationId,
              taskName: tasks.Items[x].taskName,
              taskDesc: tasks.Items[x].taskDesc,
              tasksScheduleTime: time.adjustTimeDifference(new Date(day.getFullYear() + '-' + (day.getMonth() + 1) + '-' + day.getDate() + ' ' + tasks.Items[x].taskTime).getTime(), tzo)
            })

          }
        }

        if (tasks.Items[x].taskRepeat == 'monthly') {

          let today = new Date()
          let this_month = today.getMonth() + 1
          let this_date = today.getDate()
          let this_year = today.getFullYear()

          let dates = []

          for (var y in tasks.Items[x].taskDates) {

            if (tasks.Items[x].taskDates[y] >= this_date) {

              dates.push(this_year + '-' + this_month + '-' + tasks.Items[x].taskDates[y] + ' ' + tasks.Items[x].taskTime)

            } else {

              let toAddTimeStamp = 1000 * 60 * 60 * 24 * 30;
              let theDayTime = new Date(+new Date + toAddTimeStamp)
              dates.push(theDayTime.getFullYear() + '-' + (theDayTime.getMonth() + 1) + '-' + tasks.Items[x].taskDates[y] + ' ' + tasks.Items[x].taskTime)

            }
          }

          for (var y in dates) {
            scheduled_tasks_list.push({
              companyId: tasks.Items[x].companyId,
              taskId: tasks.Items[x].taskId,
              taskType: tasks.Items[x].taskType,
              assignees: tasks.Items[x].assignees,
              assignerId: tasks.Items[x].assignerId,
              taskStatus: 'open',
              locationId: tasks.Items[x].locationId,
              taskName: tasks.Items[x].taskName,
              taskDesc: tasks.Items[x].taskDesc,
              tasksScheduleTime: time.adjustTimeDifference(new Date(dates[y]).getTime(), tzo)
            })
          }

        }

      }

      for (var x in scheduled_tasks_list) {

        if (scheduled_tasks_list[x].taskType == 'logtemp') {

          const params = {
            TableName: process.env.objectsTableName,
            FilterExpression: "objectType = :objectType and objectParent = :objectParent and objectCompanyId = :objectCompanyId",
            ExpressionAttributeValues: {
              ":objectType": 'device',
              ":objectParent": scheduled_tasks_list[x].locationId,
              ":objectCompanyId": company_id
            }
          };
          const devicesResult = await dynamoDbLib.call("scan", params);
          scheduled_tasks_list[x].devices = devicesResult.Items

        }

      }


      for (var x in scheduled_tasks_list) {

        const params = {
          TableName: process.env.tasksSchedulesTableName,
          FilterExpression: "taskId = :taskId and tasksScheduleTime = :tasksScheduleTime and locationId = :locationId and companyId = :companyId",
          ExpressionAttributeValues: {
            ":taskId": scheduled_tasks_list[x].taskId,
            ":tasksScheduleTime": scheduled_tasks_list[x].tasksScheduleTime,
            ":locationId": scheduled_tasks_list[x].locationId,
            ":companyId": scheduled_tasks_list[x].companyId

          }
        };
        const tasksSchedules = await dynamoDbLib.call("scan", params);

        if (tasksSchedules.Items.length == 0) {

          if (scheduled_tasks_list[x].taskType == 'logtemp') {

            let devices = scheduled_tasks_list[x].devices
            scheduled_tasks_list[x].devices = []

            for (var y in devices) {

              scheduled_tasks_list[x].deviceId = devices[y].objectId
              scheduled_tasks_list[x].deviceName = devices[y].objectName
              scheduled_tasks_list[x].deviceType = devices[y].objectKind
              scheduled_tasks_list[x].taskScheduleId = uuid.v1()
              scheduled_tasks_list[x].createdAt = new Date().getTime()

              var insertParams = {
                TableName: process.env.tasksSchedulesTableName,
                Item: scheduled_tasks_list[x]
              };
              await dynamoDbLib.call("put", insertParams);
            }

          } else {

            scheduled_tasks_list[x].taskScheduleId = uuid.v1()
            scheduled_tasks_list[x].createdAt = new Date().getTime()

            var insertParams = {
              TableName: process.env.tasksSchedulesTableName,
              Item: scheduled_tasks_list[x]
            };

            await dynamoDbLib.call("put", insertParams);
          }
        }
      }
    }

    callback(null, 'done');

  } catch (e) {
    bugsnagClient.notify(null, e)
    callback(null, e);
  }
}