import * as dynamoDbLib from "../../../libs/dynamodb-lib";
import * as bugsnagClient from "../../../libs/bugsnag";
import uuid from "uuid";
import {
  success,
  failure
} from "../../../libs/response-lib";

import {
  is_level_permitted
} from "../../../libs/permissions";

import PowerOffice from "../../../libs/poweroffice";
import {
  log_event
} from "../../../libs/logs";


export async function order(event, context, callback) {

  try {

    const access_scope = ['owner', 'manager', 'employee']

    const data = JSON.parse(event.body);


    var user_id = event.requestContext.identity.cognitoAuthenticationProvider.split('CognitoSignIn:')[1]


    const permissions = await is_level_permitted(user_id, access_scope);

    if (permissions.level_allowed) {

      let shop_id = event.pathParameters.id

      const shopParams = {
        TableName: process.env.objectsTableName,
        FilterExpression: "objectType = :objectType and objectId = :objectId",
        ExpressionAttributeValues: {
          ":objectType": 'shop',
          ":objectId": shop_id
        }
      }

      const shopResult = await dynamoDbLib.call("scan", shopParams);

      let shop = shopResult.Items[0]

      let cart_id = data.cart_id
      let status = data.status
      let op = data.op


      //  if (permissions.user_company_id == shop.objectCompanyId) {
      let params = {}

      if (status == 'canceled') {

        var deleteOrder = async function deleteOrder(cart_id, status, user_id) {

          const ordersResult = await dynamoDbLib.call("scan", {
            TableName: process.env.cartTableName,
            FilterExpression: "cartId = :cartId",
            ExpressionAttributeValues: {
              ":cartId": cart_id
            }
          });

          let params = {
            TableName: process.env.cartTableName,

            Key: {
              cartId: cart_id
            },

            UpdateExpression: "SET powerOfficeInvoiceId = :powerOfficeInvoiceId, powerOfficeInvoiceIdentifier = :powerOfficeInvoiceIdentifier, orderStatus = :orderStatus, canceledAt = :canceledAt, canceledBy = :canceledBy",
            ExpressionAttributeValues: {
              ":orderStatus": status,
              ":canceledBy": user_id,
              ":powerOfficeInvoiceId": null,
              ":powerOfficeInvoiceIdentifier": null,
              ":canceledAt": new Date().getTime(),
            },
            ReturnValues: "ALL_NEW"
          };


          const result = await dynamoDbLib.call("update", params);

          await log_event(
            user_id,
            permissions.user_full_name,
            permissions.user_company_name,
            shopResult.Items[0].objectCompanyId,
            "order_canceled", {
              cartId: cart_id,
              orderType: 'single',
              created_from: 'shop'
            },
            shop_id,
            shopResult.Items[0].objectName,
          )

          // refund stripe order
          if (ordersResult.Items[0].paymentMethod == 'stripe' && ordersResult.Items[0].PaymentMethodId && ordersResult.Items[0].transactionId) {

            const transaction = await dynamoDbLib.call("scan", {
              TableName: process.env.transactionsTableName,
              FilterExpression: "transactionId = :transactionId",
              ExpressionAttributeValues: {
                ":transactionId": ordersResult.Items[0].transactionId
              }
            });

            let charge_id = transaction.Items[0].transactionDetails.charges.data[0].id


            const payment_method_details = await dynamoDbLib.call("scan", {
              TableName: process.env.objectsTableName,
              FilterExpression: "objectType = :objectType and objectId = :objectId",
              ExpressionAttributeValues: {
                ":objectType": 'payment_method',
                ":objectId": ordersResult.Items[0].PaymentMethodId
              }
            });

            const integrationParams = {
              TableName: process.env.integrationsTableName,
              FilterExpression: "integrationType = :integrationType and integrationId = :integrationId",
              ExpressionAttributeValues: {
                ":integrationType": 'stripe',
                ":integrationId": payment_method_details.Items[0].objectIntegrationId
              }
            }

            const stripeIntegrationResult = await dynamoDbLib.call("scan", integrationParams);
            let stripeIntegration = stripeIntegrationResult.Items[0]
            let pkey = stripeIntegration.integrationPKey
            let skey = stripeIntegration.integrationSKey

            const stripe = require('stripe')(skey);

            await stripe.refunds.create({
              charge: charge_id
            });


          }

          if (ordersResult.Items[0].powerOfficeInvoiceIdentifier) {
            if (shopResult.Items[0].objectPowerOfficeIntegration) {

              let company_id = shopResult.Items[0].objectCompanyId

              const integrationParams = {
                TableName: process.env.integrationsTableName,
                FilterExpression: "integrationId = :integrationId",
                ExpressionAttributeValues: {
                  ":integrationId": shopResult.Items[0].objectPowerOfficeIntegration
                }
              };

              const integrations = await dynamoDbLib.call("scan", integrationParams);
              let poweroffice_client_key = integrations.Items[0].integrationPOAppKey
              let poweroffice_secrect_key = integrations.Items[0].integrationPOClientKey

              let poweroffice = await new PowerOffice(process.env.POWEROFFICE_APP_KEY, poweroffice_secrect_key)

              let invoice = await poweroffice.getInvoice(ordersResult.Items[0].powerOfficeInvoiceIdentifier)
              if (invoice.data.status == 0) {
                // if invoice still draft, we delete it.
                await poweroffice.deleteInvoice(ordersResult.Items[0].powerOfficeInvoiceIdentifier)

              } else {
                // we create a reverse invoice with minus values.
                let invoice_lines = []

                for (var z in invoice.data.outgoingInvoiceLines) {
                  invoice_lines.push({
                    SortOrder: invoice.data.outgoingInvoiceLines[z].sortOrder,
                    ProductCode: invoice.data.outgoingInvoiceLines[z].productCode,
                    Description: invoice.data.outgoingInvoiceLines[z].description,
                    Quantity: invoice.data.outgoingInvoiceLines[z].quantity,
                    DiscountPercent: invoice.data.outgoingInvoiceLines[z].discountPercent,
                    UnitPrice: -1 * invoice.data.outgoingInvoiceLines[z].unitPrice,
                    NetAmount: -1 * invoice.data.outgoingInvoiceLines[z].netAmount
                  })
                }

                let order = await poweroffice.createInvoice({
                  OrderDate: invoice.data.orderDate,
                  CustomerCode: invoice.data.customerCode,
                  CustomerEmail: invoice.data.customerEmail,
                  CustomerReference: invoice.data.customerReference,
                  DeliveryAddressId: invoice.data.deliveryAddressId,
                  DeliveryDate: invoice.data.deliveryDate,
                  InvoiceDeliveryType: invoice.data.invoiceDeliveryType,
                  PurchaseOrderNo: invoice.data.purchaseOrderNo,
                  Status: 0,
                  currencyCode: (ordersResult.Items[0].currency) ? ordersResult.Items[0].currency : 'NOK',
                  OutgoingInvoiceLines: invoice_lines
                })
              }

            }
          }
        }

        await deleteOrder(cart_id, status, user_id)

        if (op == 'all') {

          let childsOrders = await dynamoDbLib.call("scan", {
            TableName: process.env.cartTableName,
            FilterExpression: "parent_order = :parent_order and delivery_date > :delivery_date",
            ExpressionAttributeValues: {
              ":parent_order": cart_id,
              ":delivery_date": new Date().getTime()
            }
          });

          for (var x in childsOrders.Items) {
            await deleteOrder(childsOrders.Items[x].cartId, status, user_id)
          }

        }

      }

      if (status == 'pending') {

        let transaction_id = null

        const ordersResult = await dynamoDbLib.call("scan", {
          TableName: process.env.cartTableName,
          FilterExpression: "cartId = :cartId",
          ExpressionAttributeValues: {
            ":cartId": cart_id
          }
        });

        if (ordersResult.Items[0].paymentMethod == 'stripe' && ordersResult.Items[0].PaymentMethodId && ordersResult.Items[0].transactionId) {

          const payment_method_details = await dynamoDbLib.call("scan", {
            TableName: process.env.objectsTableName,
            FilterExpression: "objectType = :objectType and objectId = :objectId",
            ExpressionAttributeValues: {
              ":objectType": 'payment_method',
              ":objectId": ordersResult.Items[0].PaymentMethodId
            }
          });

          const integrationParams = {
            TableName: process.env.integrationsTableName,
            FilterExpression: "integrationType = :integrationType and integrationId = :integrationId",
            ExpressionAttributeValues: {
              ":integrationType": 'stripe',
              ":integrationId": payment_method_details.Items[0].objectIntegrationId
            }
          }

          const stripeIntegrationResult = await dynamoDbLib.call("scan", integrationParams);
          let stripeIntegration = stripeIntegrationResult.Items[0]
          let pkey = stripeIntegration.integrationPKey
          let skey = stripeIntegration.integrationSKey

          const stripe = require('stripe')(skey);

          const paymentIntent = await stripe.paymentIntents.create({
            amount: parseInt(parseFloat(Math.round(ordersResult.Items[0].total * 100) / 100).toFixed(2) * 100),
            currency: (ordersResult.Items[0].currency) ? ordersResult.Items[0].currency.toLowerCase() : 'nok',
            payment_method_types: ['card'],
            customer: payment_method_details.Items[0].objectStripeCustomerId,
            payment_method: payment_method_details.Items[0].objectPaymentMethodId,
            off_session: true,
            confirm: true
          });

          if (paymentIntent.status == 'succeeded') {
            transaction_id = uuid.v1()
          }

          await dynamoDbLib.call("put", {
            TableName: process.env.transactionsTableName,
            Item: {
              transactionId: transaction_id,
              transactionDetails: paymentIntent,
              createdAt: new Date().getTime(),
              PaymentMethodId: ordersResult.Items[0].PaymentMethodId
            }
          });


        }

        params = {
          TableName: process.env.cartTableName,

          Key: {
            cartId: cart_id
          },

          UpdateExpression: "SET transactionId = :transactionId , orderStatus = :orderStatus, restoredAt = :restoredAt, restoredBy = :restoredBy",
          ExpressionAttributeValues: {
            ":orderStatus": status,
            ":restoredBy": user_id,
            ":transactionId": transaction_id,
            ":restoredAt": new Date().getTime(),
          },
          ReturnValues: "ALL_NEW"
        };
        const result = await dynamoDbLib.call("update", params);

        await log_event(
          user_id,
          permissions.user_full_name,
          permissions.user_company_name,
          shopResult.Items[0].objectCompanyId,
          "order_restored", {
            cartId: cart_id,
            orderType: 'single',
            created_from: 'shop'
          },
          shop_id,
          shopResult.Items[0].objectName,
        )


      }


      /*
      // remove from pogo

      // delete related tasks to cart

      const tasksParam = {
        TableName: process.env.tasksTableName,
        FilterExpression: "cartId = :cartId",
        ExpressionAttributeValues: {
          ":cartId": cart_id
        }
      };

      let tasksResult = await dynamoDbLib.call("scan", tasksParam);

      for (var x in tasksResult.Items) {

        let taskId = tasksResult.Items[x].taskId

        await dynamoDbLib.call("delete", {
          TableName: process.env.tasksTableName,
          Key: {
            taskId: taskId
          }
        });

      }

      // delete related tasks sechudels to cart

      const tasksSchedulesParam = {
        TableName: process.env.tasksSchedulesTableName,
        FilterExpression: "cartId = :cartId",
        ExpressionAttributeValues: {
          ":cartId": cart_id
        }
      };

      let tasksSchedulesResult = await dynamoDbLib.call("scan", tasksSchedulesParam);

      for (var x in tasksSchedulesResult.Items) {

        let taskScheduleId = tasksSchedulesResult.Items[x].taskScheduleId
        let taskId = tasksSchedulesResult.Items[x].taskId


        await dynamoDbLib.call("delete", {
          TableName: process.env.tasksSchedulesTableName,
          Key: {
            taskScheduleId: taskScheduleId,
            taskId: taskId

          }
        });

      }

      */


      callback(null, success('done'));

      /*
      } else {

        callback(null, failure({
          status: 'you do not have access to this shop'
        }));
      }
      */

    } else {

      callback(null, failure({
        status: 'you do not have access to this api call'
      }));

    }
  } catch (e) {
    console.log(e);
    bugsnagClient.notify(user_id, e)
    callback(null, failure({
      status: e
    }));
  }
}