import * as dynamoDbLib from "../../../libs/dynamodb-lib";
import * as bugsnagClient from "../../../libs/bugsnag";
import {
  success,
  failure
} from "../../../libs/response-lib";

import {
  is_level_permitted
} from "../../../libs/permissions";
const AWS = require('aws-sdk')
const SNS = new AWS.SNS();

export async function category(event, context, callback) {

  try {

    const access_scope = ['owner', 'manager', 'employee']

    const data = JSON.parse(event.body);

    var user_id = event.requestContext.identity.cognitoAuthenticationProvider.split('CognitoSignIn:')[1]

    const permissions = await is_level_permitted(user_id, access_scope);
    if (permissions.level_allowed) {

      const params = {
        TableName: process.env.objectsTableName,

        Key: {
          objectCompanyId: permissions.user_company_id,
          objectId: event.pathParameters.id
        },

        UpdateExpression: "SET objectName = :objectName, objectVisibility = :objectVisibility, objectAdminCategory = :objectAdminCategory, ObjectLastUpdatedBy = :ObjectLastUpdatedBy",
        ExpressionAttributeValues: {
          ":objectName": data.objectName ? data.objectName : null,
          ":objectVisibility": data.objectVisibility ? data.objectVisibility : false,
          ":objectAdminCategory": data.objectAdminCategory ? data.objectAdminCategory : false,

          ":ObjectLastUpdatedBy": user_id
        },
        ReturnValues: "ALL_NEW"
      };

      await SNS.publish({
        Message: event.pathParameters.id,
        TopicArn: process.env.generatePowerOfficeCategoriesSNSTopic
      }, function(err, data) {

        if (err) {
          callback(null, err);
        }

      });

      const result = await dynamoDbLib.call("update", params);
      callback(null, success({
        status: true
      }));

    } else {

      callback(null, failure({
        status: 'you do not have access to this api call'
      }));

    }

  } catch (e) {
    bugsnagClient.notify(user_id, e)
    callback(null, failure({
      status: false
    }));
  }
}