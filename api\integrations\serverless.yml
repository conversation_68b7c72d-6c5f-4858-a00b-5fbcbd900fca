service: kompis-integrations

plugins:
  - serverless-webpack

custom:
  webpackIncludeModules: true
  stage: ${opt:stage, self:provider.stage}
  stageSufix:
    prod: ""
    dev: -dev
  congnito_pool_id:
    dev: eu-central-1_o36r1nMGj
    prod: eu-central-1_E1nLfop0C

  poweroffice_auth_url:
    dev: https://api-demo.poweroffice.net/OAuth/Token
    prod: https://api.poweroffice.net/OAuth/Token

  poweroffice_api_url:
    dev: https://api-demo.poweroffice.net
    prod: https://api.poweroffice.net

  poweroffice_app_key:
    dev: 0fabc9dc-5477-4459-bd0c-7bc19a02caf3
    prod: 4ace3064-f987-48f1-9111-c96c896f36c6

  poweroffice_callback_url:
    dev: https://devapi.kompis.app/integrations/callbacks/poweroffice/
    prod: https://api.kompis.app/integrations/callbacks/poweroffice/

provider:
  versionFunctions: false
  name: aws
  endpointType: REGIONAL
  runtime: nodejs16.x
  stage: dev
  region: eu-central-1
  environment:
    COGNITO_POOL_ID: ${self:custom.congnito_pool_id.${self:provider.stage}}
    POWEROFFICE_AUTH_URL: ${self:custom.poweroffice_auth_url.${self:provider.stage}}
    POWEROFFICE_API_URL: ${self:custom.poweroffice_api_url.${self:provider.stage}}
    POWEROFFICE_APP_KEY: ${self:custom.poweroffice_app_key.${self:provider.stage}}
    POWEROFFICE_CALLBACK_URL: ${self:custom.poweroffice_callback_url.${self:provider.stage}}
    generateCompaniesTasksSNSTopic: arn:aws:sns:eu-central-1:589634798762:generate_companies_tasks${self:custom.stageSufix.${self:custom.stage}}
    generatePowerOfficeInvoicesSNSTopic: arn:aws:sns:eu-central-1:589634798762:generate_poweroffice_invoices${self:custom.stageSufix.${self:custom.stage}}
    cartTableName: cart${self:custom.stageSufix.${self:custom.stage}}
    exceptionsTableName: exceptions${self:custom.stageSufix.${self:custom.stage}}
    objectsTableName: objects${self:custom.stageSufix.${self:custom.stage}}
    sensorsDataLogsTableName: sensorsDataLogs${self:custom.stageSufix.${self:custom.stage}}
    tasksTableName: tasks${self:custom.stageSufix.${self:custom.stage}}
    tasksLogTableName: tasksLog${self:custom.stageSufix.${self:custom.stage}}
    tasksSchedulesTableName: tasksSchedules${self:custom.stageSufix.${self:custom.stage}}
    userPermissionsTableName: userPermissions${self:custom.stageSufix.${self:custom.stage}}
    logsTableName: logs${self:custom.stageSufix.${self:custom.stage}}
    integrationsTableName: integrations${self:custom.stageSufix.${self:custom.stage}}

  iamRoleStatements:
    - Effect: "Allow"
      Action:
        - "SNS:Publish"
      Resource: "arn:aws:sns:eu-central-1:589634798762:generate_companies_tasks${self:custom.stageSufix.${self:custom.stage}}"

    - Effect: "Allow"
      Action:
        - "SNS:Publish"
      Resource: "arn:aws:sns:eu-central-1:589634798762:generate_poweroffice_invoices${self:custom.stageSufix.${self:custom.stage}}"

    - Effect: "Allow"
      Action:
        - "cognito-idp:*"
      Resource: "arn:aws:cognito-idp:eu-central-1:*:userpool/*"

    - Effect: Allow
      Action:
        - dynamodb:Query
        - dynamodb:Scan
        - dynamodb:GetItem
        - dynamodb:PutItem
        - dynamodb:UpdateItem
        - dynamodb:DeleteItem
      Resource: "arn:aws:dynamodb:eu-central-1:*:*"

functions:
  createStripeIntegration:
    handler: functions/create_stripe.integration
    events:
      - http:
          path: /stripe
          method: post
          cors: true
          authorizer: aws_iam

  createPowerOfficeIntegration:
    handler: functions/create_poweroffice.integration
    events:
      - http:
          path: /poweroffice
          method: post
          cors: true
          authorizer: aws_iam

  callbackPowerOffice:
    handler: functions/callback_poweroffice.integration
    events:
      - http:
          path: /callbacks/poweroffice/{id}
          method: post
          cors: true

  fetchPowerOfficeLedgerAccounts:
    handler: functions/fetch_ledger_accounts.accounts
    events:
      - http:
          path: /poweroffice/fetch
          method: post
          cors: true
          authorizer: aws_iam

  updateStripeIntegration:
    handler: functions/update_stripe.integration
    events:
      - http:
          path: /stripe/{id}
          method: put
          cors: true
          authorizer: aws_iam

  updatePowerOfficeIntegration:
    handler: functions/update_poweroffice.integration
    events:
      - http:
          path: /poweroffice/{id}
          method: put
          cors: true
          authorizer: aws_iam

  deleteIntegration:
    handler: functions/delete.integration
    events:
      - http:
          path: /{id}
          method: delete
          cors: true
          authorizer: aws_iam

  listIntegrations:
    handler: functions/list.integrations
    events:
      - http:
          path: /
          method: get
          cors: true
          authorizer: aws_iam

  listStripeIntegrations:
    handler: functions/list_stripe.integrations
    events:
      - http:
          path: /stripe
          method: get
          cors: true
          authorizer: aws_iam

  listPowerOfficeIntegrations:
    handler: functions/list_poweroffice.integrations
    events:
      - http:
          path: /poweroffice
          method: get
          cors: true
          authorizer: aws_iam

  checkPowerOfficeIntegration:
    handler: functions/check_poweroffice_errors.integrations
    events:
      - http:
          path: /poweroffice/check/{id}
          method: get
          cors: true
          authorizer: aws_iam
