import uuid from "uuid";
import * as dynamoDbLib from "../../libs/dynamodb-lib";
import * as bugsnagClient from "../../libs/bugsnag";

export async function generate(event, context, callback) {

  const AWS = require('aws-sdk')
  const SNS = new AWS.SNS();
  var arraySort = require('array-sort')

  try {

    let shop_id = event.Records[0].Sns.Message
    //let shop_id = 'dfea4f90-c455-11e9-9ab0-7fc92b1e522d'
    //let shop_id = 'e3336010-c455-11e9-9ab0-7fc92b1e522d'
    //let shop_id = '3b7df8d0-259f-11e9-94bb-f3a44358c2ae'
    //return

    const shopParams = {
      TableName: process.env.objectsTableName,
      FilterExpression: "objectType = :objectType and objectId = :objectId",
      ExpressionAttributeValues: {
        ":objectType": 'shop',
        ":objectId": shop_id
      }
    };

    const shopResult = await dynamoDbLib.call("scan", shopParams);
    let shopDetails = shopResult.Items[0]
    let orderDeadline = (shopDetails.orderDeadline) ? parseInt(shopDetails.orderDeadline) : 0
    let orderDeadlineTime = (shopDetails.orderDeadlineTime) ? parseInt(shopDetails.orderDeadlineTime) : 24

    let ordersParams = {
      TableName: process.env.cartTableName,
      FilterExpression: "shopId = :shopId and orderStatus <> :orderStatus and schedule_type = :schedule_type",
      ExpressionAttributeValues: {
        ":shopId": shop_id,
        ":orderStatus": 'canceled',
        ":schedule_type": 'weekly'
      }
    };

    let orders = await dynamoDbLib.call("scan", ordersParams);


    let scheduled_orders_list = []
    let weeks_array = [7, 14, 21, 28] // for 1 and 2 weeks.

    for (var x in orders.Items) {

      let order_company_id = orders.Items[x].companyId

      let shops_permissions = await dynamoDbLib.call("scan", {
        TableName: process.env.userPermissionsTableName,
        FilterExpression: "companyId = :companyId and roleType = :roleType and isActive = :isActive and shopId = :shopId",
        ExpressionAttributeValues: {
          ":companyId": order_company_id,
          ":roleType": 'shop_access_role',
          ":isActive": true,
          ":shopId": shop_id
        }
      })

      if (shops_permissions.Items[0].allow_recurring_orders) {

        for (var y in orders.Items[x].schedule_dates) {

          for (var w in weeks_array) {

            let schedule_day = parseInt(orders.Items[x].schedule_dates[y])

            let schedule_date = nextDay(schedule_day, weeks_array[w])
            //schedule_date.setDate(schedule_date.getDate() + (schedule_day + weeks_array[w] - schedule_date.getDay()) % weeks_array[w]);

            let day = nextDay(schedule_day, weeks_array[w])
            //day.setDate(day.getDate() + (schedule_day + weeks_array[w] - day.getDay()) % weeks_array[w]);

            let order_time_stamp = new Date(day.getFullYear() + '-' + (day.getMonth() + 1) + '-' + day.getDate())

            let today = new Date();
            let current_hour = today.getHours()

            const diffDays = Math.round(((schedule_date - today) / (24 * 60 * 60 * 1000)));

            let recurring_start_date = (orders.Items[x].recurring_start_date) ? orders.Items[x].recurring_start_date : null

            if (!recurring_start_date || (recurring_start_date <= schedule_date)) {

              if ((diffDays > orderDeadline) || ((diffDays == orderDeadline) && (current_hour < (orderDeadlineTime - (orders.Items[x].tzo / 60) - 1)))) {

                scheduled_orders_list.push({
                  userId: orders.Items[x].userId,
                  tzo: (orders.Items[x].tzo) ? orders.Items[x].tzo : 0,
                  parent_order: orders.Items[x].cartId,
                  freight: orders.Items[x].freight,
                  total: parseFloat(Math.round(orders.Items[x].total * 100) / 100).toFixed(2),
                  shopId: orders.Items[x].shopId,
                  schedule_dates: null,
                  schedule_type: 'once',
                  schedule_duration_type: (orders.Items[x].schedule_duration_type) ? orders.Items[x].schedule_duration_type : 1,
                  orderStatus: 'pending',
                  paymentStatus: orders.Items[x].paymentStatus,
                  on_behalf_of: orders.Items[x].on_behalf_of,
                  mva: orders.Items[x].mva,

                  discount: orders.Items[x].discount,
                  discount_value: orders.Items[x].discount_value,
                  paymentMethod: orders.Items[x].paymentMethod,
                  PaymentMethodId: orders.Items[x].PaymentMethodId,
                  companyId: orders.Items[x].companyId,
                  delivery_date: order_time_stamp.getTime() + (3 * 60 * 60 * 1000),
                  delivery_point: orders.Items[x].delivery_point,
                  delivery_time: orders.Items[x].delivery_time,
                  currency: (orders.Items[x].currency) ? orders.Items[x].currency : 'NOK',
                  comment: orders.Items[x].comment,
                  items: orders.Items[x].items,
                  createdAt: new Date().getTime(),
                  schedule_date: schedule_date,
                  day_index: orders.Items[x].schedule_dates[y]
                })

              }
            }
          }
        }
      }
    }

    scheduled_orders_list = arraySort(scheduled_orders_list, 'delivery_date')



    for (var x in scheduled_orders_list) {

      let childsOrders = await dynamoDbLib.call("scan", {
        TableName: process.env.cartTableName,
        FilterExpression: "parent_order = :parent_order and companyId = :companyId and companyId = :companyId",
        ExpressionAttributeValues: {
          ":parent_order": scheduled_orders_list[x].parent_order,
          ":companyId": scheduled_orders_list[x].companyId
        }
      });

      let childsOrdersFilitered = []

      for (var y in childsOrders.Items) {
        if (new Date(childsOrders.Items[y].delivery_date).getDay() == scheduled_orders_list[x].day_index && (scheduled_orders_list[x].delivery_date > childsOrders.Items[y].delivery_date)) {
          childsOrdersFilitered.push(childsOrders.Items[y])
        }
      }


      let childsOrdersList = arraySort(childsOrdersFilitered, 'delivery_date')

      let lastChildOrder = childsOrdersList[childsOrdersList.length - 1]

      const ordersSchedules = await dynamoDbLib.call("scan", {
        TableName: process.env.cartTableName,
        FilterExpression: "parent_order = :parent_order and delivery_date = :delivery_date and companyId = :companyId and companyId = :companyId",
        ExpressionAttributeValues: {
          ":parent_order": scheduled_orders_list[x].parent_order,
          ":delivery_date": scheduled_orders_list[x].delivery_date,
          ":companyId": scheduled_orders_list[x].companyId
        }
      });

      let last_child_delivery = (lastChildOrder) ? new Date(lastChildOrder.delivery_date) : new Date(0);
      let current_order_delivery = new Date(scheduled_orders_list[x].delivery_date);
      let Difference_In_Time = current_order_delivery.getTime() - last_child_delivery.getTime();
      let Difference_In_Days = Difference_In_Time / (1000 * 3600 * 24);

      //console.log(new Date(scheduled_orders_list[x].delivery_date), ordersSchedules.Items.length, (Difference_In_Days >= (scheduled_orders_list[x].schedule_duration_type * 7)), scheduled_orders_list[x].total, last_child_delivery, Difference_In_Days, (lastChildOrder) ? lastChildOrder.cartId : null);

      if (ordersSchedules.Items.length == 0 && (Difference_In_Days >= (scheduled_orders_list[x].schedule_duration_type * 7))) {

        let cart_id = uuid.v1()
        scheduled_orders_list[x].cartId = cart_id
        console.log(new Date(scheduled_orders_list[x].delivery_date), ordersSchedules.Items.length, (Difference_In_Days >= (scheduled_orders_list[x].schedule_duration_type * 7)), scheduled_orders_list[x].total, last_child_delivery, Difference_In_Days, (lastChildOrder) ? lastChildOrder.cartId : null);
        //console.log(new Date(scheduled_orders_list[x].delivery_date), scheduled_orders_list[x].total);

        var insertParams = {
          TableName: process.env.cartTableName,
          Item: scheduled_orders_list[x]
        };

        await dynamoDbLib.call("put", insertParams);


        /*
        // create order as task
        let location_id = ''
        let company_id = ''

        let total_quantity = 0

        for (var z in scheduled_orders_list[x].items) {

          total_quantity += scheduled_orders_list[x].items[z].quantity
        }


        let product = await dynamoDbLib.call("scan", {
          TableName: process.env.objectsTableName,
          FilterExpression: "objectType = :objectType and objectId = :objectId",
          ExpressionAttributeValues: {
            ":objectType": 'product',
            ":objectId": scheduled_orders_list[x].items[0].id
          }
        });
        // here we assume that all in one location, later we will update this.
        location_id = product.Items[0].objectLocation
        company_id = product.Items[0].objectCompanyId

        let locationParam = {
          TableName: process.env.objectsTableName,
          FilterExpression: "objectId = :objectId",
          ExpressionAttributeValues: {
            ":objectId": location_id
          }
        };

        const locationResult = await dynamoDbLib.call("scan", locationParam);

        let autoAssignTasks = (locationResult.Items[0].objectAutoAssignTasks) ? locationResult.Items[0].objectAutoAssignTasks : null


        if (product.Items[0].objectLocation && product.Items[0].objectCompanyId) {


          let task_dates = scheduled_orders_list[x].schedule_date

          let manufacturer_task_id = uuid.v1()

          await dynamoDbLib.call("put", {
            TableName: process.env.tasksTableName,
            Item: {
              taskId: manufacturer_task_id,
              assignerId: scheduled_orders_list[x].userId,
              tzo: scheduled_orders_list[x].tzo,
              assignees: (autoAssignTasks) ? autoAssignTasks.making : 'any-owner',
              taskName: total_quantity,
              taskType: 'manufacturer',
              taskDesc: null,
              taskRepeat: 'never',
              locationId: location_id,
              companyId: company_id,
              taskStatus: 'open',
              taskDates: [task_dates],
              taskTime: (scheduled_orders_list[x].delivery_time) ? scheduled_orders_list[x].delivery_time : '16:00:00',
              cartId: cart_id,
              shopId: scheduled_orders_list[x].shopId,
              createdAt: new Date().getTime()
            }
          });

          await SNS.publish({
            Message: manufacturer_task_id,
            TopicArn: process.env.generateCompaniesTasksSNSTopic
          }, function(err, data) {
            if (err) {
              callback(null, failure({
                status: err
              }));
            }

          });


          let package_task_id = uuid.v1()

          await dynamoDbLib.call("put", {
            TableName: process.env.tasksTableName,
            Item: {
              taskId: package_task_id,
              assignerId: scheduled_orders_list[x].userId,
              tzo: scheduled_orders_list[x].tzo,
              assignees: (autoAssignTasks) ? autoAssignTasks.packing : 'any-owner',
              taskName: total_quantity,
              taskType: 'package',
              taskDesc: null,
              taskRepeat: 'never',
              locationId: location_id,
              companyId: company_id,
              taskStatus: 'open',
              taskDates: [task_dates],
              taskTime: (scheduled_orders_list[x].delivery_time) ? scheduled_orders_list[x].delivery_time : '16:00:00',
              cartId: cart_id,
              shopId: scheduled_orders_list[x].shopId,
              createdAt: new Date().getTime()
            }
          });

          await SNS.publish({
            Message: package_task_id,
            TopicArn: process.env.generateCompaniesTasksSNSTopic
          }, function(err, data) {
            if (err) {
              callback(null, failure({
                status: err
              }));
            }

          });


          let ship_task_id = uuid.v1()
          await dynamoDbLib.call("put", {
            TableName: process.env.tasksTableName,
            Item: {
              taskId: ship_task_id,
              assignerId: scheduled_orders_list[x].userId,
              tzo: scheduled_orders_list[x].tzo,
              assignees: (autoAssignTasks) ? autoAssignTasks.shipping : 'any-owner',
              taskName: total_quantity,
              taskType: 'ship',
              taskDesc: null,
              taskRepeat: 'never',
              locationId: location_id,
              companyId: company_id,
              taskStatus: 'open',
              taskDates: [task_dates],
              taskTime: (scheduled_orders_list[x].delivery_time) ? scheduled_orders_list[x].delivery_time : '16:00:00',
              cartId: cart_id,
              shopId: scheduled_orders_list[x].shopId,
              createdAt: new Date().getTime()
            }
          });

          await SNS.publish({
            Message: ship_task_id,
            TopicArn: process.env.generateCompaniesTasksSNSTopic
          }, function(err, data) {
            if (err) {
              callback(null, failure({
                status: err
              }));
            }

          });

        }
        */
      }
    }


    callback(null, 'done');

  } catch (e) {
    console.log(e);
    bugsnagClient.notify(null, e)
    callback(null, e);
  }
}

function nextDay(x, a) {
  var now = new Date();
  now.setDate(now.getDate() + (x + (7 - now.getDay())) % 7);
  now.setDate(now.getDate() + (a - 7));
  return now
}