import uuid from "uuid";
import * as dynamoDbLib from "../../../libs/dynamodb-lib";
import * as bugsnagClient from "../../../libs/bugsnag";

import {
  success,
  failure
} from "../../../libs/response-lib";

import {
  is_level_permitted
} from "../../../libs/permissions";

import nodemailer from "nodemailer"
import {
  log_event
} from "../../../libs/logs";


const AWS = require('aws-sdk')
const SNS = new AWS.SNS();

import PowerOffice from "../../../libs/poweroffice";


export async function cart(event, context, callback) {


  try {

    let cognito_client = new AWS.CognitoIdentityServiceProvider()
    let pool_id = process.env.COGNITO_POOL_ID
    const access_scope = ['owner', 'manager', 'employee']

    const data = JSON.parse(event.body);
    

    var user_id = event.requestContext.identity.cognitoAuthenticationProvider.split('CognitoSignIn:')[1]
    //var user_id = 'ae1754ff-dba6-4ef7-a200-77e224b3f46b';
    const permissions = await is_level_permitted(user_id, access_scope);

    let shop_id = event.pathParameters.id
    //let shop_id = 'e3336010-c455-11e9-9ab0-7fc92b1e522d'
    const shopInfo = await dynamoDbLib.call("scan", {
      TableName: process.env.objectsTableName,
      FilterExpression: "objectType = :objectType and objectId = :objectId",
      ExpressionAttributeValues: {
        ":objectType": 'shop',
        ":objectId": shop_id
      }
    });

    let shop_name = shopInfo.Items[0].objectName
    let shop_currency = (shopInfo.Items[0].shopCurrency) ? shopInfo.Items[0].shopCurrency : 'NOK'
    let new_order_notification = (shopInfo.Items[0].newOrderNotification) ? shopInfo.Items[0].newOrderNotification : false

    const company = await dynamoDbLib.call("scan", {
      TableName: process.env.objectsTableName,
      FilterExpression: "objectType = :objectType and objectId = :objectId",
      ExpressionAttributeValues: {
        ":objectType": 'company',
        ":objectId": shopInfo.Items[0].objectCompanyId
      }
    });

    let shipping_tax = (company.Items[0].objectTax) ? (company.Items[0].objectTax.shipping / 100) : 0



    const shopProducts = await dynamoDbLib.call("scan", {
      TableName: process.env.objectsTableName,
      FilterExpression: "objectType = :objectType and objectShopId = :objectShopId",
      ExpressionAttributeValues: {
        ":objectType": 'product',
        ":objectShopId": shop_id
      }
    });

    let is_shop_owner = false


    if (shopInfo.Items[0].objectCompanyId == permissions.user_company_id) {
      is_shop_owner = true
    }

    let shipping_cost = (shopInfo.Items[0].shippingCost) ? shopInfo.Items[0].shippingCost : 0


    const companyInfo = await dynamoDbLib.call("scan", {
      TableName: process.env.objectsTableName,
      FilterExpression: "objectType = :objectType and objectCompanyId = :objectCompanyId",
      ExpressionAttributeValues: {
        ":objectType": 'company',
        ":objectCompanyId": shopInfo.Items[0].objectCompanyId
      }
    });

    let company_name = companyInfo.Items[0].objectName

    if (permissions.level_allowed || is_shop_owner) {

      let params1 = {
        TableName: process.env.userPermissionsTableName,
        FilterExpression: "userId = :userId and locationId = :locationId",
        ExpressionAttributeValues: {
          ":userId": permissions.user_company_id,
          ":locationId": data.delivery_point
        }
      };

      const result1 = await dynamoDbLib.call("scan", params1);


      if (result1.Items.length > 0 || is_shop_owner) {

        let today16 = new Date();
        today16.setHours(16, 0, 0, 0);

        let today16_time_stamp = today16.getTime()
        let now_time_stamp = new Date().getTime()
        let delivery_date_time_stamp = new Date(data.delivery_date).getTime()


        if (
          (delivery_date_time_stamp >= today16_time_stamp) ||
          (delivery_date_time_stamp <= today16_time_stamp && now_time_stamp <= today16_time_stamp ||
            true
          )
        ) {

          let customer_company_id = (data.company_id != null) ? data.company_id : permissions.user_company_id

          const customer_permission = await dynamoDbLib.call("scan", {
            TableName: process.env.userPermissionsTableName,
            FilterExpression: "shopId = :shopId and roleType = :roleType and isActive = :isActive and companyId = :companyId",
            ExpressionAttributeValues: {
              ":shopId": shop_id,
              ":roleType": 'shop_access_role',
              ":isActive": true,
              ":companyId": customer_company_id
            }
          });


          let discount = (customer_permission.Items[0].discount) ? parseFloat(customer_permission.Items[0].discount) : 0
          let shipping = (customer_permission.Items[0].shipping) ? customer_permission.Items[0].shipping : 0
          let discount_value = 0
          let freight = (shipping) ? shipping : shipping_cost
          let total = 0
          let total_mva = 0
          let mva = 0
          let total_quantity = 0

          for (var x in data.items) {

            for (var y in shopProducts.Items) {
              if (shopProducts.Items[y].objectId == data.items[x].id) {

                let applied_discount = null
                let product_discount = (shopProducts.Items[y].objectMaximumDiscount) ? parseFloat(shopProducts.Items[y].objectMaximumDiscount) : null
                if (product_discount != null) {
                  if (discount >= product_discount) {
                    applied_discount = product_discount
                  } else {
                    applied_discount = discount
                  }
                } else {
                  applied_discount = discount
                }

                data.items[x].discount = applied_discount


                total += (parseFloat(shopProducts.Items[y].objectPrice) * data.items[x].quantity)
                total = parseFloat(total * (1 - parseFloat(applied_discount / 100)))
                total_mva += ((parseFloat(shopProducts.Items[y].objectPrice * (shopProducts.Items[y].objectMVA > 0 ? (parseFloat(shopProducts.Items[y].objectMVA) / 100 + 1) : 1)) * data.items[x].quantity) * ((applied_discount > 0) ? (1 - (applied_discount / 100)) : 1))
                mva += ((parseFloat(shopProducts.Items[y].objectPrice * (shopProducts.Items[y].objectMVA > 0 ? (parseFloat(shopProducts.Items[y].objectMVA) / 100) : 0)) * data.items[x].quantity) * ((applied_discount > 0) ? (1 - (applied_discount / 100)) : 1))
                discount_value += ((parseFloat(shopProducts.Items[y].objectPrice) * data.items[x].quantity) * ((applied_discount > 0) ? ((applied_discount / 100)) : 1))
                //total_mva = parseFloat(total_mva * (1 - parseFloat(applied_discount / 100)))
                total_quantity += data.items[x].quantity
              }
            }


          }

          //mva = total_mva - total

          if (discount > 0) {
            //discount_value = total * (discount / 100)
            //total = total - discount_value
            //total_mva = total_mva - (total_mva * (discount / 100))
          }

          total += parseFloat(freight)
          total_mva += parseFloat(freight * (1 + shipping_tax))
          mva += parseFloat(freight * shipping_tax)

          if (total_quantity > 0) {

            let payment_status = 'unpaid'
            let transaction_id = null
            let payment_method = data.payment_method

            if (data.schedule_type != 'weekly' && shopInfo.Items[0].objectPaymentMethod == 'stripe' && payment_method != 'manual') {


              const payment_method_details = await dynamoDbLib.call("scan", {
                TableName: process.env.objectsTableName,
                FilterExpression: "objectType = :objectType and objectId = :objectId",
                ExpressionAttributeValues: {
                  ":objectType": 'payment_method',
                  ":objectId": payment_method
                }
              });

              const integrationParams = {
                TableName: process.env.integrationsTableName,
                FilterExpression: "integrationType = :integrationType and integrationId = :integrationId",
                ExpressionAttributeValues: {
                  ":integrationType": 'stripe',
                  ":integrationId": payment_method_details.Items[0].objectIntegrationId
                }
              }

              const stripeIntegrationResult = await dynamoDbLib.call("scan", integrationParams);
              let stripeIntegration = stripeIntegrationResult.Items[0]
              let pkey = stripeIntegration.integrationPKey
              let skey = stripeIntegration.integrationSKey

              const stripe = require('stripe')(skey);

              const paymentIntent = await stripe.paymentIntents.create({
                amount: parseInt(parseFloat(Math.round(total_mva * 100) / 100).toFixed(2) * 100),
                currency: shop_currency.toLowerCase(),
                payment_method_types: ['card'],
                customer: payment_method_details.Items[0].objectStripeCustomerId,
                payment_method: payment_method_details.Items[0].objectPaymentMethodId,
                off_session: true,
                confirm: true
              });
              
              if (paymentIntent.status == 'succeeded') {
                payment_status = 'paid'
                transaction_id = uuid.v1()
              }

              await dynamoDbLib.call("put", {
                TableName: process.env.transactionsTableName,
                Item: {
                  transactionId: transaction_id,
                  transactionDetails: paymentIntent,
                  createdAt: new Date().getTime(),
                  PaymentMethodId: payment_method
                }
              });

            }

            let is_edit = (data.cartId) ? true : false
            let cart_id = uuid.v1()

            if (!is_edit) {

              const params = {
                TableName: process.env.cartTableName,
                Item: {
                  cartId: cart_id,
                  userId: user_id,
                  freight: freight,
                  total: parseFloat(Math.round(total_mva * 100) / 100).toFixed(2),
                  mva: mva,
                  discount: discount,
                  discount_value: discount_value,
                  shopId: shop_id,
                  schedule_dates: data.schedule_dates,
                  schedule_type: data.schedule_type,
                  schedule_duration_type: (data.schedule_type == 'weekly') ? data.schedule_duration_type : null,
                  orderStatus: 'pending',
                  paymentStatus: payment_status,
                  paymentMethod: (shopInfo.Items[0].objectPaymentMethod) ? shopInfo.Items[0].objectPaymentMethod : 'manual',
                  PaymentMethodId: payment_method,
                  companyId: (data.company_id != null) ? data.company_id : permissions.user_company_id,
                  on_behalf_of: data.customer_employee_id,
                  delivery_date: delivery_date_time_stamp,
                  delivery_point: data.delivery_point,
                  delivery_time: data.delivery_time,
                  recurring_start_date: (data.recurring_start_date) ? data.recurring_start_date : null,
                  comment: (data.comment) ? data.comment : null,
                  items: data.items,
                  tzo: (data.tzo) ? data.tzo : 0,
                  transactionId: transaction_id,
                  currency: shop_currency,
                  createdAt: new Date().getTime()
                }
              };

              await dynamoDbLib.call("put", params);


              await log_event(
                user_id,
                permissions.user_full_name,
                permissions.user_company_name,
                shopInfo.Items[0].objectCompanyId,
                "order_created", {
                  cartId: cart_id,
                  orderType: 'single',
                  created_from: 'shop'
                },
                shop_id,
                shopInfo.Items[0].objectName
              )

            } else {

              cart_id = data.cartId


              let this_cart = await dynamoDbLib.call("scan", {
                TableName: process.env.cartTableName,
                FilterExpression: "cartId = :cartId",
                ExpressionAttributeValues: {
                  ":cartId": cart_id,
                }
              });


              if (this_cart.Items[0].powerOfficeInvoiceIdentifier) {
                if (shopInfo.Items[0].objectPowerOfficeIntegration) {

                  const integrationParams = {
                    TableName: process.env.integrationsTableName,
                    FilterExpression: "integrationId = :integrationId",
                    ExpressionAttributeValues: {
                      ":integrationId": shopInfo.Items[0].objectPowerOfficeIntegration
                    }
                  };

                  const integrations = await dynamoDbLib.call("scan", integrationParams);
                  let poweroffice_client_key = integrations.Items[0].integrationPOAppKey
                  let poweroffice_secrect_key = integrations.Items[0].integrationPOClientKey

                  let poweroffice = await new PowerOffice(process.env.POWEROFFICE_APP_KEY, poweroffice_secrect_key)

                  let invoice = await poweroffice.getInvoice(this_cart.Items[0].powerOfficeInvoiceIdentifier)
                
                  if (invoice.data) {
                  if (invoice.data.status == 0) {
                    // if invoice still draft, we delete it.
                    await poweroffice.deleteInvoice(this_cart.Items[0].powerOfficeInvoiceIdentifier)

                  } else {
                    // we create a reverse invoice with minus values.
                    let invoice_lines = []

                    for (var z in invoice.data.outgoingInvoiceLines) {
                      invoice_lines.push({
                        SortOrder: invoice.data.outgoingInvoiceLines[z].sortOrder,
                        ProductCode: invoice.data.outgoingInvoiceLines[z].productCode,
                        Description: invoice.data.outgoingInvoiceLines[z].description,
                        Quantity: invoice.data.outgoingInvoiceLines[z].quantity,
                        DiscountPercent: invoice.data.outgoingInvoiceLines[z].discountPercent,
                        UnitPrice: -1 * invoice.data.outgoingInvoiceLines[z].unitPrice,
                        NetAmount: -1 * invoice.data.outgoingInvoiceLines[z].netAmount
                      })
                    }

                    let order = await poweroffice.createInvoice({
                      OrderDate: invoice.data.orderDate,
                      CustomerCode: invoice.data.customerCode,
                      CustomerEmail: invoice.data.customerEmail,
                      CustomerReference: invoice.data.customerReference,
                      DeliveryAddressId: invoice.data.deliveryAddressId,
                      DeliveryDate: invoice.data.deliveryDate,
                      InvoiceDeliveryType: invoice.data.invoiceDeliveryType,
                      PurchaseOrderNo: invoice.data.purchaseOrderNo,
                      Status: 0,
                      OutgoingInvoiceLines: invoice_lines
                    })
                  }
                }

                }
              }

              const result = await dynamoDbLib.call("update", {
                TableName: process.env.cartTableName,

                Key: {
                  cartId: data.cartId
                },
                ExpressionAttributeNames: {
                  '#items': 'items',
                  '#total': 'total',
                  '#comment': 'comment',
                },
                UpdateExpression: "SET updatedBy = :updatedBy,freight = :freight, #total = :total, mva = :mva, discount = :discount, discount_value = :discount_value, schedule_dates = :schedule_dates, schedule_type = :schedule_type, schedule_duration_type = :schedule_duration_type, orderStatus = :orderStatus, paymentStatus = :paymentStatus, paymentMethod = :paymentMethod, PaymentMethodId = :PaymentMethodId, companyId = :companyId, on_behalf_of = :on_behalf_of, delivery_date = :delivery_date, delivery_point = :delivery_point, delivery_time = :delivery_time, recurring_start_date = :recurring_start_date, #comment = :comment, #items = :items, updatedAt = :updatedAt, powerOfficeInvoiceId = :powerOfficeInvoiceId, powerOfficeInvoiceIdentifier = :powerOfficeInvoiceIdentifier",
                ExpressionAttributeValues: {
                  ":updatedBy": user_id,
                  ":freight": freight,
                  ":total": parseFloat(Math.round(total_mva * 100) / 100).toFixed(2),
                  ":mva": mva,
                  ":discount": discount,
                  ":discount_value": discount_value,
                  ":schedule_dates": data.schedule_dates,
                  ":schedule_type": data.schedule_type,
                  ":schedule_duration_type": (data.schedule_type == 'weekly') ? data.schedule_duration_type : null,
                  ":orderStatus": 'pending',
                  ":paymentStatus": payment_status,
                  ":paymentMethod": (shopInfo.Items[0].objectPaymentMethod) ? shopInfo.Items[0].objectPaymentMethod : 'manual',
                  ":PaymentMethodId": payment_method,
                  ":companyId": (data.company_id != null) ? data.company_id : permissions.user_company_id,
                  ":on_behalf_of": data.customer_employee_id,
                  ":delivery_date": delivery_date_time_stamp,
                  ":delivery_point": data.delivery_point,
                  ":delivery_time": data.delivery_time,
                  ":recurring_start_date": (data.recurring_start_date) ? data.recurring_start_date : null,
                  ":comment": (data.comment) ? data.comment : null,
                  ":items": data.items,
                  ":powerOfficeInvoiceId": null,
                  ":powerOfficeInvoiceIdentifier": null,
                  ":updatedAt": new Date().getTime()
                },
                ReturnValues: "ALL_NEW"
              });

              await log_event(
                user_id,
                permissions.user_full_name,
                permissions.user_company_name,
                shopInfo.Items[0].objectCompanyId,
                "order_edited", {
                  cartId: cart_id,
                  orderType: 'single',
                  created_from: 'shop'
                },
                shop_id,
                shopInfo.Items[0].objectName
              )

            }


            if (data.schedule_type != 'weekly') {
              /*

              let location_id = ''
              let company_id = ''


              let product = await dynamoDbLib.call("scan", {
                TableName: process.env.objectsTableName,
                FilterExpression: "objectType = :objectType and objectId = :objectId",
                ExpressionAttributeValues: {
                  ":objectType": 'product',
                  ":objectId": data.items[0].id
                }
              });
              // here we assume that all in one location, later we will update this.
              location_id = product.Items[0].objectLocation
              company_id = product.Items[0].objectCompanyId


              let locationParam = {
                TableName: process.env.objectsTableName,
                FilterExpression: "objectId = :objectId",
                ExpressionAttributeValues: {
                  ":objectId": location_id
                }
              };

              const locationResult = await dynamoDbLib.call("scan", locationParam);

              let autoAssignTasks = (locationResult.Items[0].objectAutoAssignTasks) ? locationResult.Items[0].objectAutoAssignTasks : null

              if (locationResult.Items[0].objectAllowTasks) {

                if (product.Items[0].objectLocation && product.Items[0].objectCompanyId) {

                  let manufacturer_task_id = uuid.v1()

                  await dynamoDbLib.call("put", {
                    TableName: process.env.tasksTableName,
                    Item: {
                      taskId: manufacturer_task_id,
                      assignerId: user_id,
                      assignees: (autoAssignTasks) ? autoAssignTasks.making : 'any-owner',
                      taskName: total_quantity,
                      taskType: 'manufacturer',
                      taskDesc: null,
                      taskRepeat: 'never',
                      locationId: location_id,
                      companyId: company_id,
                      taskStatus: 'open',
                      tzo: (data.tzo) ? data.tzo : 0,
                      taskDates: [data.delivery_date],
                      taskTime: (data.delivery_time) ? data.delivery_time : '16:00:00',
                      cartId: cart_id,
                      shopId: shop_id,
                      createdAt: new Date().getTime()
                    }
                  });

                  await SNS.publish({
                    Message: manufacturer_task_id,
                    TopicArn: process.env.generateCompaniesTasksSNSTopic
                  }, function(err, data) {
                    if (err) {
                      callback(null, failure({
                        status: err
                      }));
                    }
                    // sns sent. good job
                  });


                  let package_task_id = uuid.v1()

                  await dynamoDbLib.call("put", {
                    TableName: process.env.tasksTableName,
                    Item: {
                      taskId: package_task_id,
                      assignerId: user_id,
                      assignees: (autoAssignTasks) ? autoAssignTasks.packing : 'any-owner',
                      taskName: total_quantity,
                      taskType: 'package',
                      taskDesc: null,
                      taskRepeat: 'never',
                      locationId: location_id,
                      companyId: company_id,
                      taskStatus: 'open',
                      tzo: (data.tzo) ? data.tzo : 0,
                      taskDates: [data.delivery_date],
                      taskTime: (data.delivery_time) ? data.delivery_time : '16:00:00',
                      cartId: cart_id,
                      shopId: shop_id,
                      createdAt: new Date().getTime()
                    }
                  });

                  await SNS.publish({
                    Message: package_task_id,
                    TopicArn: process.env.generateCompaniesTasksSNSTopic
                  }, function(err, data) {
                    if (err) {
                      callback(null, failure({
                        status: err
                      }));
                    }
                    // sns sent. good job
                  });


                  let ship_task_id = uuid.v1()
                  await dynamoDbLib.call("put", {
                    TableName: process.env.tasksTableName,
                    Item: {
                      taskId: ship_task_id,
                      assignerId: user_id,
                      assignees: (autoAssignTasks) ? autoAssignTasks.shipping : 'any-owner',
                      taskName: total_quantity,
                      taskType: 'ship',
                      taskDesc: null,
                      taskRepeat: 'never',
                      locationId: location_id,
                      companyId: company_id,
                      taskStatus: 'open',
                      tzo: (data.tzo) ? data.tzo : 0,
                      taskDates: [data.delivery_date],
                      taskTime: (data.delivery_time) ? data.delivery_time : '16:00:00',
                      cartId: cart_id,
                      shopId: shop_id,
                      createdAt: new Date().getTime()
                    }
                  });


                  await SNS.publish({
                    Message: ship_task_id,
                    TopicArn: process.env.generateCompaniesTasksSNSTopic
                  }, function(err, data) {
                    if (err) {
                      callback(null, failure({
                        status: err
                      }));
                    }
                    // sns sent. good job
                  });

                }
              }
              */
            }

            await SNS.publish({
              Message: shop_id,
              TopicArn: process.env.generateRecurringOrdersSNSTopic
            }, function(err, data) {
              if (err) {
                callback(null, failure({
                  status: err
                }));
              }
              // sns sent. good job
            });


            await SNS.publish({
              Message: cart_id,
              TopicArn: process.env.sendOrderConfirmationSecondaryCustomerSNSTopic
            }, function(err, data) {
              if (err) {
                callback(null, failure({
                  status: err
                }));
              }
              // sns sent. good job
            });

            if (new_order_notification) {
              await SNS.publish({
                Message: cart_id,
                TopicArn: process.env.sendOrderConfirmationSecondaryPrimarySNSTopic
              }, function(err, data) {
                if (err) {
                  callback(null, failure({
                    status: err
                  }));
                }
                // sns sent. good job
              });
            }


            callback(null, success(cart_id));

          } else {
            callback(null, failure({
              status: '0 products ordered'
            }));
          }


        } else {


          callback(null, failure({
            status: 'You are not allwed to place an order at this time'
          }));

        }

      } else {


        callback(null, failure({
          status: 'you do not have access to this delivery point'
        }));

      }

    } else {

      callback(null, failure({
        status: 'you do not have access to this api call'
      }));

    }

  } catch (e) {
    console.log(e);
    bugsnagClient.notify(user_id, e)
    callback(null, failure({
      status: e
    }));
  }

}