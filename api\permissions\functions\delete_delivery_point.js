import * as dynamoDbLib from "../../../libs/dynamodb-lib";
import * as bugsnagClient from "../../../libs/bugsnag";
import {
  success,
  failure
} from "../../../libs/response-lib";

import {
  is_level_permitted,
  is_object_permitted
} from "../../../libs/permissions";

export async function permission(event, context, callback) {


  try {
    const access_scope = ['owner', 'manager', 'employee']

    var user_id = event.requestContext.identity.cognitoAuthenticationProvider.split('CognitoSignIn:')[1]

    const permissions = await is_level_permitted(user_id, access_scope);
    if (permissions.level_allowed) {

      let go = true
      /*
      if (permissions.location_allowed) {
        go = true
      } else {

        const location_permission = await is_object_permitted(user_id, permissions.user_company_id, data.locationId);

        if (location_permission.is_object_allowed) {
          go = true
        }
      }
      */

      if (go) {



        const permissionsParam = {
          TableName: process.env.userPermissionsTableName,
          FilterExpression: "userId = :userId",
          ExpressionAttributeValues: {
            ":userId": event.pathParameters.id
          }
        };

        let permissionsResult = await dynamoDbLib.call("scan", permissionsParam);

        for (var x in permissionsResult.Items) {
          if (permissionsResult.Items[x].locationId) {
            let permission_id = permissionsResult.Items[x].permissionId

            let delivery_point = await dynamoDbLib.call("scan", {

              TableName: process.env.objectsTableName,
              FilterExpression: "objectType = :objectType and objectId = :objectId and objectShopId = :objectShopId",
              ExpressionAttributeValues: {
                ":objectType": 'delivery_point',
                ":objectId": permissionsResult.Items[x].locationId,
                ":objectShopId": event.pathParameters.shop
              }
            });

            if (delivery_point.Items.length == 1) {

              const params = {
                TableName: process.env.userPermissionsTableName,
                Key: {
                  permissionId: permission_id

                }
              };
              const result = await dynamoDbLib.call("delete", params);
            }


          }
        }

        callback(null, success({
          status: true
        }));

      } else {

        callback(null, failure({
          status: 'you do not have access to this api call'
        }));

      }

    } else {

      callback(null, failure({
        status: 'you do not have access to this api call'
      }));

    }

  } catch (e) {
    bugsnagClient.notify(user_id, e)
    callback(null, failure({
      error: e
    }))
  }

}