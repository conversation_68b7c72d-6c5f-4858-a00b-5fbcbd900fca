import * as dynamoDbLib from "../../../libs/dynamodb-lib";
import * as bugsnagClient from "../../../libs/bugsnag";
import {
  success,
  failure
} from "../../../libs/response-lib";

import {
  is_level_permitted
} from "../../../libs/permissions";

export async function bills(event, context, callback) {


  try {
    const access_scope = ['owner', 'manager', 'employee']

    var user_id = event.requestContext.identity.cognitoAuthenticationProvider.split('CognitoSignIn:')[1]
    let payment_status = event.pathParameters.id

    //var user_id = '62f6d0d4-4d4c-4ce3-82c9-0d92b4b21e3e'
    //var payment_status = 'in_progress'

    const permissions = await is_level_permitted(user_id, access_scope);

    if (permissions.level_allowed) {

      const bills = await dynamoDbLib.call("scan", {
        TableName: process.env.billsTableName,
        FilterExpression: "companyId = :billCompanyId and paymentStatus = :paymentStatus",
        ExpressionAttributeValues: {
          ":billCompanyId": permissions.user_company_id,
          ":paymentStatus": payment_status
        }
      });

      callback(null, success(bills.Items));

    } else {

      callback(null, failure({
        status: 'you do not have access to this api call'
      }));
    }
  } catch (e) {
    console.log(e);
    bugsnagClient.notify(user_id, e)
    callback(null, failure({
      status: e
    }));
  }
}