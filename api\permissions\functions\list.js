import * as dynamoDbLib from "../../../libs/dynamodb-lib";
import * as bugsnagClient from "../../../libs/bugsnag";
import {
  success,
  failure
} from "../../../libs/response-lib";

export async function permissions(event, context, callback) {

  try {

    var user_id = event.requestContext.identity.cognitoAuthenticationProvider.split('CognitoSignIn:')[1]

    const params = {
      TableName: process.env.userPermissionsTableName,
      FilterExpression: "userId = :userId",
      ExpressionAttributeValues: {
        ":userId": user_id
      }
    };

    const result = await dynamoDbLib.call("scan", params);

    let locations = ['owner', 'manager']

    for (var x in result.Items) {

      locations.push(result.Items[x].locationId)
    }

    callback(null, success(locations))

  } catch (e) {
    bugsnagClient.notify(user_id, e)
    callback(null, failure({
      status: e
    }));
  }

}