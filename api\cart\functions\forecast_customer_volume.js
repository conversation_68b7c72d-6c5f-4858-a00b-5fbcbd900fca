import * as dynamoDbLib from "../../../libs/dynamodb-lib";
import * as bugsnagClient from "../../../libs/bugsnag";
import {
  success,
  failure
} from "../../../libs/response-lib";

import {
  is_level_permitted
} from "../../../libs/permissions";

var arraySort = require('array-sort')
const groupArray = require('group-array');

export async function products(event, context, callback) {

  try {

    const access_scope = ['owner', 'manager', 'employee']
    const data = JSON.parse(event.body);
    //let data = {}

    var user_id = event.requestContext.identity.cognitoAuthenticationProvider.split('CognitoSignIn:')[1]
    let shop_id = event.pathParameters.id

    //var user_id = '8e41a015-604b-4d46-8337-e67584b8a852'
    //let shop_id = '58340300-2eb6-11e9-96c1-f3dbad797ef9'


    let shopParams = {
      TableName: process.env.objectsTableName,
      FilterExpression: "objectType = :objectType and objectId = :objectId",
      ExpressionAttributeValues: {
        ":objectType": 'shop',
        ":objectId": shop_id
      }
    }

    const shopResult = await dynamoDbLib.call("scan", shopParams);
    const permissions = await is_level_permitted(user_id, access_scope);

    let is_shop_owner = false

    if (shopResult.Items[0].objectCompanyId == permissions.user_company_id) {
      is_shop_owner = true
    }

    if (permissions.level_allowed || is_shop_owner) {

      const permissionParams = {
        TableName: process.env.userPermissionsTableName,
        FilterExpression: "roleType = :roleType and companyId = :companyId and shopId = :shopId",
        ExpressionAttributeValues: {
          ":roleType": 'shop_access_role',
          ":companyId": permissions.user_company_id,
          ":shopId": shop_id
        }
      }

      const permissionResult = await dynamoDbLib.call("scan", permissionParams);

      if (permissionResult.Items.length == 1 || is_shop_owner) {


        //let from = data.filters.from
        //let to = data.filters.to

        //let from = 1572555600000
        //let to = 1573160399000

        let from = data.filters.from
        let to = data.filters.to


        var days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];

        var startDate = new Date(from + 10800000);
        var endDate = new Date(to + 10800000);

        let fields = new Object();

        var getDateArray = function(start, end) {
          var arr = new Array();
          var dt = new Date(start);
          while (dt <= end) {
            let date = dt.getFullYear() + '-' + pad((dt.getMonth() + 1), 2) + '-' + pad(dt.getDate(), 2)
            arr.push({
              index: dt.getDay(),
              name: days[dt.getDay()],
              date: date
            });
            fields[date] = date
            dt.setDate(dt.getDate() + 1);
          }
          return arr;
        }

        var dateArr = getDateArray(startDate, endDate);

        let shop_categories = await dynamoDbLib.call("scan", {
          TableName: process.env.objectsTableName,
          FilterExpression: "objectType = :objectType and objectShopId = :objectShopId",
          ExpressionAttributeValues: {
            ":objectType": 'category',
            ":objectShopId": shop_id
          }
        });

        let shop_products = await dynamoDbLib.call("scan", {
          TableName: process.env.objectsTableName,
          FilterExpression: "objectType = :objectType and objectShopId = :objectShopId",
          ExpressionAttributeValues: {
            ":objectType": 'product',
            ":objectShopId": shop_id
          }
        });



        let forecastedOrders = []
        let orders;

        //from = from - 10800000
        //to = to - 10800000


        let deliveryFrequency = shopResult.Items[0].deliveryFrequency
        let orderDates = (shopResult.Items[0].orderDates) ? shopResult.Items[0].orderDates : []

        let shops_filter_expression = ''
        let shops_filter_expression_values = {
          ":date1": from,
          ":date2": to,
          ":schedule_type": 'once',
          ":shopId": shop_id,
          ":orderStatus": 'canceled',
          ":companyId": (data.company_id != null) ? data.company_id : permissions.user_company_id
        }


        const productsResult = await dynamoDbLib.call("scan", {
          TableName: process.env.objectsTableName,
          FilterExpression: "objectType = :objectType  and objectShopId = :objectShopId and objectVisibility = :objectVisibility",
          ExpressionAttributeValues: {
            ":objectType": 'product',
            ":objectShopId": shop_id,
            ":objectVisibility": true
          }
        });


        orders = await dynamoDbLib.call("scan", {
          TableName: process.env.cartTableName,
          FilterExpression: "companyId = :companyId and delivery_date >= :date1 and orderStatus <> :orderStatus and shopId = :shopId and delivery_date <= :date2 and schedule_type = :schedule_type ",
          ExpressionAttributeValues: shops_filter_expression_values
        });


        let products_ids = []

        for (var x in orders.Items) {

          let delivery_date = new Date(orders.Items[x].delivery_date + 10800000)

          delivery_date = delivery_date.getFullYear() + '-' + pad((delivery_date.getMonth() + 1), 2) + '-' + pad(delivery_date.getDate(), 2)

          for (var y in orders.Items[x].items) {

            for (var z in dateArr) {

              products_ids.push(orders.Items[x].items[y].id)

              let min = 0
              let max = 1000

              for (var k in productsResult.Items) {
                if (orders.Items[x].items[y].id == productsResult.Items[k].objectId) {
                  min = (productsResult.Items[k].objectMinimumOrderQuantity) ? productsResult.Items[k].objectMinimumOrderQuantity : min
                  max = (productsResult.Items[k].objectMaximumOrderQuantity) ? productsResult.Items[k].objectMaximumOrderQuantity : max
                }
              }

              let product_name = ''
              for (var m in shop_products.Items) {
                if (shop_products.Items[m].objectId == orders.Items[x].items[y].id) {
                  product_name = shop_products.Items[m].objectName
                }
              }

              if (dateArr[z].date === delivery_date) {

                let product_quantity = orders.Items[x].items[y].quantity

                if (deliveryFrequency == 'weekly' && !orderDates.includes(dateArr[z].index.toString())) {
                  product_quantity = 'Unavailable'
                }

                forecastedOrders.push({
                  product_id: orders.Items[x].items[y].id,
                  product_name: product_name,
                  product_quantity: product_quantity,
                  delivery_date: dateArr[z].date,
                  product_min: min,
                  product_max: max
                })
              } else {

                let product_quantity = 0

                if (deliveryFrequency == 'weekly' && !orderDates.includes(dateArr[z].index.toString())) {
                  product_quantity = 'Unavailable'
                }

                forecastedOrders.push({
                  product_id: orders.Items[x].items[y].id,
                  product_name: product_name,
                  product_quantity: product_quantity,
                  delivery_date: dateArr[z].date,
                  product_min: min,
                  product_max: max
                })


              }

            }
          }
        }



        for (var x in productsResult.Items) {

          if (!products_ids.includes(productsResult.Items[x].objectId)) {
            for (var z in dateArr) {

              let product_quantity = 0

              if (deliveryFrequency == 'weekly' && !orderDates.includes(dateArr[z].index.toString())) {
                product_quantity = 'Unavailable'
              }

              let product_name = ''
              for (var m in shop_products.Items) {
                if (shop_products.Items[m].objectId == productsResult.Items[x].objectId) {
                  product_name = shop_products.Items[m].objectName
                }
              }


              forecastedOrders.push({
                product_id: productsResult.Items[x].objectId,
                product_name: product_name,
                product_quantity: product_quantity,
                delivery_date: dateArr[z].date,
                product_min: (productsResult.Items[x].objectMinimumOrderQuantity) ? productsResult.Items[x].objectMinimumOrderQuantity : 0,
                product_max: (productsResult.Items[x].objectMaximumOrderQuantity) ? productsResult.Items[x].objectMaximumOrderQuantity : 1000
              })

            }
          }

        }


        forecastedOrders = arraySort(forecastedOrders, 'delivery_date')
        forecastedOrders = groupArray(forecastedOrders, 'product_id', 'delivery_date')

        let cells_data = []
        let count = 0
        for (var x in forecastedOrders) {

          cells_data[count] = {}
          cells_data[count]['Product ID'] = ''
          cells_data[count]['Product Category'] = ''
          cells_data[count]['Product Name'] = ''

          for (var y in forecastedOrders[x]) {

            let qty = 0

            for (var z in forecastedOrders[x][y]) {

              if (Number.isInteger(parseInt(forecastedOrders[x][y][z].product_quantity))) {
                qty += parseInt(forecastedOrders[x][y][z].product_quantity)
              } else {
                qty = forecastedOrders[x][y][z].product_quantity
              }

              let category_name = ''

              for (var n in shop_products.Items) {

                if (forecastedOrders[x][y][z].product_id == shop_products.Items[n].objectId) {
                  for (var k in shop_categories.Items) {
                    if (shop_categories.Items[k].objectId == shop_products.Items[n].objectCategory) {
                      category_name = shop_categories.Items[k].objectName
                    }
                  }
                }


              }

              cells_data[count]['Product ID'] = forecastedOrders[x][y][z].product_id
              cells_data[count]['Product Category'] = category_name
              cells_data[count]['Product Name'] = forecastedOrders[x][y][z].product_name
              cells_data[count]['Min Order Quantity'] = forecastedOrders[x][y][z].product_min
              cells_data[count]['Max Order Quantity'] = forecastedOrders[x][y][z].product_max
            }
            cells_data[count][y] = qty

          }
          count++
        }

        cells_data = arraySort(cells_data, 'Product Category')

        callback(null, success({
          cells_data
        }));

      } else {

        callback(null, failure({
          status: 'you do not have access to this api call'
        }));

      }

    } else {

      callback(null, failure({
        status: 'you do not have access to this api call'
      }));

    }



  } catch (e) {
    bugsnagClient.notify(user_id, e)
    callback(null, failure({
      status: e
    }));
  }

}

function pad(num, size) {
  var s = num + "";
  while (s.length < size) s = "0" + s;
  return s;
}