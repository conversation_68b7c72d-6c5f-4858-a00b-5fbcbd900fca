import {
  success,
  failure
} from "../../../libs/response-lib";
import * as dynamoDbLib from "../../../libs/dynamodb-lib";
import * as bugsnagClient from "../../../libs/bugsnag";

const AWS = require('aws-sdk')
const cognito_client = new AWS.CognitoIdentityServiceProvider()

export async function user(event, context, callback) {


  try {

    const data = JSON.parse(event.body);

    var pool_id = process.env.COGNITO_POOL_ID
    var company_id = data.company_id

    const compaines = await dynamoDbLib.call("scan", {
      TableName: process.env.objectsTableName,
      FilterExpression: "objectType = :objectType and objectCompanyId = :objectCompanyId",
      ExpressionAttributeValues: {
        ":objectType": 'company',
        ":objectCompanyId": company_id
      }
    });

    if (compaines.Items.length == 1) {


      var params = {
        UserPoolId: pool_id,
        Username: data.email,
        //DesiredDeliveryMediums: ['EMAIL'],
        //  MessageAction: 'SUPPRESS',
        ForceAliasCreation: false,
        UserAttributes: [{
            Name: 'custom:first_name',
            Value: data.first_name
          },
          {
            Name: 'custom:last_name',
            Value: data.last_name
          },
          {
            Name: 'custom:user_phone',
            Value: data.phone
          },
          {
            Name: 'custom:user_type',
            Value: 'employee'
          },
          {
            Name: 'name',
            Value: company_id
          },
          {
            Name: 'custom:company_id',
            Value: company_id
          },

          {
            Name: 'email',
            Value: data.email
          },
          {
            Name: 'custom:created_as',
            Value: data.created_as
          },
          {
            Name: 'email_verified',
            Value: 'true'
          },
          {
            Name: 'custom:language',
            Value: 'no'
          }
        ]
      };

      let new_user = await cognito_client.adminCreateUser(params).promise()

      callback(null, success(new_user))

    } else {

      callback(null, failure({
        error: 'no company found'
      }))
    }


  } catch (e) {
    bugsnagClient.notify(user_id, e)
    callback(null, failure({
      error: e
    }))
  }

}