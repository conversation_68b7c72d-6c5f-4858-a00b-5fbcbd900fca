import * as dynamoDbLib from "../../../libs/dynamodb-lib";
import {
  success,
  failure
} from "../../../libs/response-lib";

export async function sensors(event, context, callback) {

  var user_id = event.requestContext.identity.cognitoAuthenticationProvider.split('CognitoSignIn:')[1]

  const params = {
    TableName: process.env.objectsTableName,
    KeyConditionExpression: "userId = :userId  and objectId = :objectId",
    FilterExpression: "objectType = :objectType",
    ExpressionAttributeValues: {
      ":userId": user_id,
      ":objectType": 'device',
      ":objectId": event.pathParameters.id
    }
  };

  try {

    const devicesResult = await dynamoDbLib.call("query", params);
    const devicesData = devicesResult.Items;

    var device = {}

    const sensorParam = {
      TableName: process.env.objectsTableName,
      //  KeyConditionExpression: "userId = :userId",
      FilterExpression: "objectType = :objectType and objectParent = :objectParent",
      ExpressionAttributeValues: {
        //  ":userId": user_id,
        ":objectType": 'sensor',
        ":objectParent": event.pathParameters.id
      }
    };

    let sensorsResult = await dynamoDbLib.call("scan", sensorParam);
    const sensorsData = sensorsResult.Items;

    let sensors = []

    for (var skey in sensorsData) {

      let sensorId = sensorsData[skey].objectId

      const sensorDataParam = {
        TableName: process.env.sensorsDataLogsTableName,
        ScanIndexForward: false,
        KeyConditionExpression: "sensorId = :sensorId and createdAt > :createdAt",
        ExpressionAttributeValues: {
          ":sensorId": sensorId,
          ":createdAt": (parseInt(new Date().getTime()) - (event.pathParameters.duration * 60 * 60 * 1000))
        }
      };

      let sensorsDataResult = await dynamoDbLib.call("query", sensorDataParam);

      let sensorData = []


      for (var x in sensorsDataResult.Items) {


        var color = ''

        if (devicesData[0].objectKind == 'refrigerator') {

          if (sensorsDataResult.Items[x].sensorData.state.reported.temp < 0) {
            color = '#d3e3f7'
          } else if (sensorsDataResult.Items[x].sensorData.state.reported.temp >= 0 && sensorsDataResult.Items[x].sensorData.state.reported.temp <= 4) {
            color = '#8ee4bf'
          } else {
            color = '#ff8080'
          }

        } else if (devicesData[0].objectKind == 'freezer') {

          if (sensorsDataResult.Items[x].sensorData.state.reported.temp <= -18) {
            color = '#8ee4bf'
          } else {
            color = '#ff8080'
          }
        }

        sensorData.push(

          {
            pointTime: new Date(sensorsDataResult.Items[x].createdAt),
            pointValue: parseFloat(sensorsDataResult.Items[x].sensorData.state.reported.temp).toFixed(2),
            lineColor: color

          }

        )

      }
      sensors.push({

        sensorId: sensorsData[skey].objectId,
        sensorDataId: sensorId,
        sensorData: sensorData.reverse()

      })

    }

    const assetParams = {
      TableName: process.env.objectsTableName,
      KeyConditionExpression: "userId = :userId and objectId = :objectId",

      ExpressionAttributeValues: {
        ":userId": user_id,
        ":objectId": devicesData[0].objectParent
      }
    };

    const assetResult = await dynamoDbLib.call("query", assetParams);
    const asset = assetResult.Items;


    device = {

      deviceId: devicesData[0].objectId,
      deviceName: devicesData[0].objectName,
      deviceType: devicesData[0].objectKind,
      sensors: sensors,
      asset: asset

    }

    callback(null, success(device));

  } catch (e) {
    callback(null, failure({
      status: e
    }));
  }
}