import * as dynamoDbLib from "../../../libs/dynamodb-lib";
import * as bugsnagClient from "../../../libs/bugsnag";
import {
  success,
  failure
} from "../../../libs/response-lib";

import {
  is_level_permitted
} from "../../../libs/permissions";

export async function access(event, context, callback) {

  try {

    const access_scope = ['owner', 'manager', 'employee']

    var user_id = event.requestContext.identity.cognitoAuthenticationProvider.split('CognitoSignIn:')[1]

    const permissions = await is_level_permitted(user_id, access_scope);
    if (permissions.level_allowed) {

      const params = {
        TableName: process.env.userPermissionsTableName,
        FilterExpression: "companyId = :companyId and (roleType = :roleType1 or roleType = :roleType2) and isActive = :isActive",
        ExpressionAttributeValues: {
          ":companyId": permissions.user_company_id,
          ":roleType1": 'access_role',
          ":roleType2": 'shop_access_role',
          ":isActive": true
        }
      };

      let result = await dynamoDbLib.call("scan", params);




      for (var x in result.Items) {

        if (result.Items[x].roleType == 'shop_access_role') {

          let shop_id = result.Items[x].shopId

          let shop = await dynamoDbLib.call("scan", {
            TableName: process.env.objectsTableName,
            FilterExpression: "objectType = :objectType and objectId = :objectId",
            ExpressionAttributeValues: {
              ":objectType": 'shop',
              ":objectId": shop_id
            }
          });
          result.Items[x].shop_name = shop.Items[0].objectName
        }



      }


      callback(null, success(result.Items))

    } else {

      callback(null, failure({
        status: 'you do not have access to this api call'
      }));

    }

  } catch (e) {
    bugsnagClient.notify(user_id, e)
    callback(null, failure({
      status: e
    }));
  }

}