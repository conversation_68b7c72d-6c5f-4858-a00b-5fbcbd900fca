service: kompis-permissions

plugins:
  - serverless-webpack

custom:
  webpackIncludeModules: true
  stage: ${opt:stage, self:provider.stage}
  stageSufix:
    prod: ""
    dev: -dev
  congnito_pool_id:
    dev: eu-central-1_o36r1nMGj
    prod: eu-central-1_E1nLfop0C

provider:
  versionFunctions: false
  name: aws
  endpointType: REGIONAL
  runtime: nodejs16.x
  stage: dev
  region: eu-central-1
  environment:
    COGNITO_POOL_ID: ${self:custom.congnito_pool_id.${self:provider.stage}}
    generateCompaniesTasksSNSTopic: arn:aws:sns:eu-central-1:589634798762:generate_companies_tasks${self:custom.stageSufix.${self:custom.stage}}
    cartTableName: cart${self:custom.stageSufix.${self:custom.stage}}
    exceptionsTableName: exceptions${self:custom.stageSufix.${self:custom.stage}}
    objectsTableName: objects${self:custom.stageSufix.${self:custom.stage}}
    sensorsDataLogsTableName: sensorsDataLogs${self:custom.stageSufix.${self:custom.stage}}
    tasksTableName: tasks${self:custom.stageSufix.${self:custom.stage}}
    tasksLogTableName: tasksLog${self:custom.stageSufix.${self:custom.stage}}
    tasksSchedulesTableName: tasksSchedules${self:custom.stageSufix.${self:custom.stage}}
    userPermissionsTableName: userPermissions${self:custom.stageSufix.${self:custom.stage}}
    logsTableName: logs${self:custom.stageSufix.${self:custom.stage}}

  iamRoleStatements:
    - Effect: "Allow"
      Action:
        - "SNS:Publish"
      Resource: "arn:aws:sns:eu-central-1:589634798762:generate_companies_tasks${self:custom.stageSufix.${self:custom.stage}}"

    - Effect: "Allow"
      Action:
        - "cognito-idp:*"
      Resource: "arn:aws:cognito-idp:eu-central-1:*:userpool/*"

    - Effect: Allow
      Action:
        - dynamodb:Query
        - dynamodb:Scan
        - dynamodb:GetItem
        - dynamodb:PutItem
        - dynamodb:UpdateItem
        - dynamodb:DeleteItem
      Resource: "arn:aws:dynamodb:eu-central-1:*:*"

functions:
  createPermission:
    handler: functions/create.permission
    events:
      - http:
          path: /
          method: post
          cors: true
          authorizer: aws_iam

  listPermissions:
    handler: functions/list.permissions
    events:
      - http:
          path: /
          method: get
          cors: true
          authorizer: aws_iam

  listAccessPermissions:
    handler: functions/access.access
    events:
      - http:
          path: /access/
          method: get
          cors: true
          authorizer: aws_iam

  listUserPermissions:
    handler: functions/list_user.permissions
    events:
      - http:
          path: /{id}
          method: get
          cors: true
          authorizer: aws_iam

  deletePermissions:
    handler: functions/delete.permission
    events:
      - http:
          path: /{id}
          method: delete
          cors: true
          authorizer: aws_iam

  deletePermissionsDeliveryPoint:
    handler: functions/delete_delivery_point.permission
    events:
      - http:
          path: /{id}/{shop}
          method: delete
          cors: true
          authorizer: aws_iam
