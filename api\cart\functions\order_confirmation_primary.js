import {
  success,
  failure
} from "../../../libs/response-lib";

import * as bugsnagClient from "../../../libs/bugsnag";
import * as dynamoDbLib from "../../../libs/dynamodb-lib";
import nodemailer from "nodemailer"

const AWS = require('aws-sdk')

export async function send(event, context, callback) {


  try {

    let cart_id = event.Records[0].Sns.Message
    //let cart_id = '8a6182b0-ef4c-11e9-ad29-5f05a4a2affe'
    let cognito_client = new AWS.CognitoIdentityServiceProvider()
    let pool_id = process.env.COGNITO_POOL_ID

    const order = await dynamoDbLib.call("scan", {
      TableName: process.env.cartTableName,
      FilterExpression: "cartId = :cartId",
      ExpressionAttributeValues: {
        ":cartId": cart_id
      }
    });

    let delivery_date = new Date(order.Items[0].delivery_date + (3 * 60 * 60 * 1000))
    let schedule_type = order.Items[0].schedule_type


    let user_company = await dynamoDbLib.call("scan", {
      TableName: process.env.objectsTableName,
      FilterExpression: "objectType = :objectType and objectId = :objectId",
      ExpressionAttributeValues: {
        ":objectType": 'company',
        ":objectId": order.Items[0].companyId,
      }
    });

    let company_name = user_company.Items[0].objectName

    let shop_id = order.Items[0].shopId
    const shopInfo = await dynamoDbLib.call("scan", {
      TableName: process.env.objectsTableName,
      FilterExpression: "objectType = :objectType and objectId = :objectId",
      ExpressionAttributeValues: {
        ":objectType": 'shop',
        ":objectId": shop_id
      }
    });

    let sendNotificationsTo = (shopInfo.Items[0].sendNotificationsTo) ? shopInfo.Items[0].sendNotificationsTo : []
    let shop_name = shopInfo.Items[0].objectName
    let orderDeadline = (shopInfo.Items[0].orderDeadline) ? parseInt(shopInfo.Items[0].orderDeadline) : 0
    let orderDeadlineTime = (shopInfo.Items[0].orderDeadlineTime) ? parseInt(shopInfo.Items[0].orderDeadlineTime) : 24

    let order_type = ''

    if (schedule_type == 'once') {

      delivery_date = delivery_date.getFullYear() + '-' + pad((delivery_date.getMonth() + 1), 2) + '-' + pad(delivery_date.getDate(), 2)
      order_type = 'Ordre'

    }

    let delivery_date_start = ''
    let delivery_date_end = ''

    if (schedule_type == 'volume') {

      delivery_date_start = new Date(order.Items[0].delivery_date_start)
      delivery_date_end = new Date(order.Items[0].delivery_date_end)

      delivery_date_start = delivery_date_start.getFullYear() + '-' + pad((delivery_date_start.getMonth() + 1), 2) + '-' + pad(delivery_date_start.getDate(), 2)
      delivery_date_end = delivery_date_end.getFullYear() + '-' + pad((delivery_date_end.getMonth() + 1), 2) + '-' + pad(delivery_date_end.getDate(), 2)

      order_type = 'Volum ordre'

    }

    let recurring_delivery_dates = []
    let delivery_days = ''
    if (schedule_type == 'weekly') {

      order_type = 'Repeterende ordre'

      let schedule_dates = (order.Items[0].schedule_dates) ? order.Items[0].schedule_dates : []

      let days_of_the_month = []

      days_of_the_month[1] = 'Mandag'
      days_of_the_month[2] = 'Tirsdag'
      days_of_the_month[3] = 'Onsdag'
      days_of_the_month[4] = 'Torsdag'
      days_of_the_month[5] = 'Fredag'
      days_of_the_month[6] = 'Lørdag'
      days_of_the_month[7] = 'Søndag'


      for (var y in schedule_dates) {

        delivery_days += days_of_the_month[schedule_dates[y]]
        delivery_days += ', '

        let schedule_day = parseInt(schedule_dates[y])

        let schedule_date = new Date();
        schedule_date.setDate(schedule_date.getDate() + (schedule_day + 7 - schedule_date.getDay()) % 7);

        let today = new Date();
        let current_hour = today.getHours()

        const diffDays = Math.round(((schedule_date - today) / (24 * 60 * 60 * 1000)));

        if ((diffDays > orderDeadline) || ((diffDays == orderDeadline) && (current_hour < (orderDeadlineTime - (order.Items[0].tzo / 60) - 1)))) {
          recurring_delivery_dates.push(schedule_date.getFullYear() + '-' + pad((schedule_date.getMonth() + 1), 2) + '-' + pad(schedule_date.getDate(), 2))
        }
      }

      delivery_date = recurring_delivery_dates[0]
      delivery_days = delivery_days.substring(0, delivery_days.length - 2);
    }

    console.log(recurring_delivery_dates);

    let emails = []

    let params = {
      UserPoolId: pool_id,
      Filter: "name = \"" + shopInfo.Items[0].objectCompanyId + "\"",
      AttributesToGet: null,
      Limit: 0
    }

    let users_data = await cognito_client.listUsers(params).promise()

    for (var y in users_data.Users) {

      let email = ''
      let user_type = ''
      let username = users_data.Users[y].Username


      for (var z in users_data.Users[y].Attributes) {

        if (users_data.Users[y].Attributes[z].Name == 'email') {
          email = users_data.Users[y].Attributes[z].Value
        }

        if (users_data.Users[y].Attributes[z].Name == 'custom:user_type') {
          user_type = users_data.Users[y].Attributes[z].Value
        }

      }

      if (sendNotificationsTo.includes(username)) {
        emails.push(email)
      }

    }


    let transporter = nodemailer.createTransport({
      host: 'email-smtp.eu-west-1.amazonaws.com',
      port: 465,
      secure: true,
      auth: {
        user: 'AKIAJLILB3DMXPRVD4AA',
        pass: 'Amg0o1jLDdfBLT++mU1G0xw99F+cCjVKFg4DYMlQOppz'
      }
    });

    let from_email = '<EMAIL>'
    let mailOptions = {}

    for (var k in emails) {
      let html = ''

      if (schedule_type == 'once') {

        html = 'Hei<br /><br /> Gratulerer, du har en nye ordre i ' + shop_name + ' fra ' + company_name + ' for leveranse den ' + delivery_date + '<br /><br />For flere detaljer, <a href="' + process.env.KOMPIS_APP_URL + '">klikk her</a>.<br /><br />Mhv<br /><br />Kompis'

      }

      if (schedule_type == 'weekly') {

        html = 'Hei<br /><br />Gratulerer, du har en ny repeterende ordre i ' + shop_name + ' fra ' + company_name + '.<br /><br />Nye leveranser begynner den ' + delivery_date + ' hver ' + delivery_days + '.<br /><br />For flere detaljer, <a href="' + process.env.KOMPIS_APP_URL + '">klikk her</a>.<br /><br />Mhv<br /><br />Kompis'

      }

      if (schedule_type == 'volume') {

        html = 'Hei<br /><br />Gratulerer, du har en ny volum ordre i ' + shop_name + ' fra ' + company_name + '.<br /><br />Nye leveranser begynner den ' + delivery_date_start + ' frem til og med ' + delivery_date_end + '.<br /><br />For flere detaljer, <a href="' + process.env.KOMPIS_APP_URL + '">klikk her</a>.<br /><br />Mhv<br /><br />Kompis'

      }

      mailOptions = {
        from: from_email,
        to: emails[k],
        subject: 'Din oppdatering om ny ordre',
        html: html
      };


      await transporter.sendMail(mailOptions).then(function(info) {
        callback(null, success(true));
      }).catch(function(err) {
        bugsnagClient.notify(user_id, err)
        callback(null, failure({
          status: err
        }));
      });

    }
    callback(null, 'done');


  } catch (e) {
    //  bugsnagClient.notify(user_id, e)
    console.log(e);
    callback(null, failure({
      error: e
    }))
  }

}

function pad(num, size) {
  var s = num + "";
  while (s.length < size) s = "0" + s;
  return s;
}