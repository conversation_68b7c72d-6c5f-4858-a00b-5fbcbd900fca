import { success, failure } from "../../../libs/response-lib";

import { is_level_permitted } from "../../../libs/permissions";
import * as bugsnagClient from "../../../libs/bugsnag";
import * as dynamoDbLib from "../../../libs/dynamodb-lib";

import PowerOffice from "../../../libs/poweroffice";

const AWS = require("aws-sdk");
const cognito_client = new AWS.CognitoIdentityServiceProvider();

export async function init(event, context, callback) {
  try {
    let this_permission_id = event.Records[0].Sns.Message;
    //let this_permission_id = "fd54cbe0-0191-11ea-beeb-d31ead3057ee";

    let shops_permissions = await dynamoDbLib.call("scan", {
      TableName: process.env.userPermissionsTableName,
      FilterExpression: "permissionId = :permissionId",
      ExpressionAttributeValues: {
        ":permissionId": this_permission_id,
      },
    });

    const shop_id = shops_permissions.Items[0].shopId;

    const shopsSarams = {
      TableName: process.env.objectsTableName,
      FilterExpression: "objectType = :objectType and objectId = :objectId",
      ExpressionAttributeValues: {
        ":objectType": "shop",
        ":objectId": shop_id,
      },
    };

    const shops = await dynamoDbLib.call("scan", shopsSarams);

    if (shops.Items[0].objectPowerOfficeIntegration) {
      let company_id = shops.Items[0].objectCompanyId;
      let migration_mode = shops.Items[0].objectPowerOfficeMigrationMode;

      const integrationParams = {
        TableName: process.env.integrationsTableName,
        FilterExpression: "integrationId = :integrationId",
        ExpressionAttributeValues: {
          ":integrationId": shops.Items[0].objectPowerOfficeIntegration,
        },
      };

      const integrations = await dynamoDbLib.call("scan", integrationParams);
      let poweroffice_client_key = integrations.Items[0].integrationPOAppKey;
      let poweroffice_secrect_key =
        integrations.Items[0].integrationPOClientKey;
      let poweroffice_0_mva_account = integrations.Items[0].integrationPO0MVA;
      let poweroffice_15_mva_account = integrations.Items[0].integrationPO15MVA;
      let poweroffice_25_mva_account = integrations.Items[0].integrationPO25MVA;
      let migration_start_date = integrations.Items[0].createdAt;

      let poweroffice = await new PowerOffice(
        process.env.POWEROFFICE_APP_KEY,
        poweroffice_secrect_key
      );

      let customers = [];

      let shopDeliveryPoints = await dynamoDbLib.call("scan", {
        TableName: process.env.objectsTableName,
        FilterExpression:
          "objectType = :objectType and objectShopId = :objectShopId",
        ExpressionAttributeValues: {
          ":objectType": "delivery_point",
          ":objectShopId": shop_id,
        },
      });

      for (var x in shops_permissions.Items) {
        let customer = await dynamoDbLib.call("scan", {
          TableName: process.env.objectsTableName,
          FilterExpression: "objectType = :objectType and objectId = :objectId",
          ExpressionAttributeValues: {
            ":objectType": "company",
            ":objectId": shops_permissions.Items[x].companyId,
          },
        });

        let pool_id = process.env.COGNITO_POOL_ID;
        let users = [];
        let permission_id = shops_permissions.Items[x].permissionId;
        let poweroffice_customer_id = shops_permissions.Items[x]
          .powerofficeCustomerId
          ? shops_permissions.Items[x].powerofficeCustomerId
          : null;

        let user_pool = await cognito_client
          .describeUserPool({
            UserPoolId: pool_id,
          })
          .promise();

        let number_of_users = user_pool.UserPool.EstimatedNumberOfUsers;

        let loops = Math.ceil(number_of_users / 60);

        let token = "";

        for (var i = 1; i <= loops; i++) {
          let ownersParams = {};

          if (token != "") {
            ownersParams = {
              UserPoolId: pool_id,
              AttributesToGet: null,
              Filter: 'name = "' + shops_permissions.Items[x].companyId + '"',
              Limit: 0,
              PaginationToken: token,
            };
          } else {
            ownersParams = {
              UserPoolId: pool_id,
              AttributesToGet: null,
              Filter: 'name = "' + shops_permissions.Items[x].companyId + '"',
              Limit: 0,
            };
          }

          let users_data = await cognito_client
            .listUsers(ownersParams)
            .promise();

          token = users_data.PaginationToken;

          for (var z in users_data.Users) {
            let user_type = "";
            let user_company_id = "";
            let email_verified = false;
            let owner_set = false;

            for (var y in users_data.Users[z].Attributes) {
              if (
                users_data.Users[z].Attributes[y].Name == "custom:user_type"
              ) {
                user_type = users_data.Users[z].Attributes[y].Value;
              }

              if (
                users_data.Users[z].Attributes[y].Name == "custom:company_id"
              ) {
                user_company_id = users_data.Users[z].Attributes[y].Value;
              }

              if (users_data.Users[z].Attributes[y].Name == "email_verified") {
                email_verified = users_data.Users[z].Attributes[y].Value;
              }
            }

            let contact_set = shops_permissions.Items[x].contactId
              ? true
              : false;

            if (
              !owner_set &&
              user_type == "owner" &&
              user_company_id == shops_permissions.Items[x].companyId
            ) {
              owner_set = true;
              let first_name = "";
              let last_name = "";
              let email = "";
              let phone = "";
              let address = customer.Items[0].objectAddress;

              let post_code = customer.Items[0].objectPostCode;
              let company_name = customer.Items[0].objectName;
              let organization_id = customer.Items[0].objectOrganizationId;
              let invoice_delivery_type = shops_permissions.Items[x]
                .invoice_delivery_type
                ? shops_permissions.Items[x].invoice_delivery_type
                : 1;
              let city = customer.Items[0].objectCity;
              let customer_company_id = "";

              if (invoice_delivery_type == "mail") {
                invoice_delivery_type = 1;
              }

              if (invoice_delivery_type == "ehf") {
                invoice_delivery_type = 3;
              }
              if (invoice_delivery_type == "avtale_giro") {
                invoice_delivery_type = 4;
              }

              for (var y in users_data.Users[z].Attributes) {
                if (
                  users_data.Users[z].Attributes[y].Name == "custom:first_name"
                ) {
                  first_name = users_data.Users[z].Attributes[y].Value;
                }
                if (
                  users_data.Users[z].Attributes[y].Name == "custom:last_name"
                ) {
                  last_name = users_data.Users[z].Attributes[y].Value;
                }
                if (
                  users_data.Users[z].Attributes[y].Name == "custom:user_phone"
                ) {
                  phone = users_data.Users[z].Attributes[y].Value;
                }
                if (
                  users_data.Users[z].Attributes[y].Name == "custom:post_code"
                ) {
                  //  post_code = users_data.Users[z].Attributes[y].Value
                }
                if (
                  users_data.Users[z].Attributes[y].Name == "custom:company_id"
                ) {
                  customer_company_id = users_data.Users[z].Attributes[y].Value;
                }
                if (
                  users_data.Users[z].Attributes[y].Name ==
                  "custom:company_name"
                ) {
                  //company_name = users_data.Users[z].Attributes[y].Value
                }
                if (
                  users_data.Users[z].Attributes[y].Name ==
                  "custom:organization_id"
                ) {
                  //organization_id = users_data.Users[z].Attributes[y].Value
                }
                if (users_data.Users[z].Attributes[y].Name == "custom:city") {
                  //city = users_data.Users[z].Attributes[y].Value
                }
                if (users_data.Users[z].Attributes[y].Name == "email") {
                  email = users_data.Users[z].Attributes[y].Value;
                }

                if (users_data.Users[z].Attributes[y].Name == "address") {
                  //address = users_data.Users[z].Attributes[y].Value
                }
              }

              // get customer delivery points

              let customer_address = [];

              const deliveryPointsPermissions = await dynamoDbLib.call("scan", {
                TableName: process.env.userPermissionsTableName,
                FilterExpression: "userId = :userId",
                ExpressionAttributeValues: {
                  ":userId": customer_company_id,
                },
              });

              for (var w in deliveryPointsPermissions.Items) {
                for (var z in shopDeliveryPoints.Items) {
                  if (
                    deliveryPointsPermissions.Items[w].locationId ==
                    shopDeliveryPoints.Items[z].objectId
                  ) {
                    if (
                      deliveryPointsPermissions.Items[w].powerofficeAddressId
                    ) {
                      customer_address.push({
                        Id: deliveryPointsPermissions.Items[w]
                          .powerofficeAddressId,
                        Address1: shopDeliveryPoints.Items[z].objectAddress,
                        CountryCode: "NO",
                        IsPrimary: false,
                      });
                    } else {
                      customer_address.push({
                        Address1: shopDeliveryPoints.Items[z].objectAddress,
                        CountryCode: "NO",
                        IsPrimary: false,
                      });
                    }
                  }
                }
              }

              for (var z in users_data.Users) {
                if (
                  users_data.Users[z].Username ==
                  shops_permissions.Items[x].contactId
                ) {
                  for (var y in users_data.Users[z].Attributes) {
                    if (
                      users_data.Users[z].Attributes[y].Name ==
                      "custom:first_name"
                    ) {
                      first_name = users_data.Users[z].Attributes[y].Value;
                    }
                    if (
                      users_data.Users[z].Attributes[y].Name ==
                      "custom:last_name"
                    ) {
                      last_name = users_data.Users[z].Attributes[y].Value;
                    }
                    if (
                      users_data.Users[z].Attributes[y].Name ==
                      "custom:user_phone"
                    ) {
                      phone = users_data.Users[z].Attributes[y].Value;
                    }

                    if (users_data.Users[z].Attributes[y].Name == "email") {
                      email = users_data.Users[z].Attributes[y].Value;
                    }
                  }
                }
              }

              customers.push({
                first_name,
                last_name,
                phone,
                email,
                address,
                post_code,
                company_name,
                organization_id,
                city,
                customer_company_id,
                invoice_delivery_type,
                customer_address,
                poweroffice_customer_id,
                permission_id,
              });
            }
          }
        }
      }

      customers = removeDuplicates(customers, "customer_company_id");
      let poweroffice_customer_id = null;

      let poweroffice_customers = await poweroffice.getCustomers(
        "?$filter=IsArchived%20eq%20false"
      );

      for (var x in customers) {
        if (
          customers[x].poweroffice_customer_id != null &&
          customers[x].poweroffice_customer_id != 0
        ) {
          console.log("if");
          let contact = await poweroffice.getCustomerContactPerson(
            customers[x].poweroffice_customer_id
          );

          if (contact.data.length == 0) {
            await poweroffice.createCustomerContactPerson(
              customers[x].poweroffice_customer_id,
              {
                FirstName: customers[x].first_name,
                LastName: customers[x].last_name,
                EmailAddress: customers[x].email,
              }
            );
          } else {
            await poweroffice.createCustomerContactPerson(
              customers[x].poweroffice_customer_id,
              {
                id: contact.data[0].id,
                FirstName: customers[x].first_name,
                LastName: customers[x].last_name,
                EmailAddress: customers[x].email,
              }
            );
          }

          let ccc = await poweroffice.updateCustomer({
            id: customers[x].poweroffice_customer_id,
            emailAddress: customers[x].email,
            firstName: customers[x].first_name,
            lastName: customers[x].last_name,
            invoiceDeliveryType: customers[x].invoice_delivery_type,
            invoiceEmailAddress: customers[x].email,
            isPerson: false,
            isArchived: false,
            legalName: customers[x].company_name,
            name: customers[x].company_name,
            phoneNumber: customers[x].phone,
            vatNumber: customers[x].organization_id,
            streetAddresses: customers[x].customer_address,
            mailAddress: {
              address1: customers[x].address,
              city: customers[x].city,
              zipCode: customers[x].post_code,
              countryCode: "NO",
            },
          });
          poweroffice_customer_id = customers[x].poweroffice_customer_id;
        } else {
          console.log("else");
          let cus = await poweroffice.createCustomer({
            EmailAddress: customers[x].email,
            FirstName: customers[x].first_name,
            LastName: customers[x].last_name,
            InvoiceDeliveryType: customers[x].invoice_delivery_type,
            InvoiceEmailAddress: customers[x].email,
            IsPerson: false,
            IsArchived: false,
            LegalName: customers[x].company_name,
            Name: customers[x].company_name,
            PhoneNumber: customers[x].phone,
            VatNumber: customers[x].organization_id,
            streetAddresses: customers[x].customer_address,
            MailAddress: {
              Address1: customers[x].address,
              City: customers[x].city,
              ZipCode: customers[x].post_code,
              CountryCode: "NO",
            },
          });

          if (cus.data.data) {
            await poweroffice.createCustomerContactPerson(cus.data.data.id, {
              FirstName: customers[x].first_name,
              LastName: customers[x].last_name,
              EmailAddress: customers[x].email,
            });

            await dynamoDbLib.call("update", {
              TableName: process.env.userPermissionsTableName,

              Key: {
                permissionId: customers[x].permission_id,
              },

              UpdateExpression:
                "SET powerofficeCustomerId = :powerofficeCustomerId",
              ExpressionAttributeValues: {
                ":powerofficeCustomerId": cus.data.data.id,
              },
              ReturnValues: "ALL_NEW",
            });
          }
          console.log(cus.data);
          poweroffice_customer_id = cus.data.data.id;
        }
      }

      poweroffice_customers = await poweroffice.getCustomer(
        poweroffice_customer_id
      );

      let streetAddresses = poweroffice_customers.data.streetAddresses;

      for (var k in customers) {
        for (var s in streetAddresses) {
          for (var z in shopDeliveryPoints.Items) {
            console.log("vat" + poweroffice_customers.data.vatNumber);

            if (
              poweroffice_customers.data.vatNumber ==
              customers[k].organization_id
            ) {
              let deliveryPointsPermission = await dynamoDbLib.call("scan", {
                TableName: process.env.userPermissionsTableName,
                FilterExpression:
                  "userId = :userId and locationId = :locationId",
                ExpressionAttributeValues: {
                  ":userId": customers[k].customer_company_id,
                  ":locationId": shopDeliveryPoints.Items[z].objectId,
                },
              });

              if (
                streetAddresses[s].address1 ==
                  shopDeliveryPoints.Items[z].objectAddress &&
                deliveryPointsPermission.Items.length > 0
              ) {
                console.log(deliveryPointsPermission.Items[0].permissionId);

                await dynamoDbLib.call("update", {
                  TableName: process.env.userPermissionsTableName,

                  Key: {
                    permissionId:
                      deliveryPointsPermission.Items[0].permissionId,
                  },

                  UpdateExpression:
                    "SET powerofficeAddressId = :powerofficeAddressId",
                  ExpressionAttributeValues: {
                    ":powerofficeAddressId": streetAddresses[s].id,
                  },
                  ReturnValues: "ALL_NEW",
                });
              }
            }
          }
        }
      }
    }

    callback(null, success(true));
  } catch (e) {
    //  bugsnagClient.notify(user_id, e)
    console.log(e);
    callback(
      null,
      failure({
        error: e,
      })
    );
  }
}

function removeDuplicates(myArr, prop) {
  return myArr.filter((obj, pos, arr) => {
    return arr.map((mapObj) => mapObj[prop]).indexOf(obj[prop]) === pos;
  });
}
