import * as dynamoDbLib from "../../../libs/dynamodb-lib";
import * as bugsnagClient from "../../../libs/bugsnag";
import {
  success,
  failure
} from "../../../libs/response-lib";

import {
  is_level_permitted
} from "../../../libs/permissions";

var arraySort = require('array-sort')

export async function list(event, context, callback) {


  try {
    const access_scope = ['owner', 'manager', 'employee']

    var user_id = event.requestContext.identity.cognitoAuthenticationProvider.split('CognitoSignIn:')[1]


    const permissions = await is_level_permitted(user_id, access_scope);
    if (permissions.level_allowed) {


      // get the timestamp for the last second on this week.
      let week_day = new Date();
      let end_of_the_week_day_index = 7 // 7 means Sunday
      let start_of_the_week_day_index = 1 // 1 means Monday


      week_day.setDate(week_day.getDate() + (start_of_the_week_day_index + 7 - week_day.getDay()) % 7);
      let start_of_this_week_timestamp = new Date(week_day.getFullYear() + '-' + (week_day.getMonth() + 1) + '-' + week_day.getDate() + ' 00:00:01').getTime()

      week_day.setDate(week_day.getDate() + (end_of_the_week_day_index + 7 - week_day.getDay()) % 7);
      let end_of_this_week_timestamp = new Date(week_day.getFullYear() + '-' + (week_day.getMonth() + 1) + '-' + week_day.getDate() + ' 23:59:59').getTime()

      // get the timestamp for the last second of today.
      let start_of_today_timestamp = (new Date()).setHours(0, 0, 1)
      let end_of_today_timestamp = (new Date()).setHours(23, 59, 59)

      let date = new Date();
      let start_of_this_month_timestamp = new Date(date.getFullYear(), date.getMonth(), 1).getTime();
      let end_of_this_month_timestamp = new Date(date.getFullYear(), date.getMonth() + 1, 0).getTime();


      const params = {
        TableName: process.env.objectsTableName,
        FilterExpression: "objectType = :objectType and objectCompanyId = :objectCompanyId",
        ExpressionAttributeValues: {
          ":objectType": 'shop',
          ":objectCompanyId": permissions.user_company_id
        }
      };

      const shop = await dynamoDbLib.call("scan", params);
      let shop_id = shop.Items[0].objectId

      const cartParsms = {
        TableName: process.env.cartTableName,
        FilterExpression: "createdAt BETWEEN :startOfMonth AND :endOfMonth and shopId = :shopId",
        ExpressionAttributeValues: {
          ":startOfMonth": start_of_this_month_timestamp,
          ":endOfMonth": end_of_this_month_timestamp,
          ":shopId": shop_id
        }
      };

      const orders = await dynamoDbLib.call("scan", cartParsms);

      let today_stats = {
        products: 0,
        orders: 0,
        revenue: 0
      }
      let week_stats = {
        products: 0,
        orders: 0,
        revenue: 0
      }
      let month_stats = {
        products: 0,
        orders: 0,
        revenue: 0
      }

      for (var x in orders.Items) {

        let order_time = orders.Items[x].createdAt

        if (order_time <= end_of_today_timestamp && order_time >= start_of_today_timestamp) {


          for (var y in orders.Items[x].items) {
            today_stats.products += orders.Items[x].items[y].quantity
          }

          today_stats.orders += 1
          today_stats.revenue += orders.Items[x].total
          today_stats.revenue += orders.Items[x].freight

          today_stats.revenue = Math.round(parseFloat(today_stats.revenue) * 100) / 100

        }

        if (order_time <= end_of_this_week_timestamp && order_time >= start_of_this_week_timestamp) {

          for (var y in orders.Items[x].items) {
            week_stats.products += orders.Items[x].items[y].quantity
          }

          week_stats.orders += 1
          week_stats.revenue += orders.Items[x].total
          week_stats.revenue += orders.Items[x].freight

          week_stats.revenue = Math.round(parseFloat(week_stats.revenue) * 100) / 100

        }

        if (order_time <= end_of_this_month_timestamp && order_time >= start_of_this_month_timestamp) {

          for (var y in orders.Items[x].items) {
            month_stats.products += orders.Items[x].items[y].quantity
          }

          month_stats.orders += 1
          month_stats.revenue += orders.Items[x].total
          month_stats.revenue += orders.Items[x].freight

          month_stats.revenue = Math.round(parseFloat(month_stats.revenue) * 100) / 100

        }


      }

      callback(null, success({
        today_stats,
        week_stats,
        month_stats
      }));

    } else {

      callback(null, failure({
        status: 'you do not have access to this api call'
      }));

    }

  } catch (e) {
    bugsnagClient.notify(user_id, e)
    callback(null, failure({
      status: e
    }));
  }
}