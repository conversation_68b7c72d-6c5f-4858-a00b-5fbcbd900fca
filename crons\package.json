{"name": "serverless", "version": "1.0.0", "description": "", "main": "handler.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"aws-sdk": "^2.211.0", "babel-core": "^6.26.0", "babel-loader": "^7.1.4", "babel-plugin-transform-runtime": "^6.23.0", "babel-preset-env": "^1.6.1", "babel-preset-stage-3": "^6.24.1", "serverless-webpack": "^5.11.0", "webpack": "^5.75.0", "webpack-node-externals": "^1.6.0"}, "dependencies": {"@bugsnag/js": "^6.5.2", "array-sort": "^1.0.0", "axios": "^1.2.6", "axios-oauth-client": "^1.5.0", "babel-runtime": "^6.26.0", "nodemailer": "^6.9.1", "stripe": "^7.63.1", "uuid": "^3.4.0"}}