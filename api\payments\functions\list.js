import * as dynamoDbLib from "../../../libs/dynamodb-lib";
import * as bugsnagClient from "../../../libs/bugsnag";
import {
  success,
  failure
} from "../../../libs/response-lib";

import {
  is_level_permitted
} from "../../../libs/permissions";
const AWS = require('aws-sdk')
const SNS = new AWS.SNS();
export async function payment_methods(event, context, callback) {


  try {
    const access_scope = ['owner', 'manager', 'employee']

    var user_id = event.requestContext.identity.cognitoAuthenticationProvider.split('CognitoSignIn:')[1]


    const permissions = await is_level_permitted(user_id, access_scope);

    if (permissions.level_allowed) {

      let params = {}
      let shop_id = event.pathParameters.id

      let stripe_integration_id = process.env.STRIPE_INTEGRATION_ID

      let payment_methods = []
      let shopInfo = []

      if (shop_id == 'kompis_app') {

        await SNS.publish({
          Message: permissions.user_company_id,
          TopicArn: process.env.generateBillsSNSTopic
        }, function(err, data) {
          //console.log(data);
          if (err) {
            //callback(null, err);
          }
          // sns sent. good job
        });

      }

      if (shop_id != 'kompis_app') {

        shopInfo = await dynamoDbLib.call("scan", {
          TableName: process.env.objectsTableName,
          FilterExpression: "objectType = :objectType and objectId = :objectId",
          ExpressionAttributeValues: {
            ":objectType": 'shop',
            ":objectId": shop_id
          }
        });

        stripe_integration_id = shopInfo.Items[0].objectStripeIntegration
      }



      params = {
        TableName: process.env.objectsTableName,
        FilterExpression: "objectType = :objectType and objectCompanyId = :objectCompanyId and objectIntegrationId = :objectIntegrationId",
        ExpressionAttributeValues: {
          ":objectType": 'payment_method',
          ":objectCompanyId": permissions.user_company_id,
          ":objectIntegrationId": stripe_integration_id
        }
      };

      const result = await dynamoDbLib.call("scan", params);


      if (shop_id != 'kompis_app') {

        let shops_permissions = await dynamoDbLib.call("scan", {
          TableName: process.env.userPermissionsTableName,
          FilterExpression: "grantedBy = :grantedBy and companyId = :companyId and roleType = :roleType and isActive = :isActive and shopId = :shopId",
          ExpressionAttributeValues: {
            ":grantedBy": shopInfo.Items[0].objectCompanyId,
            ":companyId": permissions.user_company_id,
            ":roleType": 'shop_access_role',
            ":isActive": true,
            ":shopId": shop_id
          }
        })


        let allowed_payment_options = (shops_permissions.Items[0].allowed_payment_options) ? shops_permissions.Items[0].allowed_payment_options : []

        if (allowed_payment_options.length > 0) {

          if (allowed_payment_options.includes('manual')) {
            payment_methods.push({
              objectId: 'manual',
              objectName: 'manual',
              objectIsDefault: true,
              objectPaymentMethodHolder: 'none'
            })
          }
        }

      }


      for (var x in result.Items) {
        payment_methods.push({
          objectId: result.Items[x].objectId,
          objectName: result.Items[x].objectName,
          objectIsDefault: result.Items[x].objectIsDefault,
          objectPaymentMethodHolder: result.Items[x].objectPaymentMethodHolder,
        })
      }


      callback(null, success(payment_methods));

    } else {

      callback(null, failure({
        status: 'you do not have access to this api call'
      }));
    }
  } catch (e) {
    bugsnagClient.notify(user_id, e)
    callback(null, failure({
      status: e
    }));
  }
}