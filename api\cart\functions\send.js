export async function send(event, context, callback) {

  const nodemailer = require('nodemailer');

  let transporter = nodemailer.createTransport({
    host: 'email-smtp.eu-west-1.amazonaws.com',
    port: 465,
    secure: true,
    auth: {
      user: 'AKIAJLILB3DMXPRVD4AA',
      pass: 'Amg0o1jLDdfBLT++mU1G0xw99F+cCjVKFg4DYMlQOppz'
    }
  });


  let mailOptions = {
    from: '<EMAIL>',
    to: '<EMAIL>',
    subject: 'Hello ✔',
    text: 'Hello world?',
    html: '<b>Hello world?</b>'
  };

  // send mail with defined transport object
  transporter.sendMail(mailOptions, (error, info) => {
    if (error) {
      return console.log(error);
    }
    // all is good

  });



}