import * as dynamoDbLib from "../../../libs/dynamodb-lib";
import * as bugsnagClient from "../../../libs/bugsnag";
import {
  success,
  failure
} from "../../../libs/response-lib";

import {
  is_level_permitted
} from "../../../libs/permissions";

const AWS = require('aws-sdk')
const cognito_client = new AWS.CognitoIdentityServiceProvider()

export async function cart(event, context, callback) {

  try {

    const access_scope = ['owner', 'manager', 'employee']

    var user_id = event.requestContext.identity.cognitoAuthenticationProvider.split('CognitoSignIn:')[1]
    let pool_id = process.env.COGNITO_POOL_ID

    const permissions = await is_level_permitted(user_id, access_scope);
    if (permissions.level_allowed) {

      let cart_id = event.pathParameters.id

      const params = {
        TableName: process.env.cartTableName,
        FilterExpression: "cartId = :cartId",
        ExpressionAttributeValues: {
          ":cartId": cart_id
          //":companyId": permissions.user_company_id
        }
      };

      const result = await dynamoDbLib.call("scan", params);

      let delivery_point_id = result.Items[0].delivery_point
      let delivery_date = result.Items[0].delivery_date
      let items = result.Items[0].items

      let shop_categories = await dynamoDbLib.call("scan", {
        TableName: process.env.objectsTableName,
        FilterExpression: "objectType = :objectType and objectShopId = :objectShopId",
        ExpressionAttributeValues: {
          ":objectType": 'category',
          ":objectShopId": result.Items[0].shopId
        }
      });

      let shop_products = await dynamoDbLib.call("scan", {
        TableName: process.env.objectsTableName,
        FilterExpression: "objectType = :objectType and objectShopId = :objectShopId",
        ExpressionAttributeValues: {
          ":objectType": 'product',
          ":objectShopId": result.Items[0].shopId
        }
      });


      for (var y in items) {

        for (var z in shop_products.Items) {
          if (items[y].id == shop_products.Items[z].objectId) {

            for (var k in shop_categories.Items) {
              if (shop_categories.Items[k].objectId == shop_products.Items[z].objectCategory) {
                items[y].category = shop_categories.Items[k].objectName
              }
            }

          }
        }

      }

      let params2 = {
        TableName: process.env.objectsTableName,
        FilterExpression: "objectType = :objectType and objectId = :objectId",
        ExpressionAttributeValues: {
          ":objectType": 'delivery_point',
          ":objectId": delivery_point_id,
        }
      };

      let result2 = await dynamoDbLib.call("scan", params2);


      let userParams = {
        UserPoolId: pool_id,
        Username: result.Items[0].userId
      };

      let user = await cognito_client.adminGetUser(userParams).promise()

      let on_behalf_of = null

      if (result.Items[0].on_behalf_of) {

        on_behalf_of = await cognito_client.adminGetUser({
          UserPoolId: pool_id,
          Username: result.Items[0].on_behalf_of
        }).promise()

      }

      let company_id = result.Items[0].companyId
      let user_company = null

      if (company_id) {
        user_company = await dynamoDbLib.call("scan", {
          TableName: process.env.objectsTableName,
          FilterExpression: "objectType = :objectType and objectId = :objectId",
          ExpressionAttributeValues: {
            ":objectType": 'company',
            ":objectId": company_id,
          }
        });

        user_company = user_company.Items[0]

      }


      callback(null, success({
        cart: {
          delivery_date,
          items,
          delivery_point_name: result2.Items[0].objectName,
          delivery_point_address: result2.Items[0].objectAddress,
          total: result.Items[0].total,
          schedule_dates: result.Items[0].schedule_dates,
          schedule_type: result.Items[0].schedule_type,
          schedule_duration_type: result.Items[0].schedule_duration_type,
          comment: result.Items[0].comment,
          freight: result.Items[0].freight,
          delivery_time: result.Items[0].delivery_time,
          delivery_date: (result.Items[0].delivery_date) ? result.Items[0].delivery_date : null,
          delivery_date_start: (result.Items[0].delivery_date_start) ? result.Items[0].delivery_date_start : null,
          delivery_date_end: (result.Items[0].delivery_date_end) ? result.Items[0].delivery_date_end : null,
          volume_order_total_products: (result.Items[0].volume_order_total_products) ? result.Items[0].volume_order_total_products : null,
          recurring_start_date: (result.Items[0].recurring_start_date) ? result.Items[0].recurring_start_date : null,
          user: user,
          on_behalf_of: on_behalf_of,
          user_company: user_company
        }
      }))

    } else {

      callback(null, failure({
        status: 'you do not have access to this api call'
      }));

    }

  } catch (e) {
    bugsnagClient.notify(user_id, e)
    callback(null, failure({
      status: e
    }));
  }

}