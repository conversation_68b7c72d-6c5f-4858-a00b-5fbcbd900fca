import * as dynamoDbLib from "../../../libs/dynamodb-lib";
import * as bugsnagClient from "../../../libs/bugsnag";

import {
  success,
  failure
} from "../../../libs/response-lib";

import {
  is_level_permitted
} from "../../../libs/permissions";

var arraySort = require('array-sort')

export async function integrations(event, context, callback) {

  try {

    const access_scope = ['owner', 'manager', 'employee']
    var user_id = event.requestContext.identity.cognitoAuthenticationProvider.split('CognitoSignIn:')[1]
    //var user_id = '62f6d0d4-4d4c-4ce3-82c9-0d92b4b21e3e'

    const permissions = await is_level_permitted(user_id, access_scope);
    if (permissions.level_allowed) {


      let shop_id = event.pathParameters.id
      //let shop_id = 'c07bee70-7030-11ea-bac1-379e0c3f6753'
      let errors = []

      let shop = await dynamoDbLib.call("scan", {
        TableName: process.env.objectsTableName,
        FilterExpression: "objectId = :objectId",
        ExpressionAttributeValues: {
          ":objectId": shop_id
        }
      });

      let poweroffice_integration = (shop.Items[0].objectPowerOfficeIntegration) ? shop.Items[0].objectPowerOfficeIntegration : null

      if (poweroffice_integration) {

        const integration = await dynamoDbLib.call("scan", {
          TableName: process.env.integrationsTableName,
          FilterExpression: "integrationId = :integrationId",
          ExpressionAttributeValues: {
            ":integrationId": poweroffice_integration
          }
        });

        if (!integration.Items[0].integrationPO0MVA) {
          errors.push({
            type: 'account',
            value: 'mva_0'
          })
        }

        if (!integration.Items[0].integrationPO15MVA) {
          errors.push({
            type: 'account',
            value: 'mva_15'
          })
        }

        if (!integration.Items[0].integrationPO25MVA) {
          errors.push({
            type: 'account',
            value: 'mva_25'
          })
        }

        if (!integration.Items[0].integrationPOBankFeesAccount) {
          errors.push({
            type: 'account',
            value: 'bank'
          })
        }




        let shops_permissions = await dynamoDbLib.call("scan", {
          TableName: process.env.userPermissionsTableName,
          FilterExpression: "grantedBy = :grantedBy and roleType = :roleType and isActive = :isActive and shopId = :shopId",
          ExpressionAttributeValues: {
            ":grantedBy": permissions.user_company_id,
            ":roleType": 'shop_access_role',
            ":isActive": true,
            ":shopId": shop_id
          }
        })


        for (var x in shops_permissions.Items) {

          if (!shops_permissions.Items[x].powerofficeCustomerId) {

            let customer = await dynamoDbLib.call("scan", {
              TableName: process.env.objectsTableName,
              FilterExpression: "objectType = :objectType and objectId = :objectId",
              ExpressionAttributeValues: {
                ":objectType": 'company',
                ":objectId": shops_permissions.Items[x].companyId
              }
            })

            errors.push({
              type: 'customer',
              value: customer.Items[0].objectName,
              id: customer.Items[0].objectId
            })

          }
        }


        const products = await dynamoDbLib.call("scan", {
          TableName: process.env.objectsTableName,
          FilterExpression: "objectStatus <> :objectStatus and objectType = :objectType and objectCompanyId = :objectCompanyId and objectShopId = :objectShopId",
          ExpressionAttributeValues: {
            ":objectType": 'product',
            ":objectStatus": 'deleted',
            ":objectCompanyId": permissions.user_company_id,
            ":objectShopId": shop_id
          }
        });

        for (var x in products.Items) {

          if (!products.Items[x].powerOfficeProductId) {

            errors.push({
              type: 'product',
              value: products.Items[x].objectName,
              id: products.Items[x].objectId
            })

          }
        }

        callback(null, success({
          name: integration.Items[0].integrationName,
          id: poweroffice_integration,
          errors: errors
        }));

      }

      callback(null, success({
        name: null,
        id: null,
        errors: []
      }));



    } else {

      callback(null, failure({
        status: 'you do not have access to this api call'
      }));

    }

  } catch (e) {
    console.log(e);
    bugsnagClient.notify(user_id, e)
    callback(null, failure({
      status: e
    }));
  }
}