import * as dynamoDbLib from "../../../libs/dynamodb-lib";
import * as bugsnagClient from "../../../libs/bugsnag";

import {
  success,
  failure
} from "../../../libs/response-lib";

import {
  is_level_permitted
} from "../../../libs/permissions";


export async function order(event, context, callback) {


  try {

    const access_scope = ['owner', 'manager', 'employee']

    const data = JSON.parse(event.body);

    var user_id = event.requestContext.identity.cognitoAuthenticationProvider.split('CognitoSignIn:')[1]

    const permissions = await is_level_permitted(user_id, access_scope);

    if (permissions.level_allowed) {

      let shop_id = event.pathParameters.id

      const shopParams = {
        TableName: process.env.objectsTableName,
        FilterExpression: "objectType = :objectType and objectId = :objectId",
        ExpressionAttributeValues: {
          ":objectType": 'shop',
          ":objectId": shop_id
        }
      }

      const shopResult = await dynamoDbLib.call("scan", shopParams);

      let shop = shopResult.Items[0]
      let cart_id = data.cart_id


      if (permissions.user_company_id == shop.objectCompanyId) {

        const params = {
          TableName: process.env.cartTableName,

          Key: {
            cartId: cart_id
          },

          UpdateExpression: "SET orderStatus = :orderStatus, shippedAt = :shippedAt, shippedBy = :shippedBy",
          ExpressionAttributeValues: {
            ":orderStatus": "completed",
            ":shippedBy": user_id,
            ":shippedAt": new Date().getTime(),
          },
          ReturnValues: "ALL_NEW"
        };

        const result = await dynamoDbLib.call("update", params);


        callback(null, success('done'));

      } else {

        callback(null, failure({
          status: 'you do not have access to this shop'
        }));
      }

    } else {

      callback(null, failure({
        status: 'you do not have access to this api call'
      }));

    }
  } catch (e) {
    bugsnagClient.notify(user_id, e)
    callback(null, failure({
      status: e
    }));
  }
}