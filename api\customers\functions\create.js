import {
  success,
  failure
} from "../../../libs/response-lib";


import {
  is_level_permitted
} from "../../../libs/permissions";

import * as dynamoDbLib from "../../../libs/dynamodb-lib";
import * as bugsnagClient from "../../../libs/bugsnag";
import uuid from "uuid";

const AWS = require('aws-sdk')
const SNS = new AWS.SNS();
const cognito_client = new AWS.CognitoIdentityServiceProvider()

export async function customer(event, context, callback) {


  try {

    const data = JSON.parse(event.body);

    let access_scope = ['owner', 'manager', 'employee']

    var user_id = event.requestContext.identity.cognitoAuthenticationProvider.split('CognitoSignIn:')[1]

    var pool_id = process.env.COGNITO_POOL_ID

    let shop_id = data.shop_id
    let discount = data.discount
    let shipping = (data.shipping) ? data.shipping : null
    let poweroffice_customers_id = data.poweroffice_customers_id
    let allow_volume_orders = data.allow_volume_orders
    let allow_recurring_orders = data.allow_recurring_orders
    let invoice_delivery_type = data.invoice_delivery_type
    let allowed_payment_options = data.allowed_payment_options
    let shops = []


    const permissions = await is_level_permitted(user_id, access_scope);

    if (permissions.level_allowed) {


      let params = {
        UserPoolId: pool_id,
        Filter: "email = \"" + data.email + "\""
      };

      let new_user = await cognito_client.listUsers(params).promise()

      let new_user_id = ''
      let company_id = ''


      if (new_user.Users.length == 0) {


        company_id = uuid.v1()

        const companyParams = {
          TableName: process.env.objectsTableName,
          Item: {
            objectCreatedBy: user_id,
            objectCompanyId: company_id,
            objectId: company_id,
            objectName: data.company_name,
            objectAddress: data.company_address,
            objectPostCode: data.company_post_code,
            objectCity: data.company_city,
            objectOrganizationId: data.organization_id,
            objectType: "company",
            createdAt: new Date().getTime()
          }
        };

        await dynamoDbLib.call("put", companyParams);
        let p1 = uuid.v1()
        let permissionParams = {

          TableName: process.env.userPermissionsTableName,
          Item: {
            grantedBy: permissions.user_company_id,
            companyId: company_id,
            roleType: 'shop_access_role',
            shopId: shop_id,
            discount: discount,
            shipping: shipping,
            powerofficeCustomerId: poweroffice_customers_id,
            allow_volume_orders: allow_volume_orders,
            allow_recurring_orders: allow_recurring_orders,
            invoice_delivery_type: invoice_delivery_type,
            allowed_payment_options: allowed_payment_options,
            isActive: true,
            permissionId: p1,
            createdAt: new Date().getTime()
          }
        };

        await dynamoDbLib.call("put", permissionParams);

        params = {
          UserPoolId: pool_id,
          Username: data.email,
          ForceAliasCreation: false,
          UserAttributes: [{
              Name: 'custom:company_name',
              Value: data.company_name
            },
            {
              Name: 'custom:organization_id',
              Value: "" + data.organization_id
            },
            {
              Name: 'custom:first_name',
              Value: data.first_name
            },
            {
              Name: 'custom:last_name',
              Value: data.last_name
            },
            {
              Name: 'custom:user_phone',
              Value: data.phone
            },
            {
              Name: 'custom:user_type',
              Value: 'owner'
            },
            {
              Name: 'name',
              Value: company_id
            },
            {
              Name: 'custom:company_id',
              Value: company_id
            },
            {
              Name: 'email_verified',
              Value: 'true'
            },
            {
              Name: 'custom:created_as',
              Value: 'company_customer'
            },
            {
              Name: 'email',
              Value: data.email
            },
            {
              Name: 'custom:language',
              Value: 'no'
            }
          ]
        };

        new_user = await cognito_client.adminCreateUser(params).promise()
        new_user_id = new_user.User.Username


        let params2 = {
          UserAttributes: [{
            Name: 'custom:company_id',
            Value: company_id
          }],
          UserPoolId: pool_id,
          Username: new_user_id
        };

        await cognito_client.adminUpdateUserAttributes(params2).promise()

        // set comapny id
        var params3 = {
          GroupName: 'Owner',
          UserPoolId: pool_id,
          Username: new_user_id
        };

        // add user to company group
        await cognito_client.adminAddUserToGroup(params3).promise()




        let location_id = uuid.v1()
        const locationParams = {
          TableName: process.env.objectsTableName,
          Item: {
            objectCreatedBy: new_user.User.Username,
            objectCompanyId: company_id,
            objectId: location_id,
            objectName: 'Location 1',
            //  objectLocation: '',
            objectType: "asset",
            createdAt: new Date().getTime()
          }
        };

        await dynamoDbLib.call("put", locationParams);

        const locationPermissionParams = {

          TableName: process.env.userPermissionsTableName,
          Item: {
            grantedBy: 'signup',
            userId: new_user_id,
            companyId: company_id,
            locationId: location_id,
            permissionId: uuid.v1(),
            createdAt: new Date().getTime()
          }
        };

        await dynamoDbLib.call("put", locationPermissionParams);

        await SNS.publish({
          Message: p1,
          TopicArn: process.env.generatePowerOfficeCustomersSNSTopic
        }, function(err, data) {

          if (err) {
            callback(null, err);
          }

        });


      } else {

        for (var x in new_user.Users[0].Attributes) {

          if (new_user.Users[0].Attributes[x].Name == 'custom:company_id') {
            company_id = new_user.Users[0].Attributes[x].Value
          }

        }

        let customerParams = {
          TableName: process.env.userPermissionsTableName,
          FilterExpression: "grantedBy = :grantedBy and roleType = :roleType and isActive = :isActive and companyId = :companyId and shopId = :shopId",
          ExpressionAttributeValues: {
            ":grantedBy": permissions.user_company_id,
            ":roleType": 'shop_access_role',
            ":isActive": true,
            ":companyId": company_id,
            ":shopId": shop_id
          }
        };

        let customer = await dynamoDbLib.call("scan", customerParams)

        if (customer.Items.length == 0) {

          new_user_id = new_user.Users[0].Username
          let p2 = uuid.v1()

          let permissionParams = {

            TableName: process.env.userPermissionsTableName,
            Item: {
              grantedBy: permissions.user_company_id,
              companyId: company_id,
              roleType: 'shop_access_role',
              discount: discount,
              shipping: shipping,
              powerofficeCustomerId: poweroffice_customers_id,
              allow_volume_orders: allow_volume_orders,
              allow_recurring_orders: allow_recurring_orders,
              invoice_delivery_type: invoice_delivery_type,
              allowed_payment_options: allowed_payment_options,
              shopId: shop_id,
              isActive: true,
              permissionId: p2,
              createdAt: new Date().getTime()
            }
          };

          await dynamoDbLib.call("put", permissionParams);

          await SNS.publish({
            Message: p2,
            TopicArn: process.env.generatePowerOfficeCustomersSNSTopic
          }, function(err, data) {

            if (err) {
              callback(null, err);
            }

          });

        } else {

          callback(null, failure({
            status: 'you already have relation with this customer'
          }));

        }
      }

      callback(null, success(company_id))

    } else {

      callback(null, failure({
        status: 'you do not have access to this api call'
      }));

    }

  } catch (e) {
    bugsnagClient.notify(user_id, e)
    callback(null, failure({
      error: e
    }))
  }

}