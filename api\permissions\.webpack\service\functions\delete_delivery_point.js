!function(e,t){for(var n in t)e[n]=t[n]}(exports,function(e){var t={};function n(r){if(t[r])return t[r].exports;var s=t[r]={i:r,l:!1,exports:{}};return e[r].call(s.exports,s,s.exports,n),s.l=!0,s.exports}return n.m=e,n.c=t,n.d=function(e,t,r){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:r})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(n.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var s in e)n.d(r,s,function(t){return e[t]}.bind(null,s));return r},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=3)}([function(e,t){e.exports=require("aws-sdk")},function(e,t,n){"use strict";n.r(t),n.d(t,"call",function(){return a});var r=n(0),s=n.n(r);s.a.config.update({region:"eu-central-1"});const o=new s.a.DynamoDB.DocumentClient;async function a(e,t){if("scan"==e){let e={Items:[]},r=await o.scan(t).promise();for(e.Items=r.Items;void 0!==r.LastEvaluatedKey;)for(var n in t.ExclusiveStartKey=r.LastEvaluatedKey,(r=await o.scan(t).promise()).Items)e.Items.push(r.Items[n]);return e}return o[e](t).promise()}},function(e,t){e.exports=require("@bugsnag/js")},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.permission=void 0;var r=l(n(4)),s=l(n(5)),o=(t.permission=function(){var e=(0,s.default)(r.default.mark(function e(t,n,s){var c,l,d,p,m,f,b;return r.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,c=["owner","manager","employee"],l=t.requestContext.identity.cognitoAuthenticationProvider.split("CognitoSignIn:")[1],e.next=5,(0,u.is_level_permitted)(l,c);case 5:if(!e.sent.level_allowed){e.next=34;break}if(!!0){e.next=31;break}return d={TableName:process.env.userPermissionsTableName,FilterExpression:"userId = :userId",ExpressionAttributeValues:{":userId":t.pathParameters.id}},e.next=12,o.call("scan",d);case 12:p=e.sent,e.t0=r.default.keys(p.Items);case 14:if((e.t1=e.t0()).done){e.next=28;break}if(m=e.t1.value,!p.Items[m].locationId){e.next=26;break}return f=p.Items[m].permissionId,e.next=20,o.call("scan",{TableName:process.env.objectsTableName,FilterExpression:"objectType = :objectType and objectId = :objectId and objectShopId = :objectShopId",ExpressionAttributeValues:{":objectType":"delivery_point",":objectId":p.Items[m].locationId,":objectShopId":t.pathParameters.shop}});case 20:if(1!=e.sent.Items.length){e.next=26;break}return b={TableName:process.env.userPermissionsTableName,Key:{permissionId:f}},e.next=25,o.call("delete",b);case 25:e.sent;case 26:e.next=14;break;case 28:s(null,(0,i.success)({status:!0})),e.next=32;break;case 31:s(null,(0,i.failure)({status:"you do not have access to this api call"}));case 32:e.next=35;break;case 34:s(null,(0,i.failure)({status:"you do not have access to this api call"}));case 35:e.next=41;break;case 37:e.prev=37,e.t2=e.catch(0),a.notify(l,e.t2),s(null,(0,i.failure)({error:e.t2}));case 41:case"end":return e.stop()}},e,this,[[0,37]])}));return function(t,n,r){return e.apply(this,arguments)}}(),c(n(1))),a=c(n(6)),i=n(7),u=n(8);function c(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t.default=e,t}function l(e){return e&&e.__esModule?e:{default:e}}},function(e,t){e.exports=require("babel-runtime/regenerator")},function(e,t){e.exports=require("babel-runtime/helpers/asyncToGenerator")},function(e,t,n){"use strict";n.r(t),n.d(t,"notify",function(){return o});var r=n(2);const s=n.n(r)()({apiKey:"fd490dc0489374329842ddd3b0b568d7",releaseStage:"dev",appType:"backend",appVersion:"1.00"});function o(e=null,t){s.user={id:e},s.notify(t)}},function(e,t,n){"use strict";function r(e){return o(200,e)}function s(e){return o(500,e)}function o(e,t){return{statusCode:e,headers:{"Access-Control-Allow-Origin":"*","Access-Control-Allow-Credentials":!0},body:JSON.stringify(t)}}n.r(t),n.d(t,"success",function(){return r}),n.d(t,"failure",function(){return s})},function(e,t,n){"use strict";n.r(t),n.d(t,"is_level_permitted",function(){return a}),n.d(t,"is_object_permitted",function(){return i});var r=n(0),s=n.n(r),o=n(1);function a(e,t){return new Promise(function(n,r){let a=new s.a.CognitoIdentityServiceProvider,i=(process.env.COGNITO_POOL_ID,""),u="",c="",l="",d="",p={UserPoolId:process.env.COGNITO_POOL_ID,Username:e};a.adminGetUser(p,async function(e,s){if(e)r(e);else{for(var a in s.UserAttributes)"custom:company_id"==s.UserAttributes[a].Name&&(i=s.UserAttributes[a].Value),"custom:user_type"==s.UserAttributes[a].Name&&(u=s.UserAttributes[a].Value),"custom:first_name"==s.UserAttributes[a].Name&&(l=s.UserAttributes[a].Value),"custom:last_name"==s.UserAttributes[a].Name&&(d=s.UserAttributes[a].Value),"email"==s.UserAttributes[a].Name&&(c=s.UserAttributes[a].Value);const e=await o.call("scan",{TableName:process.env.objectsTableName,FilterExpression:"objectType = :objectType and objectId = :objectId",ExpressionAttributeValues:{":objectType":"company",":objectId":i}});"company"==u||"owner"==u?n({level_allowed:!0,location_allowed:!0,user_company_id:i,user_company_name:e.Items[0].objectName?e.Items[0].objectName:null,user_type:u,user_email:c,user_full_name:l+" "+d}):-1!=t.indexOf(u)?n({level_allowed:!0,location_allowed:!1,user_company_id:i,user_company_name:e.Items[0].objectName?e.Items[0].objectName:null,user_type:u,user_full_name:l+" "+d}):n({level_allowed:!1,location_allowed:!1,user_company_id:i,user_company_name:e.Items[0].objectName?e.Items[0].objectName:null,user_type:u,user_full_name:l+" "+d})}})})}function i(e,t,n,r=null){return new Promise(async function(s,a){"company"!=r&&"owner"!=r||s({is_object_allowed:!0});const i={TableName:process.env.userPermissionsTableName,FilterExpression:"locationId = :locationId and companyId = :companyId and userId = :userId",ExpressionAttributeValues:{":userId":e,":locationId":n,":companyId":t}};1==(await o.call("scan",i)).Items.length?s({is_object_allowed:!0}):s({is_object_allowed:!1})})}s.a.config.update({region:"eu-central-1"})}]));