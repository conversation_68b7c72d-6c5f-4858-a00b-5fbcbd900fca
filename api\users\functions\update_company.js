import {
  success,
  failure
} from "../../../libs/response-lib";
import * as dynamoDbLib from "../../../libs/dynamodb-lib";
import {
  is_level_permitted
} from "../../../libs/permissions";
import * as bugsnagClient from "../../../libs/bugsnag";

export async function company(event, context, callback) {

  try {
    let access_scope = ['owner', 'manager', 'employee']
    //  const data = JSON.parse(event.body);
    var user_id = event.requestContext.identity.cognitoAuthenticationProvider.split('CognitoSignIn:')[1]

    const permissions = await is_level_permitted(user_id, access_scope);

    if (permissions.level_allowed) {

      const data = JSON.parse(event.body);

      let update_expression = ''
      let expression_attribute_values = {}

      if (data.settings == 'plan') {
        update_expression = "SET objectPlan = :objectPlan"
        expression_attribute_values = {
          ":objectPlan": data.plan
        }
      }

      if (data.settings == 'info') {
        update_expression = "SET objectTax = :objectTax, objectAddress = :objectAddress, objectCity = :objectCity, objectName = :objectName, objectOrganizationId = :objectOrganizationId, objectPostCode = :objectPostCode, objectCountry = :objectCountry"
        expression_attribute_values = {
          ":objectAddress": data.company_address,
          ":objectCity": data.company_city,
          ":objectName": data.company_name,
          ":objectOrganizationId": data.company_org_number,
          ":objectPostCode": data.company_postcode,
          ":objectCountry": data.company_country,
          ":objectTax": (data.company_tax) ? data.company_tax : null
        }
      }

      await dynamoDbLib.call("update", {
        TableName: process.env.objectsTableName,
        Key: {
          objectId: (data.company_id == null) ? permissions.user_company_id : data.company_id,
          objectCompanyId: (data.company_id == null) ? permissions.user_company_id : data.company_id,
        },

        UpdateExpression: update_expression,
        ExpressionAttributeValues: expression_attribute_values,
        ReturnValues: "ALL_NEW"
      });

      callback(null, success('done'))

    } else {

      callback(null, failure({
        status: 'you do not have access to this api call'
      }));

    }

  } catch (e) {
    bugsnagClient.notify(user_id, e)
    callback(null, failure({
      status: e
    }));
  }

}