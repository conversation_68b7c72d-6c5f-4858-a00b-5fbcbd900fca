import * as dynamoDbLib from "../../../libs/dynamodb-lib";
import * as bugsnagClient from "../../../libs/bugsnag";
import {
  success,
  failure
} from "../../../libs/response-lib";

import {
  is_level_permitted
} from "../../../libs/permissions";

const AWS = require('aws-sdk')
const cognito_client = new AWS.CognitoIdentityServiceProvider()


export async function orders(event, context, callback) {

  try {

    const access_scope = ['owner', 'manager', 'employee']
    const data = JSON.parse(event.body);
    let pool_id = process.env.COGNITO_POOL_ID
    var user_id = event.requestContext.identity.cognitoAuthenticationProvider.split('CognitoSignIn:')[1]

    const permissions = await is_level_permitted(user_id, access_scope);

    if (permissions.level_allowed) {

      let shop_id = event.pathParameters.id
      let order_id = data.orderId

      const shopParams = {
        TableName: process.env.objectsTableName,
        FilterExpression: "objectType = :objectType and objectId = :objectId",
        ExpressionAttributeValues: {
          ":objectType": 'shop',
          ":objectId": shop_id
        }
      }

      const shopResult = await dynamoDbLib.call("scan", shopParams);

      let shop = shopResult.Items[0]


      if (permissions.user_company_id == shop.objectCompanyId) {


        let ordersParams = {
          TableName: process.env.cartTableName,
          FilterExpression: "shopId = :shopId and cartId = :cartId",
          ExpressionAttributeValues: {
            ":shopId": shop_id,
            ":cartId": order_id
          }
        };

        let orders = await dynamoDbLib.call("scan", ordersParams);


        let shop_categories = await dynamoDbLib.call("scan", {
          TableName: process.env.objectsTableName,
          FilterExpression: "objectType = :objectType and objectShopId = :objectShopId",
          ExpressionAttributeValues: {
            ":objectType": 'category',
            ":objectShopId": shop_id
          }
        });

        let shop_products = await dynamoDbLib.call("scan", {
          TableName: process.env.objectsTableName,
          FilterExpression: "objectType = :objectType and objectShopId = :objectShopId",
          ExpressionAttributeValues: {
            ":objectType": 'product',
            ":objectShopId": shop_id
          }
        });


        for (var x in orders.Items) {

          for (var y in orders.Items[x].items) {

            for (var z in shop_products.Items) {
              if (orders.Items[x].items[y].id == shop_products.Items[z].objectId) {

                for (var k in shop_categories.Items) {
                  if (shop_categories.Items[k].objectId == shop_products.Items[z].objectCategory) {
                    orders.Items[x].items[y].category = shop_categories.Items[k].objectName
                  }
                }

              }
            }

          }


          let params = {
            UserPoolId: pool_id,
            Username: orders.Items[x].userId
          };

          let user = await cognito_client.adminGetUser(params).promise()
          orders.Items[x].user = user

          if (orders.Items[x].on_behalf_of) {

            let on_behalf_of = await cognito_client.adminGetUser({
              UserPoolId: pool_id,
              Username: orders.Items[x].on_behalf_of
            }).promise()

            orders.Items[x].on_behalf_of = on_behalf_of

          }

          let company_id = orders.Items[x].companyId



          if (company_id) {
            let user_company = await dynamoDbLib.call("scan", {
              TableName: process.env.objectsTableName,
              FilterExpression: "objectType = :objectType and objectId = :objectId",
              ExpressionAttributeValues: {
                ":objectType": 'company',
                ":objectId": company_id,
              }
            });

            orders.Items[x].user_company = user_company.Items[0]

          }


          let DeliveryPointsParams = {
            TableName: process.env.objectsTableName,
            FilterExpression: "objectType = :objectType and objectId = :objectId",
            ExpressionAttributeValues: {
              ":objectType": 'delivery_point',
              ":objectId": orders.Items[x].delivery_point,
            }
          };

          let delivery_point = await dynamoDbLib.call("scan", DeliveryPointsParams);

          orders.Items[x].delivery_point = delivery_point.Items[0]


        }

        callback(null, success(orders.Items[0]));

      } else {

        callback(null, failure({
          status: 'you do not have access to this shop'
        }));
      }

    } else {

      callback(null, failure({
        status: 'you do not have access to this api call'
      }));

    }
  } catch (e) {
    bugsnagClient.notify(user_id, e)
    callback(null, failure({
      status: e
    }));
  }
}

function inArray(needle, haystack) {
  var length = haystack.length;
  for (var i = 0; i < length; i++) {
    if (haystack[i] == needle) return true;
  }
  return false;
}