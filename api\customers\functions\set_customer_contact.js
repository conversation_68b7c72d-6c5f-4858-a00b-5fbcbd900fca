import {
  success,
  failure
} from "../../../libs/response-lib";


import {
  is_level_permitted
} from "../../../libs/permissions";

import * as dynamoDbLib from "../../../libs/dynamodb-lib";
import * as bugsnagClient from "../../../libs/bugsnag";

export async function customer(event, context, callback) {

  try {

    const data = JSON.parse(event.body);

    let access_scope = ['owner', 'manager', 'employee']

    var user_id = event.requestContext.identity.cognitoAuthenticationProvider.split('CognitoSignIn:')[1]

    let shop_id = data.shop_id
    let company_id = data.company_id
    let contact_id = data.contact_id

    const permissions = await is_level_permitted(user_id, access_scope);

    if (permissions.level_allowed) {

      let customer = await dynamoDbLib.call("scan", {
        TableName: process.env.userPermissionsTableName,
        FilterExpression: "grantedBy = :grantedBy and roleType = :roleType and isActive = :isActive and companyId = :companyId and shopId = :shopId",
        ExpressionAttributeValues: {
          ":grantedBy": permissions.user_company_id,
          ":roleType": 'shop_access_role',
          ":isActive": true,
          ":companyId": company_id,
          ":shopId": shop_id
        }
      })

      if (customer.Items.length == 1) {

        let permissionId = customer.Items[0].permissionId

        const result = await dynamoDbLib.call("update", {
          TableName: process.env.userPermissionsTableName,

          Key: {
            permissionId: permissionId
          },

          UpdateExpression: "SET contactId = :contactId",
          ExpressionAttributeValues: {
            ":contactId": contact_id
          },
          ReturnValues: "ALL_NEW"
        });
      }
      callback(null, success(true))

    } else {

      callback(null, failure({
        status: 'you do not have access to this api call'
      }));

    }

  } catch (e) {
    bugsnagClient.notify(user_id, e)
    callback(null, failure({
      error: e
    }))
  }

}