import * as dynamoDbLib from "../../../libs/dynamodb-lib";
import * as bugsnagClient from "../../../libs/bugsnag";
import {
  success,
  failure
} from "../../../libs/response-lib";

import {
  is_level_permitted,
  is_object_permitted
} from "../../../libs/permissions";

export async function customer(event, context, callback) {


  try {

    const access_scope = ['owner', 'manager']
    const data = JSON.parse(event.body);

    var user_id = event.requestContext.identity.cognitoAuthenticationProvider.split('CognitoSignIn:')[1]

    const permissions = await is_level_permitted(user_id, access_scope);
    if (permissions.level_allowed) {


      const permissionsParam = {

        TableName: process.env.userPermissionsTableName,
        FilterExpression: "companyId = :companyId and grantedBy = :grantedBy and shopId = :shopId",
        ExpressionAttributeValues: {
          ":companyId": event.pathParameters.id,
          ":grantedBy": permissions.user_company_id,
          ":shopId": data.shopId
        }
      };

      let permissionsResult = await dynamoDbLib.call("scan", permissionsParam);


      for (var x in permissionsResult.Items) {

        let permission_id = permissionsResult.Items[x].permissionId

        const params = {
          TableName: process.env.userPermissionsTableName,
          Key: {
            permissionId: permission_id

          }
        };

        const result = await dynamoDbLib.call("delete", params);

      }

      const permissionsParam2 = {

        TableName: process.env.userPermissionsTableName,
        FilterExpression: "userId = :userId and companyId = :companyId",
        ExpressionAttributeValues: {
          ":userId": event.pathParameters.id,
          ":companyId": permissions.user_company_id
        }
      };

      let permissionsResult2 = await dynamoDbLib.call("scan", permissionsParam2);


      for (var x in permissionsResult2.Items) {

        let permission_id = permissionsResult2.Items[x].permissionId

        const params = {
          TableName: process.env.userPermissionsTableName,
          Key: {
            permissionId: permission_id

          }
        };

        const result = await dynamoDbLib.call("delete", params);

      }

      callback(null, success({
        status: true
      }));

    } else {

      callback(null, failure({
        status: 'you do not have access to this api call'
      }));

    }

  } catch (e) {
    bugsnagClient.notify(user_id, e)
    callback(null, failure({
      error: e
    }))
  }

}