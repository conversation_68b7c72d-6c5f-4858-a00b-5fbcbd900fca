import {
  success,
  failure
} from "../../libs/response-lib";

import {
  is_level_permitted
} from "../../libs/permissions";
import * as bugsnagClient from "../../libs/bugsnag";

const AWS = require('aws-sdk')
const acm = new AWS.ACM({
  region: "us-east-1"
});

const cloudfront = new AWS.CloudFront({
  apiVersion: '2018-11-05',
  accessKeyId: '********************',
  secretAccessKey: '6bB750d1OHP1Efz2f1AXrXO8kPZhAOpKf22xsyVk',
});


export async function init(event, context, callback) {

  try {

    /*

    var params = {
      DomainName: 'k53.treats.no',

      //CertificateAuthorityArn: 'arn:aws:acm:eu-central-1:589634798762:certificate/2ca599b9-e199-4365-a0a5-3902d61fa8e9',
      DomainValidationOptions: [{
          DomainName: 'k53.treats.no',

          ValidationDomain: 'k53.treats.no'
        },

      ],

      ValidationMethod: 'DNS'
    };
    acm.requestCertificate(params, function(err, data) {
      if (err) console.log(err, err.stack); // an error occurred
      else console.log(data); // successful response
    });



    acm.describeCertificate({
      CertificateArn: 'arn:aws:acm:us-east-1:589634798762:certificate/ea196dc8-dc34-4a7c-921c-7a2e820ff717'
    }, function(err, data) {
      if (err) console.log(err, err.stack);
      else console.log(data.Certificate.DomainValidationOptions);
    });

      */
    /*
    let cc = await cloudfront.getDistribution({
      Id: "E3QK0UG059QCMY"
    }).promise()

    console.log(cc);



    acm.waitFor('certificateValidated', {
      CertificateArn: 'arn:aws:acm:us-east-1:589634798762:certificate/e3c8df7c-fd13-46dc-82fe-6c6dd1dac3d2'
    }, function(err, data) {
      if (err) console.log(err, err.stack); // an error occurred
      else console.log(data); // successful response
    });

    /*
        let cccong = await cloudfront.getDistributionConfig({
          Id: process.env.SHOP_CLOUDFRONT_DIST
        }).promise();



        cccong.DistributionConfig.CallerReference = 'Hamada'
        cccong.DistributionConfig.Aliases.Items = []
        cccong.DistributionConfig.Aliases.Quantity = 0

        let cf = await cloudfront.createDistribution({
          DistributionConfig: cccong.DistributionConfig
        }).promise();
        */

    //console.log(cccong);
    /*
    let cc = await cloudfront.getDistribution({
      Id: "E2FAX8O16AMHNY"
    }).promise()
    */

    acm.waitFor('certificateValidated', {
      CertificateArn: 'arn:aws:acm:us-east-1:589634798762:certificate/08245f50-24ca-425f-a238-08f780a51ce0'
    }, function(err, data) {
      if (err) console.log(err, err.stack); // an error occurred
      else console.log(data); // successful response
    });



  } catch (e) {
    //bugsnagClient.notify(user_id, e)
    callback(null, failure({
      error: e
    }))
  }

}