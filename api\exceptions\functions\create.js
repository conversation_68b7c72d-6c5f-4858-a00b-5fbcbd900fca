import uuid from "uuid";
import * as dynamoDbLib from "../../../libs/dynamodb-lib";
import * as bugsnagClient from "../../../libs/bugsnag";

import {
  success,
  failure
} from "../../../libs/response-lib";

import {
  is_level_permitted
} from "../../../libs/permissions";

export async function exception(event, context, callback) {


  try {

    let access_scope = ['owner', 'manager', 'employee']

    const data = JSON.parse(event.body);
    var user_id = event.requestContext.identity.cognitoAuthenticationProvider.split('CognitoSignIn:')[1]



    const permissions = await is_level_permitted(user_id, access_scope);

    if (permissions.level_allowed) {

      let exception_id = uuid.v1()
      const params = {

        TableName: process.env.exceptionsTableName,

        Item: {
          user_id: user_id,
          exception_id: exception_id,
          parent_type: (data.parent_type) ? data.parent_type : 'deviation',
          parent_id: (data.parent_id) ? data.parent_id : '',
          location_id: data.location_id,
          deviation_description: data.deviation_description,
          corrective_measures: data.corrective_measures,
          name: data.name,
          how_solved: data.how_solved,
          createdAt: new Date().getTime(),
          companyId: permissions.user_company_id
        }
      };
      await dynamoDbLib.call("put", params);
      callback(null, success({
        exception_id
      }));

    } else {

      callback(null, failure({
        status: 'you do not have access to this api call'
      }));

    }

  } catch (e) {
    bugsnagClient.notify(user_id, e)

    callback(null, failure({
      status: false
    }));
  }
}