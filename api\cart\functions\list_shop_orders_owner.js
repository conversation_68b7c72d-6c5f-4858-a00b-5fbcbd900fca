import * as dynamoDbLib from "../../../libs/dynamodb-lib";
import * as bugsnagClient from "../../../libs/bugsnag";
import {
  success,
  failure
} from "../../../libs/response-lib";

import {
  is_level_permitted
} from "../../../libs/permissions";

const AWS = require('aws-sdk')
var arraySort = require('array-sort')
var unique = require('array-unique');
const cognito_client = new AWS.CognitoIdentityServiceProvider()


export async function orders(event, context, callback) {

  try {

    const access_scope = ['owner', 'manager', 'employee']
    const data = JSON.parse(event.body);
    let pool_id = process.env.COGNITO_POOL_ID
    var user_id = event.requestContext.identity.cognitoAuthenticationProvider.split('CognitoSignIn:')[1]

    let mode = data.mode

    const permissions = await is_level_permitted(user_id, access_scope);

    if (permissions.level_allowed) {

      let shop_id = event.pathParameters.id

      let shopParams = {}
      let resultOrders = []
      let shops = []

      for (var x in data.filters.filters) {
        if (data.filters.filters[x].type.value == 'shopsList') {
          shops.push(data.filters.filters[x].value.value)
        }
      }

      if (shop_id != 'null') {

        shopParams = {
          TableName: process.env.objectsTableName,
          FilterExpression: "objectType = :objectType and objectId = :objectId and objectCompanyId = :objectCompanyId",
          ExpressionAttributeValues: {
            ":objectType": 'shop',
            ":objectId": shop_id,
            ":objectCompanyId": permissions.user_company_id,
          }
        }

      } else {

        if (shops.length > 0) {

          let shops_filter_expression = ''
          let shops_filter_expression_values = {
            ":objectType": 'shop',
            ":objectCompanyId": permissions.user_company_id
          }

          shops_filter_expression += ' and ('

          for (var x in shops) {
            shops_filter_expression += ' objectId = :shop' + x
            if (x < shops.length - 1) {
              shops_filter_expression += ' or '
            }

            if (x == shops.length - 1) {
              shops_filter_expression += ' ) '
            }

            shops_filter_expression_values[':shop' + x] = shops[x]

          }

          shopParams = {
            TableName: process.env.objectsTableName,
            FilterExpression: "objectType = :objectType and objectCompanyId = :objectCompanyId" + shops_filter_expression,
            ExpressionAttributeValues: shops_filter_expression_values
          }




        } else {
          shopParams = {
            TableName: process.env.objectsTableName,
            FilterExpression: "objectType = :objectType and objectCompanyId = :objectCompanyId",
            ExpressionAttributeValues: {
              ":objectType": 'shop',
              ":objectCompanyId": permissions.user_company_id
            }
          }

        }

      }

      const shopResult = await dynamoDbLib.call("scan", shopParams);

      let congnito_users = []

      let user_pool = await cognito_client.describeUserPool({
        UserPoolId: pool_id
      }).promise()

      let number_of_users = user_pool.UserPool.EstimatedNumberOfUsers

      let loops = Math.ceil(number_of_users / 60)

      let token = ''

      for (var i = 1; i <= loops; i++) {

        let ownersParams = {};

        if (token != '') {
          ownersParams = {
            UserPoolId: pool_id,
            AttributesToGet: null,
            Limit: 0,
            PaginationToken: token
          }
        } else {
          ownersParams = {
            UserPoolId: pool_id,
            AttributesToGet: null,
            Limit: 0
          }
        }

        let users_data = await cognito_client.listUsers(ownersParams).promise()
        token = users_data.PaginationToken

        for (var x in users_data.Users) {
          congnito_users.push(users_data.Users[x])
        }


      }


      for (var b in shopResult.Items) {

        let shop = shopResult.Items[b]
        shop_id = shopResult.Items[b].objectId


        if (permissions.user_company_id == shop.objectCompanyId) {

          let today = new Date();

          let today16 = new Date();
          today16.setHours(16, 0, 0, 0);
          let today16_time_stamp = today16.getTime()

          let tomorrow16 = new Date();
          tomorrow16.setDate(today.getDate() + 1);

          tomorrow16.setHours(16, 0, 0, 0);
          let tomorrow16_time_stamp = tomorrow16.getTime()


          let from = data.filters.from
          let to = data.filters.to
          let customers = []
          let products = []
          let order_status = null
          let order_id = null
          let payment_method = null

          for (var x in data.filters.filters) {
            if (data.filters.filters[x].type.value == 'customers') {
              customers.push(data.filters.filters[x].value.value)
            }
            if (data.filters.filters[x].type.value == 'productsList') {
              products.push(data.filters.filters[x].value.value)
            }
            if (data.filters.filters[x].type.value == 'orderStatus') {
              order_status = data.filters.filters[x].value.value
            }
            if (data.filters.filters[x].type.value == 'orderid') {
              order_id = data.filters.filters[x].value.value
            }
            if (data.filters.filters[x].type.value == 'paymentMethod') {
              payment_method = data.filters.filters[x].value.value
            }
          }

          // this is the shop owner.

          let ordersParams = {}
          ordersParams.TableName = process.env.cartTableName
          ordersParams.FilterExpression = ''
          ordersParams.ExpressionAttributeValues = {}
          ordersParams.FilterExpression = 'shopId = :shopId and schedule_type = :schedule_type'
          if (mode == 'recurring') {

            ordersParams.ExpressionAttributeValues[":schedule_type"] = 'weekly'
          } else {
            ordersParams.ExpressionAttributeValues[":schedule_type"] = 'once'

          }

          ordersParams.ExpressionAttributeValues[":shopId"] = shop_id



          if (from && to && mode != 'recurring') {
            ordersParams.FilterExpression += ' and delivery_date >= :date1 and delivery_date <= :date2 '

            ordersParams.ExpressionAttributeValues[":date1"] = from
            ordersParams.ExpressionAttributeValues[":date2"] = to

          }

          if (payment_method) {
            ordersParams.FilterExpression += ' and paymentMethod = :paymentMethod '

            ordersParams.ExpressionAttributeValues[":paymentMethod"] = payment_method

          }

          if (order_status) {

            ordersParams.FilterExpression += ' and orderStatus = :orderStatus '

            ordersParams.ExpressionAttributeValues[":orderStatus"] = order_status

          }

          if (order_id) {
            ordersParams.FilterExpression += ' and cartId = :cartId '

            ordersParams.ExpressionAttributeValues[":cartId"] = order_id

          }


          let users = []
          let compaines = []
          let delivery_points = []

          let users_ids = []
          let compaines_ids = []
          let delivery_points_ids = []

          let orders = await dynamoDbLib.call("scan", ordersParams);

          for (var x in orders.Items) {

            if (!users_ids.includes(orders.Items[x].userId)) {
              users_ids.push(orders.Items[x].userId)
            }

            if (orders.Items[x].on_behalf_of) {
              if (!users_ids.includes(orders.Items[x].on_behalf_of)) {
                users_ids.push(orders.Items[x].on_behalf_of)
              }

            }

            if (orders.Items[x].canceledBy) {
              if (!users_ids.includes(orders.Items[x].canceledBy)) {
                users_ids.push(orders.Items[x].canceledBy)
              }

            }

            if (!delivery_points_ids.includes(orders.Items[x].delivery_point)) {
              delivery_points_ids.push(orders.Items[x].delivery_point)
            }

          }



          for (var x in users_ids) {

            let user = {}

            for (var y in congnito_users) {
              if (congnito_users[y].Username == users_ids[x]) {
                user = congnito_users[y]
                users.push({
                  user_id: users_ids[x],
                  user: congnito_users[y]
                })
              }
            }



            let company_id = null

            for (var z in user.Attributes) {
              if (user.Attributes[z].Name == 'name') {
                company_id = user.Attributes[z].Value
              }
            }

            if (!compaines_ids.includes(company_id)) {
              compaines_ids.push(company_id)
            }

          }

          for (var x in compaines_ids) {

            let user_company = await dynamoDbLib.call("scan", {
              TableName: process.env.objectsTableName,
              FilterExpression: "objectType = :objectType and objectId = :objectId",
              ExpressionAttributeValues: {
                ":objectType": 'company',
                ":objectId": compaines_ids[x],
              }
            });

            compaines.push({
              company_id: user_company.Items[0].objectId,
              company: user_company.Items[0]
            })
          }

          for (var x in delivery_points_ids) {

            let DeliveryPointsParams = {
              TableName: process.env.objectsTableName,
              FilterExpression: "objectType = :objectType and objectId = :objectId",
              ExpressionAttributeValues: {
                ":objectType": 'delivery_point',
                ":objectId": delivery_points_ids[x],
              }
            };

            let delivery_point = await dynamoDbLib.call("scan", DeliveryPointsParams);
            delivery_points.push({
              delivery_point_id: delivery_points_ids[x],
              delivery_point: delivery_point.Items[0]
            })

          }


          let shop_categories = await dynamoDbLib.call("scan", {
            TableName: process.env.objectsTableName,
            FilterExpression: "objectType = :objectType and objectShopId = :objectShopId",
            ExpressionAttributeValues: {
              ":objectType": 'category',
              ":objectShopId": shop_id
            }
          });

          let shop_products = await dynamoDbLib.call("scan", {
            TableName: process.env.objectsTableName,
            FilterExpression: "objectType = :objectType and objectShopId = :objectShopId",
            ExpressionAttributeValues: {
              ":objectType": 'product',
              ":objectShopId": shop_id
            }
          });


          for (var x in orders.Items) {

            for (var y in orders.Items[x].items) {

              for (var z in shop_products.Items) {
                if (orders.Items[x].items[y].id == shop_products.Items[z].objectId) {

                  for (var k in shop_categories.Items) {
                    if (shop_categories.Items[k].objectId == shop_products.Items[z].objectCategory) {
                      orders.Items[x].items[y].category = shop_categories.Items[k].objectName
                      orders.Items[x].items[y].name = shop_products.Items[z].objectName
                    }
                  }

                }
              }

            }

            for (var y in users) {


              let company_id = null

              if (orders.Items[x].userId == users[y].user_id) {
                orders.Items[x].user = users[y].user
                for (var z in users[y].user.Attributes) {

                  if (users[y].user.Attributes[z].Name == 'name') {
                    company_id = users[y].user.Attributes[z].Value
                    for (var k in compaines) {
                      if (company_id === compaines[k].company_id && company_id == orders.Items[x].companyId) {
                        orders.Items[x].user_company = compaines[k].company
                      }
                    }
                  }
                }
              }

              if (orders.Items[x].on_behalf_of) {

                if (orders.Items[x].on_behalf_of == users[y].user_id) {

                  orders.Items[x].on_behalf_of = users[y].user
                  for (var z in users[y].user.Attributes) {

                    if (users[y].user.Attributes[z].Name == 'name') {
                      company_id = users[y].user.Attributes[z].Value
                      for (var k in compaines) {
                        if (company_id == compaines[k].company_id && company_id == orders.Items[x].companyId) {
                          orders.Items[x].user_company = compaines[k].company
                        }
                      }
                    }
                  }
                }
              }

              if (orders.Items[x].canceledBy) {

                if (orders.Items[x].canceledBy == users[y].user_id) {

                  orders.Items[x].canceledBy = users[y].user
                  for (var z in users[y].user.UserAttributes) {

                    if (users[y].user.UserAttributes[z].Name == 'custom:company_id') {
                      company_id = users[y].user.UserAttributes[z].Value
                      for (var k in compaines) {
                        if (company_id == compaines[k].company_id) {
                          orders.Items[x].user_company = compaines[k].company
                        }
                      }
                    }
                  }
                }
              }
            }


            for (var y in delivery_points) {
              if (delivery_points[y].delivery_point_id == orders.Items[x].delivery_point) {
                orders.Items[x].delivery_point = delivery_points[y].delivery_point
              }
            }

          }

          let final_orders = []

          if (customers.length > 0) {
            for (var y in customers) {
              for (var x in orders.Items) {

                if (orders.Items[x].companyId === customers[y]) {
                  final_orders.push(orders.Items[x])
                }

              }

            }

          } else {
            final_orders = orders.Items
          }

          let final_orders_2 = []

          if (products.length > 0) {
            for (var x in final_orders) {
              let in_order = false
              for (var y in final_orders[x].items) {
                for (var z in products) {
                  if (final_orders[x].items[y].id == products[z]) {
                    in_order = true
                  }
                }

              }

              if (in_order) {
                final_orders_2.push(final_orders[x])
              }

            }
          } else {
            final_orders_2 = final_orders
          }

          resultOrders = resultOrders.concat(final_orders_2);

        } else {

          callback(null, failure({
            status: 'you do not have access to this shop'
          }));
        }

      }

      let total_orders = resultOrders.length
      let total_products = 0
      let total_customers = 0
      let customers_list = []

      for (var x in resultOrders) {
        for (var y in resultOrders[x].items) {
          total_products += parseInt(resultOrders[x].items[y].quantity)
        }
        customers_list.push(resultOrders[x].companyId)
      }

      customers_list = unique(customers_list)
      total_customers = customers_list.length

      resultOrders = arraySort(resultOrders, 'delivery_date')
      callback(null, success({
        orders: resultOrders,
        total_orders: total_orders,
        total_products: total_products,
        total_customers: total_customers
      }));



    } else {

      callback(null, failure({
        status: 'you do not have access to this api call'
      }));

    }
  } catch (e) {
    bugsnagClient.notify(user_id, e)
    callback(null, failure({
      status: e
    }));
  }
}

function inArray(needle, haystack) {
  var length = haystack.length;
  for (var i = 0; i < length; i++) {
    if (haystack[i] == needle) return true;
  }
  return false;
}