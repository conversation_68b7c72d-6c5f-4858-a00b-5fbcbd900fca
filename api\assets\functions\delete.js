import * as dynamoDbLib from "../../../libs/dynamodb-lib";
import * as bugsnagClient from "../../../libs/bugsnag";
import {
  success,
  failure
} from "../../../libs/response-lib";
import {
  is_level_permitted
} from "../../../libs/permissions";

export async function asset(event, context, callback) {

  try {

    const access_scope = ['owner', 'manager']
    var user_id = event.requestContext.identity.cognitoAuthenticationProvider.split('CognitoSignIn:')[1]

    const permissions = await is_level_permitted(user_id, access_scope);
    if (permissions.level_allowed) {

      const params = {
        TableName: process.env.objectsTableName,

        Key: {
          objectCompanyId: permissions.user_company_id,
          objectId: event.pathParameters.id
        }
      };

      const result = await dynamoDbLib.call("delete", params);
      callback(null, success({
        status: true
      }));


    } else {

      callback(null, failure({
        status: 'you do not have access to this api call'
      }));

    }
  } catch (e) {
    bugsnagClient.notify(user_id, e)
    callback(null, failure({
      status: false
    }));
  }
}