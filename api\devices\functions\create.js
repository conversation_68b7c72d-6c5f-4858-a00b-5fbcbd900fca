import uuid from "uuid";
import * as dynamoDbLib from "../../../libs/dynamodb-lib";
import * as bugsnagClient from "../../../libs/bugsnag";

import {
  success,
  failure
} from "../../../libs/response-lib";

import {
  is_level_permitted
} from "../../../libs/permissions";

export async function device(event, context, callback) {

  try {

    const access_scope = ['owner', 'manager', 'employee']

    const data = JSON.parse(event.body);
    var user_id = event.requestContext.identity.cognitoAuthenticationProvider.split('CognitoSignIn:')[1]

    const permissions = await is_level_permitted(user_id, access_scope);
    if (permissions.level_allowed) {

      // create the device
      var new_device_id = uuid.v1()

      const deviceParams = {
        TableName: process.env.objectsTableName,
        Item: {
          objectCreatedBy: user_id,
          objectId: new_device_id,
          objectName: data.objectName,
          objectCompanyId: permissions.user_company_id,
          objectParent: data.objectParent,
          objectKind: data.objectKind,
          objectType: "device",
          createdAt: new Date().getTime()
        }
      };

      await dynamoDbLib.call("put", deviceParams);

      // then attch a sensor for it.
      const sensorParam = {
        TableName: process.env.objectsTableName,
        Item: {
          objectCreatedBy: user_id,
          objectId: uuid.v1(),
          objectParent: new_device_id,
          objectCompanyId: permissions.user_company_id,
          objectDataId: null,
          objectType: "sensor",
          createdAt: new Date().getTime()
        }
      };

      await dynamoDbLib.call("put", sensorParam);
      callback(null, success(deviceParams.Item));

    } else {

      callback(null, failure({
        status: 'you do not have access to this api call'
      }));

    }

  } catch (e) {
    bugsnagClient.notify(user_id, e)
    callback(null, failure({
      status: false
    }));
  }
}