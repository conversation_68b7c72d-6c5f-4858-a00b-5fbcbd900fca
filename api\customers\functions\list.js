import {
  success,
  failure
} from "../../../libs/response-lib";

import {
  is_level_permitted
} from "../../../libs/permissions";

import * as dynamoDbLib from "../../../libs/dynamodb-lib";
import * as bugsnagClient from "../../../libs/bugsnag";
const AWS = require('aws-sdk')
const cognito_client = new AWS.CognitoIdentityServiceProvider()
var arraySort = require('array-sort')

export async function customers(event, context, callback) {


  try {

    let access_scope = ['owner', 'manager', 'employee']
    let user_id = event.requestContext.identity.cognitoAuthenticationProvider.split('CognitoSignIn:')[1]

    const permissions = await is_level_permitted(user_id, access_scope);
    if (permissions.level_allowed) {

      let shop_id = event.pathParameters.id
      let params = {}


      if (shop_id != 'null') {

        params = {
          TableName: process.env.userPermissionsTableName,
          FilterExpression: "grantedBy = :grantedBy and roleType = :roleType and isActive = :isActive and shopId = :shopId",
          ExpressionAttributeValues: {
            ":grantedBy": permissions.user_company_id,
            ":roleType": 'shop_access_role',
            ":isActive": true,
            ":shopId": shop_id
          }
        };

      } else {

        params = {
          TableName: process.env.userPermissionsTableName,
          FilterExpression: "grantedBy = :grantedBy and roleType = :roleType and isActive = :isActive",
          ExpressionAttributeValues: {
            ":grantedBy": permissions.user_company_id,
            ":roleType": 'shop_access_role',
            ":isActive": true
          }
        };

      }

      let shops_permissions = await dynamoDbLib.call("scan", params)
      let customers = []

      let pool_id = process.env.COGNITO_POOL_ID
      let congnito_users = []

      let user_pool = await cognito_client.describeUserPool({
        UserPoolId: pool_id
      }).promise()

      let number_of_users = user_pool.UserPool.EstimatedNumberOfUsers

      let loops = Math.ceil(number_of_users / 60)

      let token = ''

      for (var i = 1; i <= loops; i++) {

        let ownersParams = {};

        if (token != '') {
          ownersParams = {
            UserPoolId: pool_id,
            AttributesToGet: null,
            Limit: 0,
            PaginationToken: token
          }
        } else {
          ownersParams = {
            UserPoolId: pool_id,
            AttributesToGet: null,
            Limit: 0
          }
        }

        let users_data = await cognito_client.listUsers(ownersParams).promise()
        token = users_data.PaginationToken

        for (var x in users_data.Users) {
          congnito_users.push(users_data.Users[x])
        }


      }

      for (var x in shops_permissions.Items) {

        let customer = await dynamoDbLib.call("scan", {
          TableName: process.env.objectsTableName,
          FilterExpression: "objectType = :objectType and objectId = :objectId",
          ExpressionAttributeValues: {
            ":objectType": 'company',
            ":objectId": shops_permissions.Items[x].companyId
          }
        })


        let users = []
        let contact = {}

        for (var z in congnito_users) {

          let user_type = ''
          let user_company_id = ''
          let email_verified = false
          let status = congnito_users[z].UserStatus

          for (var y in congnito_users[z].Attributes) {

            if (congnito_users[z].Attributes[y].Name == 'custom:user_type') {
              user_type = congnito_users[z].Attributes[y].Value
            }

            if (congnito_users[z].Attributes[y].Name == 'custom:company_id') {
              user_company_id = congnito_users[z].Attributes[y].Value
            }

            if (congnito_users[z].Attributes[y].Name == 'email_verified') {
              email_verified = congnito_users[z].Attributes[y].Value
            }

          }


          if ((user_type == 'owner' && user_company_id == shops_permissions.Items[x].companyId) || shops_permissions.Items[x].contactId && user_company_id == shops_permissions.Items[x].companyId) {

            let first_name = ''
            let last_name = ''
            let email = ''
            let phone = ''


            for (var y in congnito_users[z].Attributes) {

              if (congnito_users[z].Attributes[y].Name == 'custom:first_name') {
                first_name = congnito_users[z].Attributes[y].Value
              }
              if (congnito_users[z].Attributes[y].Name == 'custom:last_name') {
                last_name = congnito_users[z].Attributes[y].Value
              }
              if (congnito_users[z].Attributes[y].Name == 'custom:user_phone') {
                phone = congnito_users[z].Attributes[y].Value
              }
              if (congnito_users[z].Attributes[y].Name == 'email') {
                email = congnito_users[z].Attributes[y].Value
              }
              if (congnito_users[z].Attributes[y].Name == 'email_verified') {
                email_verified = congnito_users[z].Attributes[y].Value
              }

            }

            if (congnito_users[z].Username == shops_permissions.Items[x].contactId) {
              contact = {
                user_id: congnito_users[z].Username,
                first_name,
                last_name,
                phone,
                email,
                status
              }
            } else {
              if (user_type == 'owner') {
                users.push({
                  first_name,
                  last_name,
                  phone,
                  email,
                  status
                })
              }
            }
          }
        }


        let discount = (shops_permissions.Items[x].discount) ? shops_permissions.Items[x].discount : 0
        let shipping = (shops_permissions.Items[x].shipping) ? shops_permissions.Items[x].shipping : null
        let poweroffice_customers_id = (shops_permissions.Items[x].powerofficeCustomerId) ? shops_permissions.Items[x].powerofficeCustomerId : null
        let allow_volume_orders = (shops_permissions.Items[x].allow_volume_orders) ? shops_permissions.Items[x].allow_volume_orders : false
        let allow_recurring_orders = (shops_permissions.Items[x].allow_recurring_orders) ? shops_permissions.Items[x].allow_recurring_orders : false
        let invoice_delivery_type = (shops_permissions.Items[x].invoice_delivery_type) ? shops_permissions.Items[x].invoice_delivery_type : 'mail'
        let allowed_payment_options = (shops_permissions.Items[x].allowed_payment_options) ? shops_permissions.Items[x].allowed_payment_options : []

        customer.Items[0].owners = users
        customer.Items[0].contact = contact
        customer.Items[0].discount = discount
        customer.Items[0].shipping = shipping
        customer.Items[0].poweroffice_customers_id = poweroffice_customers_id
        customer.Items[0].allow_volume_orders = allow_volume_orders
        customer.Items[0].allow_recurring_orders = allow_recurring_orders
        customer.Items[0].invoice_delivery_type = invoice_delivery_type
        customer.Items[0].allowed_payment_options = allowed_payment_options
        customers.push(customer.Items[0])
      }

      customers = removeDuplicates(customers, 'objectId')
      customers = arraySort(customers, 'objectName')
      callback(null, success(customers))


    } else {

      callback(null, failure({
        status: 'you do not have access to this api call'
      }));

    }

  } catch (e) {
    bugsnagClient.notify(user_id, e)
    callback(null, failure({
      status: e
    }));
  }

}

function removeDuplicates(myArr, prop) {
  return myArr.filter((obj, pos, arr) => {
    return arr.map(mapObj => mapObj[prop]).indexOf(obj[prop]) === pos;
  });
}