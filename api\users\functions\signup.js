const AWS = require('aws-sdk')
import uuid from "uuid";
import * as dynamoDbLib from "../../../libs/dynamodb-lib";

import {
  success,
  failure
} from "../../../libs/response-lib";

const SNS = new AWS.SNS();

export async function send(event, context, callback) {

  let cognito_client = new AWS.CognitoIdentityServiceProvider()
  let pool_id = process.env.COGNITO_POOL_ID
  let shop_prefix = ''
  let kompis_app_url = process.env.KOMPIS_APP_URL

  if (kompis_app_url == 'https://development.kompis.app') {
    shop_prefix = 'dev'
  }

  let user_id = event.request.userAttributes.sub
  let code = event.request.codeParameter


  if (event.userPoolId === pool_id) {

    let company_id = null
    let company_name = null
    let company_address = null
    let company_post_code = null
    let company_organization_id = null
    let company_city = null
    let created_as = null
    let plan = null
    let payment_method_id = null
    let payment_holder_name = null


    let user = await cognito_client.adminGetUser({
      UserPoolId: pool_id,
      Username: user_id
    }).promise()

    for (var x in user.UserAttributes) {

      if (user.UserAttributes[x].Name == 'custom:company_id') {
        company_id = user.UserAttributes[x].Value
      }
      if (user.UserAttributes[x].Name == 'custom:company_name') {
        company_name = user.UserAttributes[x].Value
      }


      if (user.UserAttributes[x].Name == 'address') {
        company_address = user.UserAttributes[x].Value
      }

      if (user.UserAttributes[x].Name == 'custom:city') {
        company_city = user.UserAttributes[x].Value
      }
      if (user.UserAttributes[x].Name == 'custom:created_as') {
        created_as = user.UserAttributes[x].Value
      }
      if (user.UserAttributes[x].Name == 'custom:post_code') {
        company_post_code = user.UserAttributes[x].Value
      }
      if (user.UserAttributes[x].Name == 'custom:organization_id') {
        company_organization_id = user.UserAttributes[x].Value
      }
      if (user.UserAttributes[x].Name == 'custom:plan') {
        plan = user.UserAttributes[x].Value
      }

      if (user.UserAttributes[x].Name == 'custom:payment_method') {
        payment_method_id = user.UserAttributes[x].Value
      }

      if (user.UserAttributes[x].Name == 'custom:payment_holder') {
        payment_holder_name = user.UserAttributes[x].Value
      }

    }


    if (event.triggerSource === "CustomMessage_AdminCreateUser" || event.triggerSource === "CustomMessage_ForgotPassword") {

      let user_company = await dynamoDbLib.call("scan", {
        TableName: process.env.objectsTableName,
        FilterExpression: "objectType = :objectType and objectId = :objectId",
        ExpressionAttributeValues: {
          ":objectType": 'company',
          ":objectId": (company_id) ? company_id : null,
        }
      });

      company_name = user_company.Items[0].objectName
      if (created_as == 'company_employee' && event.triggerSource === "CustomMessage_AdminCreateUser") {


        event.response.smsMessage = "Welcome to the service. Your confirmation code is " + code;
        event.response.emailSubject = "Bekreft din konto";
        event.response.emailMessage = '<p>Hei </p>Vi har invitert deg via din e-post adresse ' + event.request.usernameParameter + ' til Kompis app.</p><p><a href="' + kompis_app_url + '/auth/temporary-login/' + event.request.usernameParameter + '/' + event.request.codeParameter + '">Trykk på denne linken</a> og lag et unikt passord, så er du i gang!</p><p></p><p>Mvh</p><p>' + company_name + '</p>'

      }

      if (created_as == 'company_employee' && event.triggerSource === "CustomMessage_ForgotPassword") {

        event.response.smsMessage = "Welcome to the service. Your confirmation code is " + code;
        event.response.emailSubject = "Nytt passord for din Kompiskonto";
        event.response.emailMessage = '<p>Hei</p><p>Du har forespurt om et nytt passord. Stemmer dette?</p><p>Trykk på <a href="' + kompis_app_url + '/auth/complete-forget-password/' + event.request.usernameParameter + '/' + event.request.codeParameter + '">denne linken</a> for å sette ditt nye passord</p><p>Mvh</p><p>Kompis app</p>'
      }

      if (created_as == 'company_customer' || created_as == 'customer_employee') {

        let Premisssion = await dynamoDbLib.call("scan", {
          TableName: process.env.userPermissionsTableName,
          FilterExpression: "roleType = :roleType and companyId = :companyId and isActive = :isActive",
          ExpressionAttributeValues: {
            ":roleType": 'shop_access_role',
            ":companyId": company_id,
            ":isActive": true
          }
        });

        let last_permission = 0;
        let last_permission_index = -1;

        for (var x in Premisssion.Items) {

          if (Premisssion.Items[x].createdAt > last_permission) {
            last_permission = Premisssion.Items[x].createdAt
            last_permission_index = x
          }

        }

        let shop_id = Premisssion.Items[last_permission_index].shopId
        let invited_by_company_id = Premisssion.Items[last_permission_index].grantedBy

        let this_shop = await dynamoDbLib.call("scan", {
          TableName: process.env.objectsTableName,
          FilterExpression: "objectType = :objectType and objectId = :objectId",
          ExpressionAttributeValues: {
            ":objectType": 'shop',
            ":objectId": (shop_id) ? shop_id : null

          }
        });

        let shop_url = ''
        if (this_shop.Items[0].objectDomain) {
          shop_url = 'https://' + this_shop.Items[0].objectDomain
        } else {
          shop_url = 'https://' + shop_id + '.' + shop_prefix + 'shops.kompis.app'
        }

        let contact_us_page = shop_url + '/contact'

        let companyResult = await dynamoDbLib.call("scan", {
          TableName: process.env.objectsTableName,
          FilterExpression: "objectType = :objectType and objectCompanyId = :objectCompanyId",
          ExpressionAttributeValues: {
            ":objectType": 'company',
            ":objectCompanyId": invited_by_company_id
          }
        });
        let inviter_company = companyResult.Items[0]


        if (created_as == 'company_customer' && event.triggerSource === "CustomMessage_AdminCreateUser") {


          event.response.smsMessage = "Welcome to the service. Your confirmation code is " + code;
          event.response.emailSubject = "Bekreft din konto";
          event.response.emailMessage = '<p>Hei</p><p>Du er innvilget tilgang via e-post adresse ' + event.request.usernameParameter + ' til vår bestillingsportal <a href="' + shop_url + '">' + this_shop.Items[0].objectName + '</a>.</p><p>Alt du trenger å gjøre nå er å <a href = "' + shop_url + '/temporary-login/' + event.request.usernameParameter + '/' + event.request.codeParameter + '">trykke på denne linken</a> og lage et unikt passord, så er du i gang!</p><p></p><p>Mhv</p><p>' + inviter_company.objectName + '</p>'

        }


        if (created_as == 'customer_employee' && event.triggerSource === "CustomMessage_AdminCreateUser") {

          event.response.smsMessage = "Welcome to the service. Your confirmation code is " + code;
          event.response.emailSubject = "Bekreft din konto";
          event.response.emailMessage = '<p>Hei </p><p>Du er innvilget tilgang med e-post adresse ' + event.request.usernameParameter + ' til bestillingsportalen <a href="' + shop_url + '">' + this_shop.Items[0].objectName + '</a> på vegne av Kunde av ' + company_name + '.</p><p>Alt du trenger å gjøre nå er å <a href="' + shop_url + '/temporary-login/' + event.request.usernameParameter + '/' + event.request.codeParameter + '">trykke på denne linken</a> og lage et unikt passord, så er du i gang!</p><p></p><p>Mhv</p><p>' + inviter_company.objectName + '</p>'
        }

        if ((created_as == 'company_customer' || created_as == 'customer_employee') && event.triggerSource === "CustomMessage_ForgotPassword") {

          event.response.smsMessage = "Welcome to the service. Your confirmation code is " + code;
          event.response.emailSubject = "Nytt passord for din Kompiskonto";
          event.response.emailMessage = '<p>Hei</p><p>Du har forespurt om et nytt passord. Stemmer dette?</p><p>Trykk på <a href="' + shop_url + '/complete-forget-password/' + user_id + '/' + event.request.codeParameter + '">denne linken</a> for å sette ditt nye passord</p><p>Mvh</p><p>' + this_shop.Items[0].objectName + '</p>'

        }

      }

    }

    // this is for creating a new comapny account from the main landing page.
    if (created_as == null && (event.triggerSource === "CustomMessage_SignUp" || event.triggerSource === "CustomMessage_ForgotPassword")) {

      if (event.triggerSource === "CustomMessage_SignUp") {
        event.response.smsMessage = "Welcome to the service. Your confirmation code is " + code;
        event.response.emailSubject = "Bekreft din konto";
        event.response.emailMessage = '<p>Velkommen!</p><p>Det skal være lett å bli kompis med oss :)</p><p>Trykk på denne <a href="' + kompis_app_url + '/auth/confirm/' + user_id + '/' + event.request.codeParameter + '">linken</a>. for å komme igang med appen. Vanskeligere er det ikke!</p><p>Har du spørsmål eller kommentarer?</p><p> Ta kontakt med Adrian på <EMAIL></p>';
      }

      if (event.triggerSource === "CustomMessage_ForgotPassword") {

        event.response.smsMessage = "Welcome to the service. Your confirmation code is " + code;
        event.response.emailSubject = "Nytt passord for din Kompiskonto";
        event.response.emailMessage = '<p>Hei</p><p>Du har forespurt om et nytt passord. Stemmer dette?</p><p>Trykk på <a href="' + kompis_app_url + '/auth/complete-forget-password/' + user_id + '/' + event.request.codeParameter + '">denne linken</a> for å sette ditt nye passord</p><p>Mvh</p><p>Kompis app</p>'
      }

      if (company_id == null) {

        company_id = uuid.v1()

        const companyParams = {
          TableName: process.env.objectsTableName,
          Item: {
            objectCreatedBy: user_id,
            objectCompanyId: company_id,
            objectId: company_id,
            objectName: company_name,
            objectAddress: company_address,
            objectPostCode: company_post_code,
            objectCity: company_city,
            objectOrganizationId: company_organization_id,
            objectPlan: plan,
            objectType: "company",
            createdAt: new Date().getTime()
          }
        };

        await dynamoDbLib.call("put", companyParams);


        let skey = process.env.STRIPE_PUBLISHABLE_KEY
        let stripe_integration_id = process.env.STRIPE_INTEGRATION_ID

        const stripe = require('stripe')(skey);

        const customer = await stripe.customers.create({
          payment_method: payment_method_id,
          name: company_name,
        });

        await dynamoDbLib.call("put", {
          TableName: process.env.objectsTableName,
          Item: {
            objectId: uuid.v1(),
            objectCreatedBy: user_id,
            objectCompanyId: company_id,
            objectName: company_name,
            objectPaymentMethodId: payment_method_id,
            objectPaymentMethodHolder: payment_holder_name,
            objectStripeCustomerId: customer.id,
            objectIsDefault: false,
            objectIntegrationId: stripe_integration_id,
            objectType: "payment_method",
            createdAt: new Date().getTime()
          }
        });


        await cognito_client.adminUpdateUserAttributes({
          UserAttributes: [{
              Name: 'custom:company_id',
              Value: company_id
            },
            {
              Name: 'name',
              Value: company_id
            }
          ],
          UserPoolId: pool_id,
          Username: user_id
        }).promise()

        await cognito_client.adminAddUserToGroup({
          GroupName: 'Owner',
          UserPoolId: pool_id,
          Username: user_id
        }).promise()


        let location_id = uuid.v1()
        const locationParams = {
          TableName: process.env.objectsTableName,
          Item: {
            objectCreatedBy: user_id,
            objectCompanyId: company_id,
            objectId: location_id,
            objectName: company_name,
            objectLocation: company_address,
            objectType: "asset",
            createdAt: new Date().getTime()
          }
        };

        await dynamoDbLib.call("put", locationParams);


        const locationPermissionParams = {

          TableName: process.env.userPermissionsTableName,
          Item: {
            grantedBy: 'signup',
            userId: user_id,
            companyId: company_id,
            locationId: location_id,
            permissionId: uuid.v1(),
            createdAt: new Date().getTime()
          }
        };

        await dynamoDbLib.call("put", locationPermissionParams);

        //create sample refrigerator device

        let new_device_id1 = uuid.v1()

        const deviceParams1 = {
          TableName: process.env.objectsTableName,
          Item: {
            objectCreatedBy: user_id,
            objectId: new_device_id1,
            objectName: 'Refrigerator',
            objectCompanyId: company_id,
            objectParent: location_id,
            objectKind: 'refrigerator',
            objectType: "device",
            createdAt: new Date().getTime()
          }
        };

        await dynamoDbLib.call("put", deviceParams1);

        // then attch a sensor for it.
        const sensorParam1 = {
          TableName: process.env.objectsTableName,
          Item: {
            objectCreatedBy: user_id,
            objectId: uuid.v1(),
            objectParent: new_device_id1,
            objectCompanyId: company_id,
            objectDataId: null,
            objectType: "sensor",
            createdAt: new Date().getTime()
          }
        };

        await dynamoDbLib.call("put", sensorParam1);


        //create sample freezer device

        let new_device_id2 = uuid.v1()

        const deviceParams2 = {
          TableName: process.env.objectsTableName,
          Item: {
            objectCreatedBy: user_id,
            objectId: new_device_id2,
            objectName: 'Freezer',
            objectCompanyId: company_id,
            objectParent: location_id,
            objectKind: 'freezer',
            objectType: "device",
            createdAt: new Date().getTime()
          }
        };

        await dynamoDbLib.call("put", deviceParams2);

        // then attch a sensor for it.
        const sensorParam2 = {
          TableName: process.env.objectsTableName,
          Item: {
            objectCreatedBy: user_id,
            objectId: uuid.v1(),
            objectParent: new_device_id2,
            objectCompanyId: company_id,
            objectDataId: null,
            objectType: "sensor",
            createdAt: new Date().getTime()
          }
        };

        await dynamoDbLib.call("put", sensorParam2);

        /* hidden last update

        //create sample daily clean task.
        let task_id = uuid.v1()

        const taskParams = {

          TableName: process.env.tasksTableName,
          Item: {
            taskId: task_id,
            assignerId: user_id,
            assignees: 'anyone',
            taskName: 'the the kitchen floor',
            taskType: 'clean',
            taskDesc: 'Sample task created on account setup for cleaning the kitchen floor.',
            taskRepeat: 'daily',
            locationId: location_id,
            companyId: company_id,
            taskStatus: 'open',
            taskDates: [],
            taskTime: '20:00:00',
            createdAt: new Date().getTime()
          }
        };


        await dynamoDbLib.call("put", taskParams);

        await SNS.publish({
          Message: task_id,
          TopicArn: process.env.generateCompaniesTasksSNSTopic
        }, function(err, data) {
          if (err) {
            //  callback(null, err);
          }
          // sns sent. good job
        });

        */


        let access_type = 'company'

        // create permissions.

        let permissionParams = {

          TableName: process.env.userPermissionsTableName,
          Item: {
            grantedBy: 'signup',
            companyId: company_id,
            roleType: 'access_role',
            accessType: access_type,
            isActive: true,
            permissionId: uuid.v1(),
            createdAt: new Date().getTime()
          }
        };

        await dynamoDbLib.call("put", permissionParams);

        // create shop.
        let demo_shop_id = uuid.v1()
        let shopParams = {
          TableName: process.env.objectsTableName,
          Item: {
            objectCreatedBy: user_id,
            objectCompanyId: company_id,
            objectId: demo_shop_id,
            objectName: company_name,
            objectType: "shop",
            createdAt: new Date().getTime()
          }
        };

        //await dynamoDbLib.call("put", shopParams);

        // create shop test category
        /*
        await dynamoDbLib.call("put", {
          TableName: process.env.objectsTableName,
          Item: {
            objectCreatedBy: user_id,
            objectCompanyId: company_id,
            objectId: uuid.v1(),
            objectName: 'Test',
            objectShopId: demo_shop_id,
            objectType: "category",
            objectStatus: 'hidden',
            createdAt: new Date().getTime()
          }
        });
        */


      }

    }
  }
  callback(null, event);
}