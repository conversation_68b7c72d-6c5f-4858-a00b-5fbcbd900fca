import uuid from "uuid";
import * as dynamoDbLib from "../../../libs/dynamodb-lib";
import * as bugsnagClient from "../../../libs/bugsnag";

import {
  success,
  failure
} from "../../../libs/response-lib";

import {
  is_level_permitted
} from "../../../libs/permissions";

import {
  log_event
} from "../../../libs/logs";


var arraySort = require('array-sort')
const groupArray = require('group-array');

const AWS = require('aws-sdk')
const SNS = new AWS.SNS();
import PowerOffice from "../../../libs/poweroffice";

export async function cart(event, context, callback) {


  try {

    const access_scope = ['owner', 'manager', 'employee']
    const data = JSON.parse(event.body);


    var user_id = event.requestContext.identity.cognitoAuthenticationProvider.split('CognitoSignIn:')[1]
    //var user_id = '758725ed-6f81-44e2-b07a-c8fcafe2bb56'

    const permissions = await is_level_permitted(user_id, access_scope);

    let shop_id = event.pathParameters.id
    let created_orders = []
    let canceled_orders = []
    //let shop_id = 'e3336010-c455-11e9-9ab0-7fc92b1e522d'


    const shopInfo = await dynamoDbLib.call("scan", {
      TableName: process.env.objectsTableName,
      FilterExpression: "objectType = :objectType and objectId = :objectId",
      ExpressionAttributeValues: {
        ":objectType": 'shop',
        ":objectId": shop_id
      }
    });

    const company = await dynamoDbLib.call("scan", {
      TableName: process.env.objectsTableName,
      FilterExpression: "objectType = :objectType and objectId = :objectId",
      ExpressionAttributeValues: {
        ":objectType": 'company',
        ":objectId": shopInfo.Items[0].objectCompanyId
      }
    });

    let shipping_tax = (company.Items[0].objectTax) ? (company.Items[0].objectTax.shipping / 100) : 0

    let is_shop_owner = false


    if (shopInfo.Items[0].objectCompanyId == permissions.user_company_id) {
      is_shop_owner = true
    }



    let shop_name = shopInfo.Items[0].objectName
    let shop_currency = (shopInfo.Items[0].shopCurrency) ? shopInfo.Items[0].shopCurrency : 'NOK'
    let shipping_cost = (shopInfo.Items[0].shippingCost) ? shopInfo.Items[0].shippingCost : 0
    let order_deadline = (shopInfo.Items[0].orderDeadline) ? shopInfo.Items[0].orderDeadline : 0
    let order_deadline_time = (shopInfo.Items[0].orderDeadlineTime) ? shopInfo.Items[0].orderDeadlineTime : 23
    let delivery_frequency = shopInfo.Items[0].deliveryFrequency
    let order_dates = (shopInfo.Items[0].orderDates) ? shopInfo.Items[0].orderDates : []
    let new_order_notification = (shopInfo.Items[0].newOrderNotification) ? shopInfo.Items[0].newOrderNotification : false

    const shopProducts = await dynamoDbLib.call("scan", {
      TableName: process.env.objectsTableName,
      FilterExpression: "objectType = :objectType and objectShopId = :objectShopId",
      ExpressionAttributeValues: {
        ":objectType": 'product',
        ":objectShopId": shop_id
      }
    });

    if (permissions.level_allowed || is_shop_owner) {

      let deliveryPointPermissionsParams = {
        TableName: process.env.userPermissionsTableName,
        FilterExpression: "userId = :userId and locationId = :locationId",
        ExpressionAttributeValues: {
          ":userId": permissions.user_company_id,
          ":locationId": data.delivery_point
        }
      };

      const deliveryPointsPermission = await dynamoDbLib.call("scan", deliveryPointPermissionsParams);

      if (deliveryPointsPermission.Items.length > 0 || is_shop_owner) {


        let customer_company_id = (data.company_id != null) ? data.company_id : permissions.user_company_id

        const customer_permission = await dynamoDbLib.call("scan", {
          TableName: process.env.userPermissionsTableName,
          FilterExpression: "shopId = :shopId and roleType = :roleType and isActive = :isActive and companyId = :companyId",
          ExpressionAttributeValues: {
            ":shopId": shop_id,
            ":roleType": 'shop_access_role',
            ":isActive": true,
            ":companyId": customer_company_id
          }
        });

        let discount = (customer_permission.Items[0].discount) ? parseFloat(customer_permission.Items[0].discount) : 0
        let shipping = (customer_permission.Items[0].shipping) ? customer_permission.Items[0].shipping : 0
        let orders_sheet = data.orders_sheet
        let delivery_time = data.delivery_time
        let comment = data.comment


        let initial_orders = []



        for (var x in orders_sheet) {

          let product_id = orders_sheet[x]['Product ID']
          let product_name = orders_sheet[x]['Product Name']

          for (var y in orders_sheet[x]) {

            let notlist = ["Product ID", "Product Name", "Product Category", "Min Order Quantity", "Max Order Quantity"]

            if ((!notlist.includes(y))) {

              let date = new Date(y);


              let t_delivery_time = ''

              if (delivery_time) {

                t_delivery_time = delivery_time.split(":");
                date.setHours(t_delivery_time[0], t_delivery_time[1], 0, 0);

              }

              if ((date instanceof Date) && (!isNaN(date.valueOf())) && (!isNaN(parseInt(orders_sheet[x][y])))) {

                initial_orders.push({
                  order_date_time: date.getTime(),
                  product_id: product_id,
                  product_name: product_name,
                  quantity: parseInt(orders_sheet[x][y])
                })

              }

            }


          }
        }


        initial_orders = arraySort(initial_orders, 'order_date_time')
        initial_orders = groupArray(initial_orders, 'order_date_time')


        let errors = []
        let from = 9999999999999999999999999999
        let to = 0

        for (var x in initial_orders) {

          let now = new Date();
          let order_date = new Date(+x);


          if (parseInt(x) < from) {
            from = parseInt(x)
          }
          if (parseInt(x) > to) {
            to = parseInt(x)
          }

          let order_deadline_date = new Date();
          order_deadline_date.setDate(now.getDate() + parseInt(order_deadline));

          if ((order_deadline > 0 && order_date.getTime() < (order_deadline_date.getTime() - 2 * 60 * 60 * 1000)) && !is_shop_owner) {

            errors.push({
              error_type: 'order_deadline',
              order_date: (order_date.getFullYear() + '-' + (order_date.getMonth() + 1) + '-' + order_date.getDate()),
              order_deadline_time: order_deadline,
              x: order_date.getTime(),
              y: order_deadline_date.getTime()
            })

          }

          if (
            ((now.getFullYear() + '-' + (now.getMonth() + 1) + '-' + now.getDate()) == (order_date.getFullYear() + '-' + (order_date.getMonth() + 1) + '-' + order_date.getDate()) &&
              parseInt(order_deadline_time) < now.getHours() && !is_shop_owner)
          ) {

            errors.push({
              error_type: 'order_deadline_time',
              order_date: (order_date.getFullYear() + '-' + (order_date.getMonth() + 1) + '-' + order_date.getDate()),
              order_deadline_time: order_deadline_time
            })

          }

          if ((delivery_frequency == 'weekly' && !order_dates.includes(order_date.getDay().toString())) && !is_shop_owner) {
            errors.push({
              error_type: 'not_allowed_date',
              order_date: (order_date.getFullYear() + '-' + (order_date.getMonth() + 1) + '-' + order_date.getDate()),
              order_deadline_time: order_deadline
            })
          }


          from = new Date(from)
          from.setHours(0, 0, 0, 0);
          from = from.getTime()
          to = new Date(to)
          to.setHours(23, 59, 59, 0);
          to = to.getTime()

          for (var y in initial_orders[x]) {

            for (var z in shopProducts.Items) {

              if (shopProducts.Items[z].objectId == initial_orders[x][y].product_id) {

                let min = (shopProducts.Items[z].objectMinimumOrderQuantity) ? shopProducts.Items[z].objectMinimumOrderQuantity : 0
                let max = (shopProducts.Items[z].objectMaximumOrderQuantity) ? shopProducts.Items[z].objectMaximumOrderQuantity : 1000

                if (initial_orders[x][y].quantity < min && initial_orders[x][y].quantity > 0) {
                  errors.push({
                    error_type: 'minimum_order_quantity',
                    order_date: (order_date.getFullYear() + '-' + (order_date.getMonth() + 1) + '-' + order_date.getDate()),
                    minimum_order_quantity: min,
                    product_name: initial_orders[x][y].product_name,
                    ordered_quantity: initial_orders[x][y].quantity
                  })
                }

                if (initial_orders[x][y].quantity > max && initial_orders[x][y].quantity > 0) {
                  errors.push({
                    error_type: 'maximum_order_quantity',
                    order_date: (order_date.getFullYear() + '-' + (order_date.getMonth() + 1) + '-' + order_date.getDate()),
                    minimum_order_quantity: max,
                    product_name: initial_orders[x][y].product_name,
                    ordered_quantity: initial_orders[x][y].quantity
                  })
                }

              }

            }

          }



        }

        from = (from - 7200000)
        to = to + 7200000


        if (errors.length > 0) {
          callback(null, success({
            errors: errors
          }));
        } else {

          // first we gonna cancel the current orders

          let shops_filter_expression_values = {
            ":date1": from,
            ":date2": to,
            ":schedule_type": 'once',
            ":shopId": shop_id,
            ":orderStatus": 'canceled',
            ":companyId": (data.company_id != null) ? data.company_id : permissions.user_company_id,
          }

          let orders = await dynamoDbLib.call("scan", {
            TableName: process.env.cartTableName,
            FilterExpression: "companyId = :companyId and delivery_date >= :date1 and orderStatus <> :orderStatus and shopId = :shopId and delivery_date <= :date2 and schedule_type = :schedule_type ",
            ExpressionAttributeValues: shops_filter_expression_values
          });

          for (var x in orders.Items) {

            canceled_orders.push(orders.Items[x].cartId)

            await log_event(
              user_id,
              permissions.user_full_name,
              permissions.user_company_name,
              shopInfo.Items[0].objectCompanyId,
              "order_canceled", {
                cartId: orders.Items[x].cartId,
                orderType: 'single',
                created_from: 'volume_order'
              },
              shop_id,
              shopInfo.Items[0].objectName
            )

            await dynamoDbLib.call("update", {
              TableName: process.env.cartTableName,

              Key: {
                cartId: orders.Items[x].cartId
              },

              UpdateExpression: "SET powerOfficeInvoiceId = :powerOfficeInvoiceId, powerOfficeInvoiceIdentifier = :powerOfficeInvoiceIdentifier, orderStatus = :orderStatus, canceledAt = :canceledAt, canceledBy = :canceledBy",
              ExpressionAttributeValues: {
                ":orderStatus": "canceled",
                ":powerOfficeInvoiceId": null,
                ":powerOfficeInvoiceIdentifier": null,
                ":canceledBy": user_id,
                ":canceledAt": new Date().getTime(),
              },
              ReturnValues: "ALL_NEW"
            });

            // delete from POGO

            if (orders.Items[x].powerOfficeInvoiceIdentifier) {

              if (shopInfo.Items[0].objectPowerOfficeIntegration) {

                const integrationParams = {
                  TableName: process.env.integrationsTableName,
                  FilterExpression: "integrationId = :integrationId",
                  ExpressionAttributeValues: {
                    ":integrationId": shopInfo.Items[0].objectPowerOfficeIntegration
                  }
                };

                const integrations = await dynamoDbLib.call("scan", integrationParams);
                let poweroffice_client_key = integrations.Items[0].integrationPOAppKey
                let poweroffice_secrect_key = integrations.Items[0].integrationPOClientKey

                let poweroffice = await new PowerOffice(process.env.POWEROFFICE_APP_KEY, poweroffice_secrect_key)

                await poweroffice.deleteInvoice(orders.Items[x].powerOfficeInvoiceIdentifier)

              }
            }


          }


          // then we gonna create the new orders


          let volume_order_total_value_mva = 0
          let volume_order_total_products = 0

          for (var x in initial_orders) {

            let items = []
            for (var y in initial_orders[x]) {



              for (var z in shopProducts.Items) {

                if (shopProducts.Items[z].objectId == initial_orders[x][y].product_id && parseInt(initial_orders[x][y].quantity) > 0) {

                  items.push({
                    "id": shopProducts.Items[z].objectId,
                    "moq": shopProducts.Items[z].objectMinimumOrderQuantity,
                    "mxoq": shopProducts.Items[z].objectMaximumOrderQuantity,
                    "name": shopProducts.Items[z].objectName,
                    "price": shopProducts.Items[z].objectPrice,
                    "discount": (shopProducts.Items[z].objectMaximumDiscount) ? parseFloat(shopProducts.Items[z].objectMaximumDiscount) : 0,
                    "quantity": parseInt(initial_orders[x][y].quantity),
                    "mva": shopProducts.Items[z].objectMVA
                  })

                }
              }
            }

            let discount_value = 0
            let freight = (shipping) ? shipping : shipping_cost
            let total = 0
            let total_mva = 0
            let mva = 0


            for (var k in items) {


              let applied_discount = null
              let product_discount = (items[k].discount) ? items[k].discount : null
              if (product_discount != null) {
                if (discount >= product_discount) {
                  applied_discount = product_discount
                } else {
                  applied_discount = discount
                }
              } else {
                applied_discount = discount
              }

              items[k].discount = applied_discount

              total += (parseFloat(items[k].price) * items[k].quantity)
              total = parseFloat(total * (1 - parseFloat(applied_discount / 100)))
              total_mva += (parseFloat(items[k].price * (items[k].mva > 0 ? (parseFloat(items[k].mva) / 100 + 1) : 1)) * items[k].quantity) * ((applied_discount > 0) ? (1 - (applied_discount / 100)) : 1)
              discount_value += (parseFloat(items[k].price) * items[k].quantity) * ((applied_discount > 0) ? ((applied_discount / 100)) : 1)
              mva += (parseFloat(items[k].price * (items[k].mva > 0 ? (parseFloat(items[k].mva) / 100) : 1)) * items[k].quantity) * ((applied_discount > 0) ? (1 - (applied_discount / 100)) : 1)

              //total_mva = parseFloat(total_mva * (1 - parseFloat(applied_discount / 100)))

              //total_mva += ((parseFloat(shopProducts.Items[y].objectPrice * (shopProducts.Items[y].objectMVA > 0 ? (parseFloat(shopProducts.Items[y].objectMVA) / 100 + 1) : 1)) * data.items[x].quantity) * ((applied_discount > 0) ? (1 - (applied_discount / 100)) : 1))
              //mva += ((parseFloat(shopProducts.Items[y].objectPrice * (shopProducts.Items[y].objectMVA > 0 ? (parseFloat(shopProducts.Items[y].objectMVA) / 100) : 0)) * data.items[x].quantity) * ((applied_discount > 0) ? (1 - (applied_discount / 100)) : 1))
              //discount_value += ((parseFloat(shopProducts.Items[y].objectPrice * (shopProducts.Items[y].objectMVA > 0 ? (parseFloat(shopProducts.Items[y].objectMVA) / 100 + 1) : 1)) * data.items[x].quantity) * ((applied_discount > 0) ? ((applied_discount / 100)) : 1))


              //total += (parseFloat(items[k].price) * items[k].quantity)
              //total_mva += (parseFloat(items[k].price * (items[k].mva > 0 ? (parseFloat(items[k].mva) / 100 + 1) : 1)) * items[k].quantity)


              volume_order_total_products += items[k].quantity
            }

            //mva = total_mva - total
            if (discount > 0) {
              //  discount_value = total * (discount / 100)
              //  total = total - discount_value
              //  total_mva = total_mva - (total_mva * (discount / 100))
            }

            total += parseFloat(freight)
            total_mva += parseFloat(freight * (1 + shipping_tax))
            mva += parseFloat(freight * shipping_tax)


            let cart_id = uuid.v1()
            volume_order_total_value_mva += parseFloat(parseFloat(Math.round(total_mva * 100) / 100))

            if (items.length > 0) {

              await dynamoDbLib.call("put", {
                TableName: process.env.cartTableName,
                Item: {
                  cartId: cart_id,
                  userId: user_id,
                  discount: discount,

                  freight: freight,
                  total: parseFloat(Math.round(total_mva * 100) / 100).toFixed(2),
                  mva: mva,
                  discount_value: discount_value,
                  items: items,

                  shopId: shop_id,
                  schedule_dates: [],
                  schedule_type: 'once',
                  orderStatus: 'pending',
                  paymentStatus: 'unpaid',
                  paymentMethod: 'manual',
                  PaymentMethodId: 'manual',
                  companyId: (data.company_id != null) ? data.company_id : permissions.user_company_id,
                  delivery_date: parseInt(x),
                  delivery_point: data.delivery_point,
                  delivery_time: data.delivery_time,
                  on_behalf_of: data.customer_employee_id,
                  comment: (data.comment) ? data.comment : null,
                  tzo: (data.tzo) ? data.tzo : 0,
                  currency: shop_currency,
                  createdAt: new Date().getTime()
                }
              });

              created_orders.push(cart_id)

              await log_event(
                user_id,
                permissions.user_full_name,
                permissions.user_company_name,
                shopInfo.Items[0].objectCompanyId,
                "order_created", {
                  cartId: cart_id,
                  orderType: 'single',
                  created_from: 'volume_order'
                },
                shop_id,
                shopInfo.Items[0].objectName
              )

            }
          }

          let volume_cart_id = uuid.v1()

          if (volume_order_total_products > 0) {
            await dynamoDbLib.call("put", {
              TableName: process.env.cartTableName,
              Item: {
                cartId: volume_cart_id,
                userId: user_id,

                total: volume_order_total_value_mva,
                volume_order_total_products: volume_order_total_products,
                shopId: shop_id,
                schedule_dates: [],
                schedule_type: 'volume',
                orderStatus: 'pending',
                paymentStatus: 'unpaid',
                paymentMethod: 'manual',
                PaymentMethodId: 'manual',
                companyId: (data.company_id != null) ? data.company_id : permissions.user_company_id,
                delivery_date_start: from,
                delivery_date_end: to,
                delivery_point: data.delivery_point,
                delivery_time: data.delivery_time,
                on_behalf_of: data.customer_employee_id,
                comment: (data.comment) ? data.comment : null,
                tzo: (data.tzo) ? data.tzo : 0,
                canceled_orders: canceled_orders,
                created_orders: created_orders,
                currency: shop_currency,
                createdAt: new Date().getTime()
              }
            });


            await SNS.publish({
              Message: volume_cart_id,
              TopicArn: process.env.sendOrderConfirmationSecondaryCustomerSNSTopic
            }, function(err, data) {
              if (err) {
                callback(null, failure({
                  status: err
                }));
              }
              // sns sent. good job
            });

            if (new_order_notification) {
              await SNS.publish({
                Message: volume_cart_id,
                TopicArn: process.env.sendOrderConfirmationSecondaryPrimarySNSTopic
              }, function(err, data) {
                if (err) {
                  callback(null, failure({
                    status: err
                  }));
                }
                // sns sent. good job
              });
            }

            callback(null, success({
              volume_cart_id: volume_cart_id
            }));
          } else {

            callback(null, failure({
              status: 'your order is empty'
            }));

          }

        }


      } else {


        callback(null, failure({
          status: 'you do not have access to this delivery point'
        }));

      }

    } else {

      callback(null, failure({
        status: 'you do not have access to this api call'
      }));

    }

  } catch (e) {
    console.log(e);
    bugsnagClient.notify(user_id, e)
    callback(null, failure({
      status: e
    }));
  }

}