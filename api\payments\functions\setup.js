import uuid from "uuid";
import * as dynamoDbLib from "../../../libs/dynamodb-lib";
import * as bugsnagClient from "../../../libs/bugsnag";

import {
  success,
  failure
} from "../../../libs/response-lib";

import {
  is_level_permitted
} from "../../../libs/permissions";

export async function payment_method(event, context, callback) {

  try {
    const access_scope = ['owner', 'manager', 'employee']

    const data = JSON.parse(event.body);
    var user_id = event.requestContext.identity.cognitoAuthenticationProvider.split('CognitoSignIn:')[1]
    const permissions = await is_level_permitted(user_id, access_scope);
    if (permissions.level_allowed) {


      let session = null
      let shop_id = event.pathParameters.id

      let pkey = null
      let skey = process.env.STRIPE_PUBLISHABLE_KEY

      if (shop_id != 'kompis_app') {


        const shopInfo = await dynamoDbLib.call("scan", {
          TableName: process.env.objectsTableName,
          FilterExpression: "objectType = :objectType and objectId = :objectId",
          ExpressionAttributeValues: {
            ":objectType": 'shop',
            ":objectId": shop_id
          }
        });

        let stripe_integration_id = shopInfo.Items[0].objectStripeIntegration

        const integrationParams = {
          TableName: process.env.integrationsTableName,
          FilterExpression: "integrationType = :integrationType and integrationId = :integrationId",
          ExpressionAttributeValues: {
            ":integrationType": 'stripe',
            ":integrationId": stripe_integration_id
          }
        }


        const stripeIntegrationResult = await dynamoDbLib.call("scan", integrationParams);
        let stripeIntegration = stripeIntegrationResult.Items[0]
        pkey = stripeIntegration.integrationPKey
        skey = stripeIntegration.integrationSKey

      }

      const stripe = require('stripe')(skey);

      const intent = await stripe.setupIntents.create({})

      callback(null, success(intent));

    } else {

      callback(null, failure({
        status: 'you do not have access to this api call'
      }));

    }

  } catch (e) {
    bugsnagClient.notify(user_id, e)
    callback(null, failure({
      status: e
    }));
  }

}