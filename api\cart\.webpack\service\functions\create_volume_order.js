!function(e,t){for(var r in t)e[r]=t[r]}(exports,function(e){var t={};function r(n){if(t[n])return t[n].exports;var a=t[n]={i:n,l:!1,exports:{}};return e[n].call(a.exports,a,a.exports,r),a.l=!0,a.exports}return r.m=e,r.c=t,r.d=function(e,t,n){r.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:n})},r.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},r.t=function(e,t){if(1&t&&(e=r(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var n=Object.create(null);if(r.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var a in e)r.d(n,a,function(t){return e[t]}.bind(null,a));return n},r.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return r.d(t,"a",t),t},r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r.p="",r(r.s=4)}([function(e,t){e.exports=require("aws-sdk")},function(e,t,r){"use strict";r.r(t),r.d(t,"call",function(){return s});var n=r(0),a=r.n(n);a.a.config.update({region:"eu-central-1"});const o=new a.a.DynamoDB.DocumentClient;async function s(e,t){if("scan"==e){let e={Items:[]},n=await o.scan(t).promise();for(e.Items=n.Items;void 0!==n.LastEvaluatedKey;)for(var r in t.ExclusiveStartKey=n.LastEvaluatedKey,(n=await o.scan(t).promise()).Items)e.Items.push(n.Items[r]);return e}return o[e](t).promise()}},function(e,t){e.exports=require("uuid")},function(e,t){e.exports=require("@bugsnag/js")},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.cart=void 0;var n=p(r(5)),a=p(r(6)),o=(t.cart=function(){var e=(0,a.default)(n.default.mark(function e(t,r,a){var m,p,f,b,v,g,h,x,w,j,T,N,O,P,A,F,C,D,q,S,M,E,k,V,U,G,L,z,K,Q,Y,B,R,H,J,W,X,Z,$,ee,te,re,ne,ae,oe,se,ie,ce,ue,de,le,me,pe,ye,Ie,_e,fe,be,ve,ge,he;return n.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,m=["owner","manager","employee"],p=JSON.parse(t.body),f=t.requestContext.identity.cognitoAuthenticationProvider.split("CognitoSignIn:")[1],e.next=6,(0,u.is_level_permitted)(f,m);case 6:return b=e.sent,v=t.pathParameters.id,g=[],h=[],e.next=12,s.call("scan",{TableName:process.env.objectsTableName,FilterExpression:"objectType = :objectType and objectId = :objectId",ExpressionAttributeValues:{":objectType":"shop",":objectId":v}});case 12:return x=e.sent,e.next=15,s.call("scan",{TableName:process.env.objectsTableName,FilterExpression:"objectType = :objectType and objectId = :objectId",ExpressionAttributeValues:{":objectType":"company",":objectId":x.Items[0].objectCompanyId}});case 15:return w=e.sent,j=w.Items[0].objectTax?w.Items[0].objectTax.shipping/100:0,T=!1,x.Items[0].objectCompanyId==b.user_company_id&&(T=!0),x.Items[0].objectName,N=x.Items[0].shopCurrency?x.Items[0].shopCurrency:"NOK",O=x.Items[0].shippingCost?x.Items[0].shippingCost:0,P=x.Items[0].orderDeadline?x.Items[0].orderDeadline:0,A=x.Items[0].orderDeadlineTime?x.Items[0].orderDeadlineTime:23,F=x.Items[0].deliveryFrequency,C=x.Items[0].orderDates?x.Items[0].orderDates:[],D=!!x.Items[0].newOrderNotification&&x.Items[0].newOrderNotification,e.next=29,s.call("scan",{TableName:process.env.objectsTableName,FilterExpression:"objectType = :objectType and objectShopId = :objectShopId",ExpressionAttributeValues:{":objectType":"product",":objectShopId":v}});case 29:if(q=e.sent,!b.level_allowed&&!T){e.next=131;break}return S={TableName:process.env.userPermissionsTableName,FilterExpression:"userId = :userId and locationId = :locationId",ExpressionAttributeValues:{":userId":b.user_company_id,":locationId":p.delivery_point}},e.next=34,s.call("scan",S);case 34:if(!(e.sent.Items.length>0||T)){e.next=128;break}return M=null!=p.company_id?p.company_id:b.user_company_id,e.next=39,s.call("scan",{TableName:process.env.userPermissionsTableName,FilterExpression:"shopId = :shopId and roleType = :roleType and isActive = :isActive and companyId = :companyId",ExpressionAttributeValues:{":shopId":v,":roleType":"shop_access_role",":isActive":!0,":companyId":M}});case 39:for(z in E=e.sent,k=E.Items[0].discount?parseFloat(E.Items[0].discount):0,V=E.Items[0].shipping?E.Items[0].shipping:0,U=p.orders_sheet,G=p.delivery_time,p.comment,L=[],U)for(Y in K=U[z]["Product ID"],Q=U[z]["Product Name"],U[z])["Product ID","Product Name","Product Category","Min Order Quantity","Max Order Quantity"].includes(Y)||(B=new Date(Y),R="",G&&(R=G.split(":"),B.setHours(R[0],R[1],0,0)),B instanceof Date&&!isNaN(B.valueOf())&&!isNaN(parseInt(U[z][Y]))&&L.push({order_date_time:B.getTime(),product_id:K,product_name:Q,quantity:parseInt(U[z][Y])}));for(z in L=y(L,"order_date_time"),L=I(L,"order_date_time"),H=[],J=1e28,W=0,L)for(Y in X=new Date,Z=new Date(+z),parseInt(z)<J&&(J=parseInt(z)),parseInt(z)>W&&(W=parseInt(z)),($=new Date).setDate(X.getDate()+parseInt(P)),P>0&&Z.getTime()<$.getTime()-72e5&&!T&&H.push({error_type:"order_deadline",order_date:Z.getFullYear()+"-"+(Z.getMonth()+1)+"-"+Z.getDate(),order_deadline_time:P,x:Z.getTime(),y:$.getTime()}),X.getFullYear()+"-"+(X.getMonth()+1)+"-"+X.getDate()==Z.getFullYear()+"-"+(Z.getMonth()+1)+"-"+Z.getDate()&&parseInt(A)<X.getHours()&&!T&&H.push({error_type:"order_deadline_time",order_date:Z.getFullYear()+"-"+(Z.getMonth()+1)+"-"+Z.getDate(),order_deadline_time:A}),"weekly"!=F||C.includes(Z.getDay().toString())||T||H.push({error_type:"not_allowed_date",order_date:Z.getFullYear()+"-"+(Z.getMonth()+1)+"-"+Z.getDate(),order_deadline_time:P}),(J=new Date(J)).setHours(0,0,0,0),J=J.getTime(),(W=new Date(W)).setHours(23,59,59,0),W=W.getTime(),L[z])for(ee in q.Items)q.Items[ee].objectId==L[z][Y].product_id&&(te=q.Items[ee].objectMinimumOrderQuantity?q.Items[ee].objectMinimumOrderQuantity:0,re=q.Items[ee].objectMaximumOrderQuantity?q.Items[ee].objectMaximumOrderQuantity:1e3,L[z][Y].quantity<te&&L[z][Y].quantity>0&&H.push({error_type:"minimum_order_quantity",order_date:Z.getFullYear()+"-"+(Z.getMonth()+1)+"-"+Z.getDate(),minimum_order_quantity:te,product_name:L[z][Y].product_name,ordered_quantity:L[z][Y].quantity}),L[z][Y].quantity>re&&L[z][Y].quantity>0&&H.push({error_type:"maximum_order_quantity",order_date:Z.getFullYear()+"-"+(Z.getMonth()+1)+"-"+Z.getDate(),minimum_order_quantity:re,product_name:L[z][Y].product_name,ordered_quantity:L[z][Y].quantity}));if(J-=72e5,W+=72e5,!(H.length>0)){e.next=59;break}a(null,(0,c.success)({errors:H})),e.next=126;break;case 59:return ne={":date1":J,":date2":W,":schedule_type":"once",":shopId":v,":orderStatus":"canceled",":companyId":null!=p.company_id?p.company_id:b.user_company_id},e.next=62,s.call("scan",{TableName:process.env.cartTableName,FilterExpression:"companyId = :companyId and delivery_date >= :date1 and orderStatus <> :orderStatus and shopId = :shopId and delivery_date <= :date2 and schedule_type = :schedule_type ",ExpressionAttributeValues:ne});case 62:ae=e.sent,e.t0=n.default.keys(ae.Items);case 64:if((e.t1=e.t0()).done){e.next=86;break}return z=e.t1.value,h.push(ae.Items[z].cartId),e.next=69,(0,d.log_event)(f,b.user_full_name,b.user_company_name,x.Items[0].objectCompanyId,"order_canceled",{cartId:ae.Items[z].cartId,orderType:"single",created_from:"volume_order"},v,x.Items[0].objectName);case 69:return e.next=71,s.call("update",{TableName:process.env.cartTableName,Key:{cartId:ae.Items[z].cartId},UpdateExpression:"SET powerOfficeInvoiceId = :powerOfficeInvoiceId, powerOfficeInvoiceIdentifier = :powerOfficeInvoiceIdentifier, orderStatus = :orderStatus, canceledAt = :canceledAt, canceledBy = :canceledBy",ExpressionAttributeValues:{":orderStatus":"canceled",":powerOfficeInvoiceId":null,":powerOfficeInvoiceIdentifier":null,":canceledBy":f,":canceledAt":(new Date).getTime()},ReturnValues:"ALL_NEW"});case 71:if(!ae.Items[z].powerOfficeInvoiceIdentifier){e.next=84;break}if(!x.Items[0].objectPowerOfficeIntegration){e.next=84;break}return oe={TableName:process.env.integrationsTableName,FilterExpression:"integrationId = :integrationId",ExpressionAttributeValues:{":integrationId":x.Items[0].objectPowerOfficeIntegration}},e.next=76,s.call("scan",oe);case 76:return se=e.sent,se.Items[0].integrationPOAppKey,ie=se.Items[0].integrationPOClientKey,e.next=81,new l.default(process.env.POWEROFFICE_APP_KEY,ie);case 81:return ce=e.sent,e.next=84,ce.deleteInvoice(ae.Items[z].powerOfficeInvoiceIdentifier);case 84:e.next=64;break;case 86:ue=0,de=0,e.t2=n.default.keys(L);case 89:if((e.t3=e.t2()).done){e.next=113;break}for(Y in z=e.t3.value,le=[],L[z])for(ee in q.Items)q.Items[ee].objectId==L[z][Y].product_id&&parseInt(L[z][Y].quantity)>0&&le.push({id:q.Items[ee].objectId,moq:q.Items[ee].objectMinimumOrderQuantity,mxoq:q.Items[ee].objectMaximumOrderQuantity,name:q.Items[ee].objectName,price:q.Items[ee].objectPrice,discount:q.Items[ee].objectMaximumDiscount?parseFloat(q.Items[ee].objectMaximumDiscount):0,quantity:parseInt(L[z][Y].quantity),mva:q.Items[ee].objectMVA});for(fe in me=0,pe=V||O,ye=0,Ie=0,_e=0,le)be=null,ve=le[fe].discount?le[fe].discount:null,be=null!=ve&&k>=ve?ve:k,le[fe].discount=be,ye+=parseFloat(le[fe].price)*le[fe].quantity,ye=parseFloat(ye*(1-parseFloat(be/100))),Ie+=parseFloat(le[fe].price*(le[fe].mva>0?parseFloat(le[fe].mva)/100+1:1))*le[fe].quantity*(be>0?1-be/100:1),me+=parseFloat(le[fe].price)*le[fe].quantity*(be>0?be/100:1),_e+=parseFloat(le[fe].price*(le[fe].mva>0?parseFloat(le[fe].mva)/100:1))*le[fe].quantity*(be>0?1-be/100:1),de+=le[fe].quantity;if(ye+=parseFloat(pe),Ie+=parseFloat(pe*(1+j)),_e+=parseFloat(pe*j),ge=o.default.v1(),ue+=parseFloat(parseFloat(Math.round(100*Ie)/100)),!(le.length>0)){e.next=111;break}return e.next=108,s.call("put",{TableName:process.env.cartTableName,Item:{cartId:ge,userId:f,discount:k,freight:pe,total:parseFloat(Math.round(100*Ie)/100).toFixed(2),mva:_e,discount_value:me,items:le,shopId:v,schedule_dates:[],schedule_type:"once",orderStatus:"pending",paymentStatus:"unpaid",paymentMethod:"manual",PaymentMethodId:"manual",companyId:null!=p.company_id?p.company_id:b.user_company_id,delivery_date:parseInt(z),delivery_point:p.delivery_point,delivery_time:p.delivery_time,on_behalf_of:p.customer_employee_id,comment:p.comment?p.comment:null,tzo:p.tzo?p.tzo:0,currency:N,createdAt:(new Date).getTime()}});case 108:return g.push(ge),e.next=111,(0,d.log_event)(f,b.user_full_name,b.user_company_name,x.Items[0].objectCompanyId,"order_created",{cartId:ge,orderType:"single",created_from:"volume_order"},v,x.Items[0].objectName);case 111:e.next=89;break;case 113:if(he=o.default.v1(),!(de>0)){e.next=125;break}return e.next=117,s.call("put",{TableName:process.env.cartTableName,Item:{cartId:he,userId:f,total:ue,volume_order_total_products:de,shopId:v,schedule_dates:[],schedule_type:"volume",orderStatus:"pending",paymentStatus:"unpaid",paymentMethod:"manual",PaymentMethodId:"manual",companyId:null!=p.company_id?p.company_id:b.user_company_id,delivery_date_start:J,delivery_date_end:W,delivery_point:p.delivery_point,delivery_time:p.delivery_time,on_behalf_of:p.customer_employee_id,comment:p.comment?p.comment:null,tzo:p.tzo?p.tzo:0,canceled_orders:h,created_orders:g,currency:N,createdAt:(new Date).getTime()}});case 117:return e.next=119,_.publish({Message:he,TopicArn:process.env.sendOrderConfirmationSecondaryCustomerSNSTopic},function(e,t){e&&a(null,(0,c.failure)({status:e}))});case 119:if(!D){e.next=122;break}return e.next=122,_.publish({Message:he,TopicArn:process.env.sendOrderConfirmationSecondaryPrimarySNSTopic},function(e,t){e&&a(null,(0,c.failure)({status:e}))});case 122:a(null,(0,c.success)({volume_cart_id:he})),e.next=126;break;case 125:a(null,(0,c.failure)({status:"your order is empty"}));case 126:e.next=129;break;case 128:a(null,(0,c.failure)({status:"you do not have access to this delivery point"}));case 129:e.next=132;break;case 131:a(null,(0,c.failure)({status:"you do not have access to this api call"}));case 132:e.next=139;break;case 134:e.prev=134,e.t4=e.catch(0),console.log(e.t4),i.notify(f,e.t4),a(null,(0,c.failure)({status:e.t4}));case 139:case"end":return e.stop()}},e,this,[[0,134]])}));return function(t,r,n){return e.apply(this,arguments)}}(),p(r(2))),s=m(r(1)),i=m(r(7)),c=r(8),u=r(9),d=r(10),l=p(r(11));function m(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t.default=e,t}function p(e){return e&&e.__esModule?e:{default:e}}var y=r(14),I=r(15),_=new(r(0).SNS)},function(e,t){e.exports=require("babel-runtime/regenerator")},function(e,t){e.exports=require("babel-runtime/helpers/asyncToGenerator")},function(e,t,r){"use strict";r.r(t),r.d(t,"notify",function(){return o});var n=r(3);const a=r.n(n)()({apiKey:"fd490dc0489374329842ddd3b0b568d7",releaseStage:"dev",appType:"backend",appVersion:"1.00"});function o(e=null,t){a.user={id:e},a.notify(t)}},function(e,t,r){"use strict";function n(e){return o(200,e)}function a(e){return o(500,e)}function o(e,t){return{statusCode:e,headers:{"Access-Control-Allow-Origin":"*","Access-Control-Allow-Credentials":!0},body:JSON.stringify(t)}}r.r(t),r.d(t,"success",function(){return n}),r.d(t,"failure",function(){return a})},function(e,t,r){"use strict";r.r(t),r.d(t,"is_level_permitted",function(){return s}),r.d(t,"is_object_permitted",function(){return i});var n=r(0),a=r.n(n),o=r(1);function s(e,t){return new Promise(function(r,n){let s=new a.a.CognitoIdentityServiceProvider,i=(process.env.COGNITO_POOL_ID,""),c="",u="",d="",l="",m={UserPoolId:process.env.COGNITO_POOL_ID,Username:e};s.adminGetUser(m,async function(e,a){if(e)n(e);else{for(var s in a.UserAttributes)"custom:company_id"==a.UserAttributes[s].Name&&(i=a.UserAttributes[s].Value),"custom:user_type"==a.UserAttributes[s].Name&&(c=a.UserAttributes[s].Value),"custom:first_name"==a.UserAttributes[s].Name&&(d=a.UserAttributes[s].Value),"custom:last_name"==a.UserAttributes[s].Name&&(l=a.UserAttributes[s].Value),"email"==a.UserAttributes[s].Name&&(u=a.UserAttributes[s].Value);const e=await o.call("scan",{TableName:process.env.objectsTableName,FilterExpression:"objectType = :objectType and objectId = :objectId",ExpressionAttributeValues:{":objectType":"company",":objectId":i}});"company"==c||"owner"==c?r({level_allowed:!0,location_allowed:!0,user_company_id:i,user_company_name:e.Items[0].objectName?e.Items[0].objectName:null,user_type:c,user_email:u,user_full_name:d+" "+l}):-1!=t.indexOf(c)?r({level_allowed:!0,location_allowed:!1,user_company_id:i,user_company_name:e.Items[0].objectName?e.Items[0].objectName:null,user_type:c,user_full_name:d+" "+l}):r({level_allowed:!1,location_allowed:!1,user_company_id:i,user_company_name:e.Items[0].objectName?e.Items[0].objectName:null,user_type:c,user_full_name:d+" "+l})}})})}function i(e,t,r,n=null){return new Promise(async function(a,s){"company"!=n&&"owner"!=n||a({is_object_allowed:!0});const i={TableName:process.env.userPermissionsTableName,FilterExpression:"locationId = :locationId and companyId = :companyId and userId = :userId",ExpressionAttributeValues:{":userId":e,":locationId":r,":companyId":t}};1==(await o.call("scan",i)).Items.length?a({is_object_allowed:!0}):a({is_object_allowed:!1})})}a.a.config.update({region:"eu-central-1"})},function(e,t,r){"use strict";r.r(t),r.d(t,"log_event",function(){return s});var n=r(2),a=r.n(n),o=r(1);function s(e,t,r,n,s,i,c=null,u=null){return new Promise(async function(d,l){await o.call("put",{TableName:process.env.eventsLogTableName,Item:{logId:a.a.v1(),userId:e,userFullName:t,userCompanyName:r,companyId:n,eventName:s,eventParams:i,objectId:c,objectName:u,loggedAt:(new Date).getTime()}}),d(!0)})}},function(e,t,r){"use strict";r.r(t),r.d(t,"default",function(){return o});const n=r(12),a=r(13);class o{constructor(e,t){return new Promise(async(r,a)=>{try{this.client_id=e,this.client_secret=t,this.auth_url=process.env.POWEROFFICE_AUTH_URL,this.api_url=process.env.POWEROFFICE_API_URL,this.access_token=await this.getAuthorizationCode(),this.axiosInstance=n.create({baseURL:this.api_url,timeout:3e5,headers:{Authorization:"Bearer "+this.access_token,"Content-Type":"application/json"}})}catch(e){return a(e)}r(this)})}async getGeneralLedgerAccounts(){return(await this.axiosInstance.get("/GeneralLedgerAccount")).data}async getVatCodes(){return(await this.axiosInstance.get("/VatCode")).data}async getCustomers(e=null){return(null==e?await this.axiosInstance.get("/customer"):await this.axiosInstance.get("/customer"+e)).data}async getCustomer(e){return(await this.axiosInstance.get("/customer/"+e)).data}async getCustomerContactPerson(e){return(await this.axiosInstance.get("/customer/"+e+"/contact")).data}async createCustomerContactPerson(e,t){return await this.axiosInstance.post("/customer/"+e+"/contact",t)}async createCustomer(e){return await this.axiosInstance.post("/customer",e)}async updateCustomer(e){return await this.axiosInstance.post("/customer",e)}async createAddress(e){return await this.axiosInstance.post("/address",e)}async createInvoice(e){return await this.axiosInstance.post("/OutgoingInvoice",e)}async createBankJournalVoucher(e){return await this.axiosInstance.post("/Voucher/BankJournalVoucher/",e)}async deleteInvoice(e){return await this.axiosInstance.delete("/OutgoingInvoice/"+e+"/")}async getInvoice(e){return(await this.axiosInstance.get("/OutgoingInvoice/"+e+"/")).data}async getProductGroups(){return(await this.axiosInstance.get("/ProductGroup")).data}async createProductGroup(e){return await this.axiosInstance.post("/ProductGroup",e)}async updateProductGroup(e){return await this.axiosInstance.post("/ProductGroup",e)}async deleteProductGroup(e){return await this.axiosInstance.delete("/ProductGroup/"+e+"/")}async getProducts(){return(await this.axiosInstance.get("/Product")).data}async createProduct(e){return await this.axiosInstance.post("/Product",e)}async updateProduct(e){return await this.axiosInstance.post("/Product",e)}sleep(e){return new Promise(t=>setTimeout(t,e))}async getAuthorizationCode(){const e=a.client(n.create(),{url:this.auth_url,grant_type:"client_credentials",client_id:this.client_id,client_secret:this.client_secret});return(await e()).access_token}}},function(e,t){e.exports=require("axios")},function(e,t){e.exports=require("axios-oauth-client")},function(e,t){e.exports=require("array-sort")},function(e,t){e.exports=require("group-array")}]));