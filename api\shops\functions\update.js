import * as dynamoDbLib from "../../../libs/dynamodb-lib";
import * as bugsnagClient from "../../../libs/bugsnag";
import {
  success,
  failure
} from "../../../libs/response-lib";

import {
  is_level_permitted
} from "../../../libs/permissions";

const AWS = require('aws-sdk')
const SNS = new AWS.SNS();
const S3 = new AWS.S3()

export async function shop(event, context, callback) {

  try {

    const access_scope = ['owner', 'manager', 'employee']

    const data = JSON.parse(event.body);
    var user_id = event.requestContext.identity.cognitoAuthenticationProvider.split('CognitoSignIn:')[1]


    const permissions = await is_level_permitted(user_id, access_scope);
    if (permissions.level_allowed) {


      let group = data.group
      let update_expression = ''
      let expression_attribute_values = {}


      if (group == 'shop-name') {
        update_expression = 'SET objectName = :objectName'
        expression_attribute_values = {
          ":objectName": data.objectName ? data.objectName : null
        }
      }


      if (group == 'theme-settings') {
        update_expression = 'SET objectLogo = :objectLogo,objectIcon = :objectIcon , mainBackgroundColor = :mainBackgroundColor, mainDarkBackground = :mainDarkBackground, mainDarkColor = :mainDarkColor, mainTextColor = :mainTextColor, secondaryTextColor = :secondaryTextColor'
        expression_attribute_values = {
          ":mainBackgroundColor": data.mainBackgroundColor ? data.mainBackgroundColor : '#f8f8f8',
          ":mainDarkBackground": data.mainDarkBackground ? data.mainDarkBackground : '#28a745',
          ":mainDarkColor": data.mainDarkColor ? data.mainDarkColor : '#ffffff',
          ":mainTextColor": data.mainTextColor ? data.mainTextColor : '#0000000',
          ":secondaryTextColor": data.secondaryTextColor ? data.secondaryTextColor : '#000000',
          ":objectLogo": data.objectLogo ? data.objectLogo : null,
          ":objectIcon": data.objectIcon ? data.objectIcon : null
        }
      }

      if (group == 'video-page') {
        update_expression = 'SET videosPageIsActive = :videosPageIsActive'
        expression_attribute_values = {
          ":videosPageIsActive": data.videosPageIsActive ? data.videosPageIsActive : null
        }
      }

      if (group == 'remove-domain') {
        update_expression = 'SET objectDomain = :objectDomain, domainId = :domainId, domainUpdatedAt = :domainUpdatedAt, domainStatus = :domainStatus'
        expression_attribute_values = {
          ":objectDomain": null,
          ":domainId": null,
          ":domainUpdatedAt": null,
          ":domainStatus": null
        }
      }

      if (group == 'email-settings') {
        update_expression = 'SET supportEmail = :supportEmail, sendNotificationsTo = :sendNotificationsTo, dailyOrderNotification = :dailyOrderNotification,newOrderNotification = :newOrderNotification'
        expression_attribute_values = {
          ":dailyOrderNotification": data.dailyOrderNotification ? data.dailyOrderNotification : null,
          ":newOrderNotification": data.newOrderNotification ? data.newOrderNotification : null,
          ":sendNotificationsTo": data.sendNotificationsTo ? data.sendNotificationsTo : null,
          ":supportEmail": data.supportEmail ? data.supportEmail : null,

        }
      }

      if (group == 'landing-page') {
        update_expression = 'SET landingPageIsActive = :landingPageIsActive'
        expression_attribute_values = {
          ":landingPageIsActive": data.landingPageIsActive ? data.landingPageIsActive : null
        }
      }

      if (group == 'image-icon') {
        update_expression = 'SET objectLogo = :objectLogo,objectIcon = :objectIcon'
        expression_attribute_values = {
          ":objectLogo": data.objectLogo ? data.objectLogo : null,
          ":objectIcon": data.objectIcon ? data.objectIcon : null
        }
      }

      if (group == 'machine-service') {
        update_expression = 'SET machineServiceTitle = :machineServiceTitle, machineServiceHeading = :machineServiceHeading, machineServiceButton = :machineServiceButton, machineServiceIsActive = :machineServiceIsActive'
        expression_attribute_values = {
          ":machineServiceTitle": data.machineServiceTitle ? data.machineServiceTitle : null,
          ":machineServiceHeading": data.machineServiceHeading ? data.machineServiceHeading : null,
          ":machineServiceButton": data.machineServiceButton ? data.machineServiceButton : null,
          ":machineServiceIsActive": data.machineServiceIsActive ? data.machineServiceIsActive : false
        }
      }

      if (group == 'order-settings') {
        update_expression = "SET guestCheckout = :guestCheckout, shopCurrency = :shopCurrency, objectName = :objectName, objectPowerOfficeMigrationStartDate = :objectPowerOfficeMigrationStartDate, objectPaymentMethod = :objectPaymentMethod,objectStripeIntegration = :objectStripeIntegration,objectPowerOfficeIntegration = :objectPowerOfficeIntegration,objectPowerOfficeMigrationMode = :objectPowerOfficeMigrationMode, ObjectLastUpdatedBy = :ObjectLastUpdatedBy, deliveryFrequency = :deliveryFrequency , deliveryTimeFrom = :deliveryTimeFrom , deliveryTimeTo = :deliveryTimeTo , orderDeadline = :orderDeadline , orderDeadlineTime = :orderDeadlineTime , showDeliveryTime = :showDeliveryTime, orderDates = :orderDates , shippingCost = :shippingCost"
        expression_attribute_values = {
          ":objectName": data.objectName ? data.objectName : null,
          ":objectPaymentMethod": data.objectPaymentMethod ? data.objectPaymentMethod : null,
          ":objectStripeIntegration": data.objectStripeIntegration ? data.objectStripeIntegration : null,
          ":objectPowerOfficeIntegration": data.objectPowerOfficeIntegration ? data.objectPowerOfficeIntegration : null,
          ":objectPowerOfficeMigrationMode": data.objectPowerOfficeMigrationMode ? data.objectPowerOfficeMigrationMode : null,
          ":objectPowerOfficeMigrationStartDate": (data.objectPowerOfficeMigrationMode == null || data.objectPowerOfficeMigrationMode == 'all') ? null : new Date().getTime(),
          ":deliveryFrequency": data.deliveryFrequency ? data.deliveryFrequency : 'daily',
          ":deliveryTimeFrom": data.deliveryTimeFrom ? data.deliveryTimeFrom : 9,
          ":deliveryTimeTo": data.deliveryTimeTo ? data.deliveryTimeTo : 16,
          ":orderDeadline": data.orderDeadline ? data.orderDeadline : 1,
          ":orderDeadlineTime": data.orderDeadlineTime ? data.orderDeadlineTime : 16,
          ":showDeliveryTime": data.showDeliveryTime ? data.showDeliveryTime : false,
          ":orderDates": data.orderDates ? data.orderDates : [0, 1, 2, 3, 4, 5, 6],
          ":ObjectLastUpdatedBy": user_id,
          ":guestCheckout": data.guestCheckout ? data.guestCheckout : false,
          ":shippingCost": data.shippingCost ? data.shippingCost : 65,
          ":shopCurrency": data.shopCurrency
        }


        await SNS.publish({
          Message: event.pathParameters.id,
          TopicArn: process.env.generatePowerOfficeShippingSNSTopic
        }, function(err, data) {

          if (err) {
            callback(null, err);
          }

        });


      }

      const params = {
        TableName: process.env.objectsTableName,

        Key: {
          objectCompanyId: permissions.user_company_id,
          objectId: event.pathParameters.id
        },

        UpdateExpression: update_expression,
        ExpressionAttributeValues: expression_attribute_values,
        ReturnValues: "ALL_NEW"
      };

      const result = await dynamoDbLib.call("update", params);


      /*



      UpdateExpression: "SET objectName = :objectName, objectPowerOfficeMigrationStartDate=:objectPowerOfficeMigrationStartDate, objectPaymentMethod = :objectPaymentMethod,objectStripeIntegration = :objectStripeIntegration,objectPowerOfficeIntegration = :objectPowerOfficeIntegration,objectPowerOfficeMigrationMode = :objectPowerOfficeMigrationMode,objectLogo = :objectLogo,objectIcon = :objectIcon, ObjectLastUpdatedBy = :ObjectLastUpdatedBy, deliveryFrequency = :deliveryFrequency , deliveryTimeFrom = :deliveryTimeFrom , deliveryTimeTo = :deliveryTimeTo , orderDeadline = :orderDeadline , orderDeadlineTime = :orderDeadlineTime , showDeliveryTime = :showDeliveryTime, landingPageIsActive = :landingPageIsActive , videosPageIsActive = :videosPageIsActive ,  orderDates = :orderDates, machineServiceTitle = :machineServiceTitle, machineServiceHeading = :machineServiceHeading, machineServiceButton = :machineServiceButton, machineServiceIsActive = :machineServiceIsActive, allowRecurringOrders = :allowRecurringOrders , shippingCost = :shippingCost",
      ExpressionAttributeValues: {
        ":objectName": data.objectName ? data.objectName : null,
        ":objectPaymentMethod": data.objectPaymentMethod ? data.objectPaymentMethod : null,
        ":objectStripeIntegration": data.objectStripeIntegration ? data.objectStripeIntegration : null,
        ":objectPowerOfficeIntegration": data.objectPowerOfficeIntegration ? data.objectPowerOfficeIntegration : null,
        ":objectPowerOfficeMigrationMode": data.objectPowerOfficeMigrationMode ? data.objectPowerOfficeMigrationMode : null,
        ":objectPowerOfficeMigrationStartDate": (data.objectPowerOfficeMigrationMode == null || data.objectPowerOfficeMigrationMode == 'all') ? null : new Date().getTime(),
        ":objectLogo": data.objectLogo ? data.objectLogo : null,
        ":objectIcon": data.objectIcon ? data.objectIcon : null,
        ":deliveryFrequency": data.deliveryFrequency ? data.deliveryFrequency : 'daily',
        ":deliveryTimeFrom": data.deliveryTimeFrom ? data.deliveryTimeFrom : 9,
        ":deliveryTimeTo": data.deliveryTimeTo ? data.deliveryTimeTo : 16,
        ":orderDeadline": data.orderDeadline ? data.orderDeadline : 1,
        ":orderDeadlineTime": data.orderDeadlineTime ? data.orderDeadlineTime : 16,
        ":showDeliveryTime": data.showDeliveryTime ? data.showDeliveryTime : false,
        ":landingPageIsActive": data.landingPageIsActive ? data.landingPageIsActive : false,
        ":videosPageIsActive": data.videosPageIsActive ? data.videosPageIsActive : false,
        ":orderDates": data.orderDates ? data.orderDates : [1, 2, 3, 4, 5, 6, 7],
        ":machineServiceTitle": data.machineServiceTitle ? data.machineServiceTitle : null,
        ":machineServiceHeading": data.machineServiceHeading ? data.machineServiceHeading : null,
        ":machineServiceButton": data.machineServiceButton ? data.machineServiceButton : null,
        ":machineServiceIsActive": data.machineServiceIsActive ? data.machineServiceIsActive : false,
        ":allowRecurringOrders": data.allowRecurringOrders ? data.allowRecurringOrders : false,
        ":ObjectLastUpdatedBy": user_id,
        ":shippingCost": data.shippingCost ? data.shippingCost : 65
      },



      let manifest = JSON.stringify({
        name: data.objectName ? data.objectName : null,
        short_name: data.objectName ? data.objectName : null,
        description: data.objectName ? data.objectName : null,
        icons: [{
            src: data.objectIcon ? data.objectIcon : 'https://cdn.kompis.app/public/icon.png',
            sizes: "192x192",
            type: "image/png"
          },
          {
            src: data.objectIcon ? data.objectIcon : 'https://cdn.kompis.app/public/icon.png',
            sizes: "512x512",
            type: "image/png"
          }
        ],
        start_url: "/?standalone=true",
        display: "standalone",
        background_color: "#111111",
        theme_color: "#673ab6",
        lang: "en",
        prefer_related_applications: true
      })

      await S3.upload({
        Bucket: 'kompis-app-uploads/public/manifest',
        Key: 'manifest-' + event.pathParameters.id + '.json',
        Body: manifest,
        ACL: 'public-read'
      }, {
        partSize: 10 * 1024 * 1024,
        queueSize: 1
      }, function(err, data) {});


      await SNS.publish({
        Message: 'null',
        TopicArn: process.env.updateCloudFrontCnamesSNSTopic
      }, function(err, data) {
        if (err) {
          callback(null, failure({
            status: err
          }));
        }
      });

      */

      callback(null, success({
        status: true
      }));


    } else {

      callback(null, failure({
        status: 'you do not have access to this api call'
      }));

    }


  } catch (e) {
    bugsnagClient.notify(user_id, e)
    callback(null, failure({
      status: e
    }));
  }
}