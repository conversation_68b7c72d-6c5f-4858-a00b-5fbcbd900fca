import * as dynamoDbLib from "../../../libs/dynamodb-lib";
import * as bugsnagClient from "../../../libs/bugsnag";
import {
  success,
  failure
} from "../../../libs/response-lib";

export async function task(event, context, callback) {

  try {

    var user_id = event.requestContext.identity.cognitoAuthenticationProvider.split('CognitoSignIn:')[1]
    let task_id = event.pathParameters.id

    var params = {
      TableName: process.env.tasksTableName,
      ScanIndexForward: false,
      Limit: 1,
      KeyConditionExpression: "taskId = :taskId",
      ExpressionAttributeValues: {
        ":taskId": task_id
      }
    };

    const task = await dynamoDbLib.call("query", params);

    callback(null, success(task.Items[0]))

  } catch (e) {
    bugsnagClient.notify(user_id, e)
    callback(null, failure({
      status: e
    }));
  }

}