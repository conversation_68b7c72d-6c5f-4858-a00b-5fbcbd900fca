// This #include statement was automatically added by the Particle IDE.
#include <OneWire.h>

#include <MQTT.h>

// This #include statement was automatically added by the Particle IDE.
//#include <OneWire.h>


#include "DS18.h"

DS18 sensor(D3);

//STARTUP(WiFi.selectAntenna(ANT_EXTERNAL));  // select external antenna

// led color arrays. blue, green, red. (sorry. wiring goof)

int good[] = {0, 40, 0};  // blue, green, red. (sorry. wiring goof)
int bad[] = {0, 0, 100};   // blue, green, red. (sorry. wiring goof)
int off[] = {0, 0, 0};
int cyan[] = {25, 25, 0};


char *IOT_CLIENT = "420025001847343438323536";                    // d:org_id:device_type:device_id
char *IOT_HOST = "*************:1883";  
char *IOT_PASSWORD = "";
char *IOT_PUBLISH = "localgateway_to_awsiot/420025001847343438323536";              // iot-2/evt/event_id/fmt/format_string  "iot-2/evt/{event type}/fmt/json";
char *IOT_USERNAME = "";
String msg;

/*
generated token  = id  // calibration constant :

onAc)!(UqcfK@t1yfX = templogger8   // +0.5
)sN)S?RNtLrbbqx3Qo = templogger7   // on
NLyREm_)y5qT9Z3H9s = templogger6   // -0.5
dv9-A-_FPq7oBAKqwQ = templogger5   //
0TEdiy3WEljJjjy)oI = templogger4   // -0.2
gVCV)FH?0RurA573v@ = templogger3,  // +0.4
KJhlpslmJ@-1hV&)YX = templogger2 , // benchmark
+AC?leB@Ew6CpA0RuF = templogger1 , // +0.2
U8@LX-D52M*iL&Grkz = greenhousetester,


*/

MQTT client( IOT_HOST, 1883, callback ); // port 1883 = insecure, port 8883 = secure


float tempC;

void setup() {


    for(int thisPin = 0; thisPin < 3; thisPin++ ){
        pinMode(thisPin, OUTPUT);
    }

    Particle.publish("connecting to watson...",msg,60,PRIVATE);
    client.connect(IOT_CLIENT, IOT_USERNAME, IOT_PASSWORD);
    delay(3000);
    //client.connect("d:7tmcv8:templogger:ghtest", "use-token-auth", "vO+KDjQP5J4kywfMDL");
    if(!client.isConnected()) {
        led(bad);
        Particle.publish("connection failed",NULL,60,PRIVATE);

    }

    if(client.isConnected()) led(good);

}

void loop() {

      //msg = String(random(0, 4));

    if (sensor.read()) {

        tempC = sensor.celsius();
        tempC = tempC*100 ;
        tempC = round(tempC);
        tempC = (tempC / 100) ;
        msg = String(tempC);

        led(good);
    }

    else if(client.isConnected()){

        client.publish(IOT_PUBLISH, "{\"state\": {\"reported\": {\"temp\": " + msg + "}, \"desired\": null }}" );
        delay(2000);
        client.loop();
        Particle.publish("connected",msg,60,PRIVATE);
        led(good);

    }

    else if(!sensor.read()){
        led(bad);
        Particle.publish("noSensor",msg,60,PRIVATE);
    }





  while(!client.isConnected()) {

    Particle.publish("retrying connection..",msg,60,PRIVATE);
    client.connect(IOT_CLIENT, IOT_USERNAME, IOT_PASSWORD);

    for(int i = 0; i < 3; i++){
        led(bad);
        delay(100);
        led(off);
        delay(100);
    }
    delay(1000);

  }

  delay( 2000 );
}

void led(int col[3]){
    for(int pin = 0; pin < 3; pin++ ){
        analogWrite(pin, col[pin]);
    }
}

void callback( char* topic, byte* payload, unsigned int length ) {
  char p[length + 1];

  memcpy( p, payload, length );
  p[length] = NULL;

  String message( p );
  Particle.publish("received callback:",message,60,PRIVATE);
}
