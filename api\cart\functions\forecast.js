import * as dynamoDbLib from "../../../libs/dynamodb-lib";
import * as bugsnagClient from "../../../libs/bugsnag";
import {
  success,
  failure
} from "../../../libs/response-lib";

import {
  is_level_permitted
} from "../../../libs/permissions";

var arraySort = require('array-sort')
const groupArray = require('group-array');

export async function products(event, context, callback) {

  try {

    const access_scope = ['owner', 'manager', 'employee']
    const data = JSON.parse(event.body);

    var user_id = event.requestContext.identity.cognitoAuthenticationProvider.split('CognitoSignIn:')[1]

    const permissions = await is_level_permitted(user_id, access_scope);

    if (permissions.level_allowed) {

      let shop_id = event.pathParameters.id

      let from = data.from
      let to = data.to
      let from_forecast = data.from_forecast
      let to_forecast = data.to_forecast


      var days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];

      var startDate = new Date(from + 10800000);
      var endDate = new Date(to + 10800000);

      let fields = new Object();
      fields['Product Name'] = 'product_name'

      var getDateArray = function(start, end) {
        var arr = new Array();
        var dt = new Date(start);
        while (dt <= end) {
          let date = dt.getFullYear() + '-' + pad((dt.getMonth() + 1), 2) + '-' + pad(dt.getDate(), 2)
          arr.push({
            index: dt.getDay(),
            name: days[dt.getDay()],
            date: date
          });
          fields[date] = date
          dt.setDate(dt.getDate() + 1);
        }
        return arr;
      }

      var dateArr = getDateArray(startDate, endDate);


      let forecastedOrders = []
      let orders;
      let shopParams = {
        TableName: process.env.objectsTableName,
        FilterExpression: "objectType = :objectType and objectId = :objectId and objectCompanyId = :objectCompanyId",
        ExpressionAttributeValues: {
          ":objectType": 'shop',
          ":objectId": shop_id,
          ":objectCompanyId": permissions.user_company_id,
        }
      }

      const shopResult = await dynamoDbLib.call("scan", shopParams);

      let customers = []
      let products = []
      let order_status = null
      let payment_method = null

      for (var x in data.filters) {
        if (data.filters[x].type.value == 'customers') {
          customers.push(data.filters[x].value.value)
        }

        if (data.filters[x].type.value == 'productsList') {
          products.push(data.filters[x].value.value)
        }
        if (data.filters[x].type.value == 'orderStatus') {
          order_status = data.filters[x].value.value
        }
        if (data.filters[x].type.value == 'paymentMethod') {
          payment_method = data.filters[x].value.value
        }
      }


      let shops_filter_expression = ''
      let shops_filter_expression_values = {
        ":date1": from,
        ":date2": to,
        ":schedule_type": 'once',
        ":shopId": shop_id
      }


      if (order_status != null) {
        shops_filter_expression += ' and orderStatus = :orderStatus '
        shops_filter_expression_values[':orderStatus'] = order_status
      }

      if (payment_method != null) {
        shops_filter_expression += ' and paymentMethod = :paymentMethod '
        shops_filter_expression_values[':paymentMethod'] = payment_method
      }


      let shop_categories = await dynamoDbLib.call("scan", {
        TableName: process.env.objectsTableName,
        FilterExpression: "objectType = :objectType and objectCompanyId = :objectCompanyId and objectShopId = :objectShopId",
        ExpressionAttributeValues: {
          ":objectType": 'category',
          ":objectCompanyId": permissions.user_company_id,
          ":objectShopId": shop_id
        }
      });

      let shop_products = await dynamoDbLib.call("scan", {
        TableName: process.env.objectsTableName,
        FilterExpression: "objectType = :objectType and objectCompanyId = :objectCompanyId and objectShopId = :objectShopId",
        ExpressionAttributeValues: {
          ":objectType": 'product',
          ":objectCompanyId": permissions.user_company_id,
          ":objectShopId": shop_id
        }
      });


      for (var z in shop_products.Items) {

        for (var k in shop_categories.Items) {
          if (shop_categories.Items[k].objectId == shop_products.Items[z].objectCategory) {
            shop_products.Items[z].objectName = shop_products.Items[z].objectName + ' - ' + shop_categories.Items[k].objectName
            shop_products.Items[z].category = shop_categories.Items[k].objectName
          }
        }

      }

      orders = await dynamoDbLib.call("scan", {
        TableName: process.env.cartTableName,
        FilterExpression: "delivery_date >= :date1 and delivery_date <= :date2 and schedule_type = :schedule_type and shopId = :shopId " + shops_filter_expression,
        ExpressionAttributeValues: shops_filter_expression_values
      });




      for (var x in orders.Items) {

        let delivery_date = new Date(orders.Items[x].delivery_date + 10800000)
        delivery_date = delivery_date.getFullYear() + '-' + pad((delivery_date.getMonth() + 1), 2) + '-' + pad(delivery_date.getDate(), 2)

        if (customers.length == 0 || customers.includes(orders.Items[x].companyId)) {
          for (var y in orders.Items[x].items) {
            if (products.length == 0 || products.includes(orders.Items[x].items[y].id)) {

              let product_name = ''
              let category_name = ''
              let min = 0
              let max = 1000

              for (var m in shop_products.Items) {
                if (shop_products.Items[m].objectId == orders.Items[x].items[y].id) {
                  product_name = shop_products.Items[m].objectName
                  category_name = shop_products.Items[m].category
                  min = (shop_products.Items[m].objectMinimumOrderQuantity) ? shop_products.Items[m].objectMinimumOrderQuantity : min
                  max = (shop_products.Items[m].objectMaximumOrderQuantity) ? shop_products.Items[m].objectMaximumOrderQuantity : max
                }
              }

              for (var z in dateArr) {

                if (dateArr[z].date == delivery_date) {
                  forecastedOrders.push({
                    product_id: orders.Items[x].items[y].id,
                    product_name: product_name,
                    category_name: category_name,
                    product_quantity: orders.Items[x].items[y].quantity,
                    delivery_date: delivery_date,
                    parent_order: (orders.Items[x].parent_order) ? orders.Items[x].parent_order : null,
                    product_min: min,
                    product_max: max,
                  })
                } else {
                  forecastedOrders.push({
                    product_id: orders.Items[x].items[y].id,
                    product_name: product_name,
                    category_name: category_name,
                    product_quantity: 0,
                    delivery_date: dateArr[z].date,
                    parent_order: (orders.Items[x].parent_order) ? orders.Items[x].parent_order : null,
                    product_min: min,
                    product_max: max,
                  })
                }

              }
            }

          }

        }

      }

      /*

      let weekly_orders;

      let weekly_shops_filter_expression = ''
      let weekly_shops_filter_expression_values = {
        ":schedule_type": 'weekly',
        ":shopId": shop_id
      }

      if (order_status != null) {
        weekly_shops_filter_expression += ' and orderStatus = :orderStatus '
        weekly_shops_filter_expression_values[':orderStatus'] = order_status
      }

      if (payment_method != null) {
        weekly_shops_filter_expression += ' and paymentMethod = :paymentMethod '
        weekly_shops_filter_expression_values[':paymentMethod'] = payment_method
      }


      weekly_orders = await dynamoDbLib.call("scan", {
        TableName: process.env.cartTableName,
        FilterExpression: "schedule_type = :schedule_type and shopId = :shopId and attribute_not_exists(parent_order)" + weekly_shops_filter_expression,
        ExpressionAttributeValues: weekly_shops_filter_expression_values
      });



      for (var x in weekly_orders.Items) {


        let order_company_id = weekly_orders.Items[x].companyId

        let shops_permissions = await dynamoDbLib.call("scan", {
          TableName: process.env.userPermissionsTableName,
          FilterExpression: "companyId = :companyId and roleType = :roleType and isActive = :isActive and shopId = :shopId",
          ExpressionAttributeValues: {
            ":companyId": order_company_id,
            ":roleType": 'shop_access_role',
            ":isActive": true,
            ":shopId": weekly_orders.Items[x].shopId
          }
        })

        if (shops_permissions.Items[0].allow_recurring_orders) {

          if (customers.length == 0 || customers.includes(weekly_orders.Items[x].companyId)) {

            for (var y in weekly_orders.Items[x].schedule_dates) {

              for (var z in dateArr) {

                if (dateArr[z].index == weekly_orders.Items[x].schedule_dates[y]) {

                  for (var n in weekly_orders.Items[x].items) {
                    if (products.length == 0 || products.includes(weekly_orders.Items[x].items[n].id)) {

                      let product_name = ''
                      let category_name = ''
                      let min = 0
                      let max = 1000
                      for (var m in shop_products.Items) {
                        if (shop_products.Items[m].objectId == weekly_orders.Items[x].items[n].id) {
                          product_name = shop_products.Items[m].objectName
                          category_name = shop_products.Items[m].category
                          min = (shop_products.Items[m].objectMinimumOrderQuantity) ? shop_products.Items[m].objectMinimumOrderQuantity : min
                          max = (shop_products.Items[m].objectMaximumOrderQuantity) ? shop_products.Items[m].objectMaximumOrderQuantity : max
                        }
                      }
                      forecastedOrders.push({
                        product_id: weekly_orders.Items[x].items[n].id,
                        product_name: product_name,
                        category_name: category_name,
                        product_quantity: weekly_orders.Items[x].items[n].quantity,
                        delivery_date: dateArr[z].date,
                        parent_order: weekly_orders.Items[x].cartId,
                        product_min: min,
                        product_max: max,
                      })

                    }
                  }

                } else {

                  for (var n in weekly_orders.Items[x].items) {
                    if (products.length == 0 || products.includes(weekly_orders.Items[x].items[n].id)) {

                      let product_name = ''
                      let category_name = ''
                      let min = 0
                      let max = 1000
                      for (var m in shop_products.Items) {
                        if (shop_products.Items[m].objectId == weekly_orders.Items[x].items[n].id) {
                          product_name = shop_products.Items[m].objectName
                          category_name = shop_products.Items[m].category
                          min = (shop_products.Items[m].objectMinimumOrderQuantity) ? shop_products.Items[m].objectMinimumOrderQuantity : min
                          max = (shop_products.Items[m].objectMaximumOrderQuantity) ? shop_products.Items[m].objectMaximumOrderQuantity : max
                        }
                      }

                      forecastedOrders.push({
                        product_id: weekly_orders.Items[x].items[n].id,
                        product_name: product_name,
                        category_name: category_name,
                        product_quantity: 0,
                        delivery_date: dateArr[z].date,
                        parent_order: weekly_orders.Items[x].cartId,
                        product_min: min,
                        product_max: max,
                      })

                    }
                  }
                }
              }
            }
          }
        }
      }

      */


      forecastedOrders = arraySort(forecastedOrders, 'delivery_date')
      forecastedOrders = groupArray(forecastedOrders, 'product_id', 'delivery_date')

      let cells_data = []
      let count = 0
      for (var x in forecastedOrders) {

        cells_data[count] = {}
        //cells_data[count]['Product Name'] = ''

        for (var y in forecastedOrders[x]) {
          let qty = 0

          for (var z in forecastedOrders[x][y]) {
            qty += parseInt(forecastedOrders[x][y][z].product_quantity)
            cells_data[count]['Product ID'] = forecastedOrders[x][y][z].product_id
            cells_data[count]['Product Category'] = forecastedOrders[x][y][z].category_name
            cells_data[count]['Product Name'] = forecastedOrders[x][y][z].product_name
            cells_data[count]['Min Order Quantity'] = forecastedOrders[x][y][z].product_min
            cells_data[count]['Max Order Quantity'] = forecastedOrders[x][y][z].product_max
          }
          cells_data[count][y] = qty
        }
        count++
      }

      cells_data = arraySort(cells_data, 'Product Name')

      callback(null, success({
        fields,
        cells_data
      }));


    } else {

      callback(null, failure({
        status: 'you do not have access to this api call'
      }));

    }



  } catch (e) {
    console.log(e);
    bugsnagClient.notify(user_id, e)
    callback(null, failure({
      status: e
    }));
  }

}

function pad(num, size) {
  var s = num + "";
  while (s.length < size) s = "0" + s;
  return s;
}