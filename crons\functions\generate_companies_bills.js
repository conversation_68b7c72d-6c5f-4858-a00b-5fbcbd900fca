import uuid from "uuid";
import * as dynamoDbLib from "../../libs/dynamodb-lib";
import * as bugsnagClient from "../../libs/bugsnag";

const AWS = require('aws-sdk')
const axios = require('axios');

export async function generate(event, context, callback) {

  try {

    let sns_company_id = (event.Records) ? event.Records[0].Sns.Message : null
    let companies = null

    if (sns_company_id) {
      companies = await dynamoDbLib.call("scan", {
        TableName: process.env.objectsTableName,
        FilterExpression: "objectType = :objectType and objectId = :objectId",
        ExpressionAttributeValues: {
          ":objectType": 'company',
          ":objectId": sns_company_id
        }
      });
    } else {
      companies = await dynamoDbLib.call("scan", {
        TableName: process.env.objectsTableName,
        FilterExpression: "objectType = :objectType",
        ExpressionAttributeValues: {
          ":objectType": 'company'
        }
      });
    }

    let sales_percentage = 0
    let fixed_charge = 0
    let trail_period = 10


    for (var x in companies.Items) {

      let company_id = companies.Items[x].objectId

      let company_name = (companies.Items[x].objectName) ? companies.Items[x].objectName : null
      let company_org_num = (companies.Items[x].objectOrganizationId) ? companies.Items[x].objectOrganizationId : null
      let company_address = (companies.Items[x].objectAddress) ? companies.Items[x].objectAddress : null
      let company_city = (companies.Items[x].objectCity) ? companies.Items[x].objectCity : null
      let company_post_code = (companies.Items[x].objectPostCode) ? companies.Items[x].objectPostCode : null
      let company_plan = null


      sales_percentage = 0.1
      fixed_charge = 0

      if (companies.Items[x].objectPlan) {

        company_plan = companies.Items[x].objectPlan

        if (companies.Items[x].objectPlan == 'feelancer') {
          sales_percentage = 0.1
          fixed_charge = 0
        }

        if (companies.Items[x].objectPlan == 'startup') {
          sales_percentage = 0.035
          fixed_charge = 35
        }

        if (companies.Items[x].objectPlan == 'scaleup') {
          sales_percentage = 0.005
          fixed_charge = 250
        }

      } else {
        company_plan = 'feelancer'
      }

      let register_date = companies.Items[x].createdAt
      let trail_period_expiry = companies.Items[x].createdAt + (14 * 24 * 60 * 60 * 1000)
      let register_date_minus_today = Math.round(Math.abs(((new Date()).getTime() - (new Date(register_date).getTime())) / (24 * 60 * 60 * 1000)));
      let days_this_month = daysInThisMonth();
      let remaining_days = getRemanningDays();

      let last_day_this_month = new Date(new Date().getFullYear(), (new Date()).getMonth() + 1, 0);

      let register_date_minus_last_day = Math.round(Math.abs(((new Date(last_day_this_month)).getTime() - (new Date(register_date)).getTime()) / (24 * 60 * 60 * 1000)));

      if (register_date_minus_today <= trail_period) {
        console.log(1);
        fixed_charge = 0
      } else {
        console.log(2);
        if ((new Date()).getMonth() == (new Date(register_date)).getMonth() && (new Date()).getFullYear() == (new Date(register_date)).getFullYear()) {
          console.log(3);

          if (register_date_minus_last_day == trail_period) {
            fixed_charge = 0
          }

          if (register_date_minus_last_day > trail_period && fixed_charge > 0) {
            console.log(register_date_minus_last_day);
            fixed_charge = parseFloat((fixed_charge / days_this_month) * (register_date_minus_last_day - trail_period)).toFixed(2)
          }
          if (register_date_minus_last_day < trail_period && fixed_charge > 0) {
            fixed_charge = 0
          }
        } else {
          console.log(4);
          if (
            ((new Date()).getFullYear() == (new Date(register_date)).getFullYear() && (new Date()).getMonth() - (new Date(register_date)).getMonth() == 1) ||
            ((new Date()).getFullYear() - (new Date(register_date)).getFullYear() == 1 && (new Date()).getMonth() - (new Date(register_date)).getMonth() == -11)
          ) {

            let last_day_last_month = new Date();
            last_day_last_month.setDate(1);
            last_day_last_month.setHours(-1);

            let register_date_minus_last_day_last_month = Math.round(Math.abs(((new Date(last_day_last_month)).getTime() - (new Date(register_date)).getTime()) / (24 * 60 * 60 * 1000)));
            let active_days = trail_period - register_date_minus_last_day_last_month

            if (fixed_charge > 0) {
              fixed_charge = parseFloat((fixed_charge / days_this_month) * (days_this_month - active_days)).toFixed(2)
            }

          }

        }

      }

      let day = new Date();
      let current_month = day.getMonth() + 1;
      let current_year = day.getFullYear()

      const bills = await dynamoDbLib.call("scan", {
        TableName: process.env.billsTableName,
        FilterExpression: "companyId = :billCompanyId and billMonth = :billMonth and billYear = :billYear",
        ExpressionAttributeValues: {
          ":billCompanyId": company_id,
          ":billMonth": current_month,
          ":billYear": current_year
        }
      });

      if (bills.Items.length == 0) {

        await dynamoDbLib.call("put", {
          TableName: process.env.billsTableName,
          Item: {
            billId: uuid.v1(),
            billMonth: current_month,
            billYear: current_year,
            paymentStatus: 'in_progress',
            companyId: company_id,
            companyName: company_name,
            companyOrgNumber: company_org_num,
            companyAddress: company_address,
            companyCity: company_city,
            companyPostCode: company_post_code,
            companyPlan: company_plan,
            fixed_charge: fixed_charge,
            sales_charge: 0,
            sales_amount: 0,
            sales_percentage: sales_percentage,
            mva: fixed_charge * 0.25,
            billTotal: fixed_charge + (fixed_charge * 0.25),
            createdAt: new Date().getTime()
          }
        });

      } else {

        let bill = bills.Items[0]

        let date = new Date();
        let first_time_current_month = (new Date(date.getFullYear(), date.getMonth(), 1)).setHours(0, 0, 1);
        let last_time_current_month = (new Date(date.getFullYear(), date.getMonth() + 1, 0)).setHours(23, 59, 59);


        const shops = await dynamoDbLib.call("scan", {
          TableName: process.env.objectsTableName,
          FilterExpression: "objectType = :objectType and objectCompanyId = :objectCompanyId",
          ExpressionAttributeValues: {
            ":objectType": 'shop',
            ":objectCompanyId": company_id
          }
        });


        let shops_bills = []
        let sales_amount = 0
        for (var z in shops.Items) {


          let shop_sales_amount = 0
          let currency = (shops.Items[z].shopCurrency) ? shops.Items[z].shopCurrency : 'NOK'
          let currency_rate = 1

          await axios.get('https://api.exchangeratesapi.io/latest?base=EUR&symbols=' + currency)
            .then(function(response) {
              currency_rate = response.data.rates[currency]
            })
            .catch(function(error) {
              console.log(error);
            })


          let shop_id = shops.Items[z].objectId

          let orders = await dynamoDbLib.call("scan", {
            TableName: process.env.cartTableName,
            FilterExpression: "shopId = :shopId  and orderStatus = :orderStatus and schedule_type = :schedule_type and delivery_date > :delivery_date1 and delivery_date < :delivery_date2 and delivery_date > :delivery_date3",
            ExpressionAttributeValues: {
              ":orderStatus": 'pending',
              ":schedule_type": 'once',
              ":delivery_date1": first_time_current_month,
              ":delivery_date2": last_time_current_month,
              ":delivery_date3": trail_period_expiry,

              ":shopId": shop_id
            }
          });

          for (var y in orders.Items) {

            shop_sales_amount += (parseFloat(orders.Items[y].total) - parseFloat(orders.Items[y].mva)) / currency_rate

          }

          shops_bills.push({
            shop_id: shop_id,
            shop_name: shops.Items[z].objectName,
            currency: currency,
            currency_rate: currency_rate,
            sales_amount: shop_sales_amount
          })

        }
        sales_amount = 0

        for (var k in shops_bills) {
          sales_amount += parseFloat(shops_bills[k].sales_amount)
        }

        let sales_charge = (sales_amount * bill.sales_percentage)

        await dynamoDbLib.call("update", {
          TableName: process.env.billsTableName,
          Key: {
            billId: bill.billId,
          },

          UpdateExpression: "SET shops_bills = :shops_bills, sales_percentage = :sales_percentage, fixed_charge = :fixed_charge, companyPlan = :companyPlan, companyPostCode = :companyPostCode, companyCity = :companyCity, companyAddress = :companyAddress, companyOrgNumber = :companyOrgNumber, companyName = :companyName, sales_charge = :sales_charge, billTotal = :billTotal,sales_amount = :sales_amount, mva = :mva",
          ExpressionAttributeValues: {
            ":sales_charge": sales_charge,
            ":billTotal": (parseFloat(fixed_charge) + sales_charge) + ((parseFloat(fixed_charge) + sales_charge) * 0.25),
            ":sales_amount": sales_amount,
            ":mva": ((parseFloat(fixed_charge) + sales_charge) * 0.25),
            ":companyName": company_name,
            ":companyOrgNumber": company_org_num,
            ":companyAddress": company_address,
            ":companyCity": company_city,
            ":companyPostCode": company_post_code,
            ":companyPlan": company_plan,
            ":fixed_charge": fixed_charge,
            ":sales_percentage": sales_percentage,
            ":shops_bills": shops_bills
          },
          ReturnValues: "ALL_NEW"
        });

      }

      // pay payable bills$


      const payment_method_details = await dynamoDbLib.call("scan", {
        TableName: process.env.objectsTableName,
        FilterExpression: "objectType = :objectType and objectCompanyId = :objectCompanyId and objectIsDefault = :objectIsDefault",
        ExpressionAttributeValues: {
          ":objectType": 'payment_method',
          ":objectCompanyId": company_id,
          ":objectIsDefault": true

        }
      });

      let skey = process.env.STRIPE_PUBLISHABLE_KEY
      let stripe_integration_id = process.env.STRIPE_INTEGRATION_ID
      const stripe = require('stripe')(skey);

      const payable_bills = await dynamoDbLib.call("scan", {
        TableName: process.env.billsTableName,
        FilterExpression: "companyId = :billCompanyId and billMonth < :billMonth and paymentStatus = :paymentStatus",
        ExpressionAttributeValues: {
          ":billCompanyId": company_id,
          ":billMonth": current_month,
          ":paymentStatus": 'in_progress'
        }
      });

      for (var y in payable_bills.Items) {

        if (payment_method_details.Items.length == 1 && payable_bills.Items[y].billTotal > 0) {

          const paymentIntent = await stripe.paymentIntents.create({
            amount: parseInt(payable_bills.Items[y].billTotal * 100),
            currency: 'eur',
            payment_method_types: ['card'],
            customer: payment_method_details.Items[0].objectStripeCustomerId,
            payment_method: payment_method_details.Items[0].objectPaymentMethodId,
            off_session: true,
            confirm: true,
          });

          if (paymentIntent.status == 'succeeded') {

            let transaction_id = uuid.v1()

            await dynamoDbLib.call("put", {
              TableName: process.env.transactionsTableName,
              Item: {
                transactionId: transaction_id,
                transactionDetails: paymentIntent,
                createdAt: new Date().getTime(),
                PaymentMethodId: payment_method_details.Items[0].objectId
              }
            });

            const updateBillParams = {
              TableName: process.env.billsTableName,
              Key: {
                billId: payable_bills.Items[y].billId,
              },

              UpdateExpression: "SET transactionId = :transactionId, paymentStatus = :paymentStatus",
              ExpressionAttributeValues: {
                ":transactionId": transaction_id,
                ":paymentStatus": 'paid'
              },
              ReturnValues: "ALL_NEW"
            };

            await dynamoDbLib.call("update", updateBillParams);

          }

        }

      }

    }


    callback(null, 'done');

  } catch (e) {
    bugsnagClient.notify(null, e)
    callback(null, e);
  }
}

function getRemanningDays() {
  var date = new Date();
  var time = new Date(date.getTime());
  time.setMonth(date.getMonth() + 1);
  time.setDate(0);
  var days = time.getDate() > date.getDate() ? time.getDate() - date.getDate() : 0;
  return days;
}

function daysInThisMonth() {
  var now = new Date();
  return new Date(now.getFullYear(), now.getMonth() + 1, 0).getDate();
}