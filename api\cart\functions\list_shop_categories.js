import * as dynamoDbLib from "../../../libs/dynamodb-lib";
import * as bugsnagClient from "../../../libs/bugsnag";
import {
  success,
  failure
} from "../../../libs/response-lib";

import {
  is_level_permitted
} from "../../../libs/permissions";

var arraySort = require('array-sort')

export async function categories(event, context, callback) {

  try {

    let shop_id = event.pathParameters.id
    let params = {
      TableName: process.env.objectsTableName,
      FilterExpression: "objectType = :objectType and objectShopId = :objectShopId and objectAdminCategory <> :objectAdminCategory and objectVisibility = :objectVisibility",
      ExpressionAttributeValues: {
        ":objectType": 'category',
        ":objectShopId": shop_id,
        ":objectAdminCategory": true,
        ":objectVisibility": true
      }
    }


    if (event.requestContext.identity.cognitoAuthenticationProvider) {

      let access_scope = ['owner', 'manager', 'employee']

      var user_id = event.requestContext.identity.cognitoAuthenticationProvider.split('CognitoSignIn:')[1]

      const permissions = await is_level_permitted(user_id, access_scope);

      const shopInfo = await dynamoDbLib.call("scan", {
        TableName: process.env.objectsTableName,
        FilterExpression: "objectType = :objectType and objectId = :objectId",
        ExpressionAttributeValues: {
          ":objectType": 'shop',
          ":objectId": shop_id
        }
      });


      let is_shop_owner = false

      if (shopInfo.Items[0].objectCompanyId == permissions.user_company_id) {
        is_shop_owner = true
      }




      if (is_shop_owner) {

        params = {
          TableName: process.env.objectsTableName,
          FilterExpression: "objectType = :objectType and objectShopId = :objectShopId and objectVisibility = :objectVisibility",
          ExpressionAttributeValues: {
            ":objectType": 'category',
            ":objectShopId": shop_id,
            ":objectVisibility": true
          }
        };

      }

    }



    const result = await dynamoDbLib.call("scan", params);
    callback(null, success(arraySort(result.Items, 'objectOrder')));

  } catch (e) {
    bugsnagClient.notify(user_id, e)
    callback(null, failure({
      status: e
    }));
  }
}