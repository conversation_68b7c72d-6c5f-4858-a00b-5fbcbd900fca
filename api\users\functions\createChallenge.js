import {
  randomDigits
} from 'crypto-secure-random-digit';
import * as dynamoDbLib from "../../../libs/dynamodb-lib";
const AWS = require('aws-sdk')
const cognito_client = new AWS.CognitoIdentityServiceProvider()


export async function handler(event, context, callback) {

  let secretLoginCode = '1234';

  let pool_id = process.env.COGNITO_POOL_ID

  let params = {
    UserPoolId: pool_id,
    Filter: "username = \"" + event.request.userAttributes.sub + "\""
  };

  let users = await cognito_client.listUsers(params).promise()

  let me = users.Users[0]


  for (var x in me.Attributes) {

    if (me.Attributes[x].Name == 'custom:pin_code') {
      secretLoginCode = me.Attributes[x].Value
    }

  }

  if (!event.request.session || !event.request.session.length) {


  } else {


    const previousChallenge = event.request.session.slice(-1)[0];
    secretLoginCode = previousChallenge.challengeMetadata.match(/CODE-(\d*)/)[1];
  }


  event.response.publicChallengeParameters = {
    email: event.request.userAttributes.email
  };


  event.response.privateChallengeParameters = {
    secretLoginCode
  };

  event.response.challengeMetadata = `CODE-${secretLoginCode}`;
  return event;
};