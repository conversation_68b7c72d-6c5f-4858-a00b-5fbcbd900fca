import * as dynamoDbLib from "../../../libs/dynamodb-lib";
import * as bugsnagClient from "../../../libs/bugsnag";
import {
  success,
  failure
} from "../../../libs/response-lib";

export async function videos(event, context, callback) {

  try {

    let shop_id = event.pathParameters.id

    const params = {
      TableName: process.env.objectsTableName,
      FilterExpression: "objectType = :objectType and objectShopId = :objectShopId",
      ExpressionAttributeValues: {
        ":objectType": 'video',
        ":objectShopId": shop_id
      }
    };

    const result = await dynamoDbLib.call("scan", params);

    callback(null, success(result.Items));

  } catch (e) {
    bugsnagClient.notify('guest', e)
    callback(null, failure({
      status: e
    }));
  }
}