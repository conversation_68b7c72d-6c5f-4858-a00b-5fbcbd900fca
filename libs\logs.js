import uuid from "uuid";
import * as dynamoDbLib from "./dynamodb-lib";

export function log_event(user_id, user_name, user_company, company_id, event_name, params, object_id = null, object_name = null) {

  return new Promise(
    async function(resolve, reject) {

      await dynamoDbLib.call("put", {
        TableName: process.env.eventsLogTableName,
        Item: {
          logId: uuid.v1(),
          userId: user_id,
          userFullName: user_name,
          userCompanyName: user_company,
          companyId: company_id,
          eventName: event_name,
          eventParams: params,
          objectId: object_id,
          objectName: object_name,
          loggedAt: new Date().getTime()
        }
      });

      resolve(true)

    })

}