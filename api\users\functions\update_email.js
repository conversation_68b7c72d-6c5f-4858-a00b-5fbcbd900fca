import {
  success,
  failure
} from "../../../libs/response-lib";
import * as bugsnagClient from "../../../libs/bugsnag";
import {
  is_level_permitted
} from "../../../libs/permissions";


const AWS = require('aws-sdk')
const cognito_client = new AWS.CognitoIdentityServiceProvider()

export async function user(event, context, callback) {

  try {



    var params = {
      UserAttributes: [{
          Name: 'name',
          Value: '32d5e460-f027-11e9-ac4e-112968b6935c'
        },
        {
          Name: 'custom:company_id',
          Value: '32d5e460-f027-11e9-ac4e-112968b6935c'
        },
      ],
      UserPoolId: process.env.COGNITO_POOL_ID,
      Username: '52de05a4-b2ca-4705-b43f-ed252dbf27ee',
    };


    let users_data = await cognito_client.adminUpdateUserAttributes(params).promise()

    callback(null, success({
      users_data
    }))


  } catch (e) {
    console.log(e);
    bugsnagClient.notify('', e)
    callback(null, failure({
      status: e
    }));
  }

}