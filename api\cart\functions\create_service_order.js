import uuid from "uuid";
import * as dynamoDbLib from "../../../libs/dynamodb-lib";
import * as bugsnagClient from "../../../libs/bugsnag";


import {
  success,
  failure
} from "../../../libs/response-lib";

import {
  is_level_permitted
} from "../../../libs/permissions";

import nodemailer from "nodemailer"
const AWS = require('aws-sdk')

export async function cart(event, context, callback) {


  try {

    let cognito_client = new AWS.CognitoIdentityServiceProvider()
    let pool_id = process.env.COGNITO_POOL_ID
    const access_scope = ['owner', 'manager', 'employee']
    const data = JSON.parse(event.body);
    var user_id = event.requestContext.identity.cognitoAuthenticationProvider.split('CognitoSignIn:')[1]

    const permissions = await is_level_permitted(user_id, access_scope);
    if (permissions.level_allowed) {


      let params1 = {
        TableName: process.env.userPermissionsTableName,
        FilterExpression: "userId = :userId and locationId = :locationId",
        ExpressionAttributeValues: {
          ":userId": permissions.user_company_id,
          ":locationId": data.delivery_point
        }
      };

      const result1 = await dynamoDbLib.call("scan", params1);

      if (result1.Items.length == 1) {


        let cart_id = uuid.v1()
        let shop_id = event.pathParameters.id

        const params = {
          TableName: process.env.cartTableName,
          Item: {
            cartId: cart_id,
            userId: user_id,
            freight: 0,
            total: 0,
            shopId: shop_id,
            orderStatus: 'pending',
            schedule_type: 'once',
            companyId: permissions.user_company_id,
            delivery_date: (new Date().getTime()) + 24 * 60 * 60 * 1000,
            delivery_point: data.delivery_point,
            comment: (data.comment) ? data.comment : null,
            items: data.items,
            createdAt: new Date().getTime()
          }
        };

        await dynamoDbLib.call("put", params);

        // send conformation email
        let user = await cognito_client.adminGetUser({
          UserPoolId: pool_id,
          Username: user_id
        }).promise()

        let email = ''

        for (var x in user.UserAttributes) {

          if (user.UserAttributes[x].Name == 'email') {
            email = user.UserAttributes[x].Value
          }
        }


        let transporter = nodemailer.createTransport({
          host: 'email-smtp.eu-west-1.amazonaws.com',
          port: 465,
          secure: true,
          auth: {
            user: 'AKIAJLILB3DMXPRVD4AA',
            pass: 'Amg0o1jLDdfBLT++mU1G0xw99F+cCjVKFg4DYMlQOppz'
          }
        });

        let from_email = '<EMAIL>'

        if (shop_id == '7ac2cdc0-b28d-11e8-a41b-ebbf9e9f6f0e') {
          from_email = '<EMAIL>'
        }

        let mailOptions = {
          from: from_email,
          to: email,
          subject: 'Din ordrebekreftelse fra Nord',
          html: 'Hei <br /> Vi har mottatt din ordre og ser frem imot å varte opp med et smakfult måltid. <br /> Ønsker du å se din ordre, trykk <a href="http://kompis-app.s3-website.eu-central-1.amazonaws.com/auth/login/">her</a> og logg inn på din profil <br /> Har du andre spørsmål, ta kontakt med oss på <br /> telefon 2260 3000 eller <EMAIL> <br /> '
        };

        await transporter.sendMail(mailOptions).then(function(info) {
          callback(null, success(cart_id));
        }).catch(function(err) {
          callback(null, failure({
            status: err
          }));
        });


      } else {


        callback(null, failure({
          status: 'you do not have access to this delivery point'
        }));

      }

    } else {

      callback(null, failure({
        status: 'you do not have access to this api call'
      }));

    }

  } catch (e) {
    bugsnagClient.notify(user_id, e)
    callback(null, failure({
      status: e
    }));
  }

}