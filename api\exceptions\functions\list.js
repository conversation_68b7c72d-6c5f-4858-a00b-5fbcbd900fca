import * as dynamoDbLib from "../../../libs/dynamodb-lib";
import * as bugsnagClient from "../../../libs/bugsnag";
import {
  success,
  failure
} from "../../../libs/response-lib";

import {
  is_level_permitted
} from "../../../libs/permissions";


export async function exception(event, context, callback) {


  try {

    let access_scope = ['owner', 'manager', 'employee']

    var user_id = event.requestContext.identity.cognitoAuthenticationProvider.split('CognitoSignIn:')[1]


    const permissions = await is_level_permitted(user_id, access_scope);

    if (permissions.level_allowed) {

      const deviationsParams = {
        TableName: process.env.exceptionsTableName,
        FilterExpression: "exception_id = :exception_id",
        ExpressionAttributeValues: {
          ":exception_id": event.pathParameters.id
        }
      };

      let deviationsResult = await dynamoDbLib.call("scan", deviationsParams);

      callback(null, success(deviationsResult.Items[0]));

    } else {

      callback(null, failure({
        status: 'you do not have access to this api call'
      }));

    }

  } catch (e) {
    bugsnagClient.notify(user_id, e)

    callback(null, failure({
      status: e
    }));
  }

}