import uuid from "uuid";
import * as dynamoDbLib from "../../../libs/dynamodb-lib";
import * as bugsnagClient from "../../../libs/bugsnag";

import {
  success,
  failure
} from "../../../libs/response-lib";

import {
  is_level_permitted
} from "../../../libs/permissions";

const AWS = require('aws-sdk')
const cognito_client = new AWS.CognitoIdentityServiceProvider()

const acm = new AWS.ACM({
  region: "us-east-1",
  accessKeyId: '********************',
  secretAccessKey: '6bB750d1OHP1Efz2f1AXrXO8kPZhAOpKf22xsyVk'
});

const cloudfront = new AWS.CloudFront({
  apiVersion: '2018-11-05',
  accessKeyId: '********************',
  secretAccessKey: '6bB750d1OHP1Efz2f1AXrXO8kPZhAOpKf22xsyVk',
});

export async function shop(event, context, callback) {


  try {

    const access_scope = ['owner', 'manager', 'employee']

    const data = JSON.parse(event.body);
    let pool_id = process.env.COGNITO_POOL_ID
    var user_id = event.requestContext.identity.cognitoAuthenticationProvider.split('CognitoSignIn:')[1]


    const permissions = await is_level_permitted(user_id, access_scope);

    if (permissions.level_allowed) {

      let shop_id = data.shop_id
      let new_shop_domain = data.shop_domain
      let new_shop_domain_array = new_shop_domain.split('.')
      let ssl_domain_name = ''

      if (new_shop_domain_array.length == 2 || new_shop_domain_array.length == 3) {

        ssl_domain_name = '*.'

        if (new_shop_domain_array.length == 2) {

          new_shop_domain = 'www.' + new_shop_domain
          ssl_domain_name = '*.' + new_shop_domain
          // to get *.domain.com

        } else {
          ssl_domain_name = '*.' + new_shop_domain_array[1] + '.' + new_shop_domain_array[2]
          // to get *.domain.com
        }

        const domains_result = await dynamoDbLib.call("scan", {
          TableName: process.env.objectsTableName,
          FilterExpression: "objectDomain = :objectDomain",
          ExpressionAttributeValues: {
            ":objectDomain": new_shop_domain
          }
        });

        if (domains_result.Items.length == 0) {

          let sslArn = null
          let cloudFrontId = null
          let domain_id = null


          const general_domains_result = await dynamoDbLib.call("scan", {
            TableName: process.env.domainsTableName,
            FilterExpression: "generalDomainName = :generalDomainName",
            ExpressionAttributeValues: {
              ":generalDomainName": ssl_domain_name
            }
          });

          if (general_domains_result.Items.length == 0) {


            let certificate = await acm.requestCertificate({
              DomainName: ssl_domain_name,
              DomainValidationOptions: [{
                DomainName: ssl_domain_name,
                ValidationDomain: ssl_domain_name
              }, ],
              ValidationMethod: 'DNS'
            }).promise();

            sslArn = certificate.CertificateArn


            let shop_cloudfront = await cloudfront.getDistributionConfig({
              Id: process.env.SHOP_CLOUDFRONT_DIST
            }).promise();

            shop_cloudfront.DistributionConfig.CallerReference = uuid.v1()
            shop_cloudfront.DistributionConfig.Aliases.Items = []
            shop_cloudfront.DistributionConfig.Aliases.Quantity = 0



            let cf = await cloudfront.createDistribution({
              DistributionConfig: shop_cloudfront.DistributionConfig
            }).promise();

            cloudFrontId = cf.Distribution.Id

            domain_id = uuid.v1()

            const params = {
              TableName: process.env.domainsTableName,
              Item: {
                domainId: domain_id,
                generalDomainName: ssl_domain_name,
                sslArn: sslArn,
                cloudFrontId: cloudFrontId,
                domainUpdatedAt: new Date().getTime(),
                domainStatus: 'pending'
              }
            };

            await dynamoDbLib.call("put", params);


          } else {

            sslArn = general_domains_result.Items[0].sslArn
            cloudFrontId = general_domains_result.Items[0].cloudFrontId
            domain_id = general_domains_result.Items[0].domainId

          }

          const params = {
            TableName: process.env.objectsTableName,

            Key: {
              objectCompanyId: permissions.user_company_id,
              objectId: shop_id
            },

            UpdateExpression: "SET domainId = :domainId, objectDomain = :objectDomain, domainUpdatedAt = :domainUpdatedAt, domainStatus= :domainStatus",
            ExpressionAttributeValues: {
              ":objectDomain": new_shop_domain ? new_shop_domain : null,
              ":domainId": domain_id,
              ":domainUpdatedAt": new Date().getTime(),
              ":domainStatus": 'pending'
            },
            ReturnValues: "ALL_NEW"
          };

          const result = await dynamoDbLib.call("update", params);

          callback(null, success({
            status: true
          }));

        } else {
          callback(null, failure({
            status: 'this domain name already used by a different shop'
          }));
        }
      } else {
        callback(null, failure({
          status: 'incorrect domain name'
        }));
      }

    } else {

      callback(null, failure({
        status: 'you do not have access to this api call'
      }));

    }

  } catch (e) {
    bugsnagClient.notify(user_id, e)
    callback(null, failure({
      status: e
    }));
  }

}