import AWS from "aws-sdk";
AWS.config.update({
  region: "eu-central-1"
});

import * as dynamoDbLib from "./dynamodb-lib";

export function is_level_permitted(user_id, access_scope) {

  var user_company_id = ''

  return new Promise(
    function(resolve, reject) {

      let cognito_client = new AWS.CognitoIdentityServiceProvider()
      let pool_id = process.env.COGNITO_POOL_ID
      let user_company_id = ''
      let user_type = ''
      let user_email = ''
      let user_first_name = ''
      let user_last_name = ''

      let params = {
        UserPoolId: process.env.COGNITO_POOL_ID,
        Username: user_id
      };


      cognito_client.adminGetUser(params, async function(err, user_data) {

        if (err) {

          reject(err)

        } else {

          for (var x in user_data.UserAttributes) {

            if (user_data.UserAttributes[x].Name == 'custom:company_id') {
              user_company_id = user_data.UserAttributes[x].Value
            }

            if (user_data.UserAttributes[x].Name == 'custom:user_type') {
              user_type = user_data.UserAttributes[x].Value
            }
            if (user_data.UserAttributes[x].Name == 'custom:first_name') {
              user_first_name = user_data.UserAttributes[x].Value
            }
            if (user_data.UserAttributes[x].Name == 'custom:last_name') {
              user_last_name = user_data.UserAttributes[x].Value
            }
            if (user_data.UserAttributes[x].Name == 'email') {
              user_email = user_data.UserAttributes[x].Value
            }

          }

          const company = await dynamoDbLib.call("scan", {
            TableName: process.env.objectsTableName,
            FilterExpression: "objectType = :objectType and objectId = :objectId",
            ExpressionAttributeValues: {
              ":objectType": 'company',
              ":objectId": user_company_id
            }
          });

          if (user_type == 'company' || user_type == 'owner') {
            resolve({
              level_allowed: true,
              location_allowed: true,
              user_company_id: user_company_id,
              user_company_name: (company.Items[0].objectName) ? company.Items[0].objectName : null,
              user_type: user_type,
              user_email: user_email,
              user_full_name: user_first_name + ' ' + user_last_name
            })
          } else {

            if (access_scope.indexOf(user_type) != -1) {

              resolve({
                level_allowed: true,
                location_allowed: false,
                user_company_id: user_company_id,
                user_company_name: (company.Items[0].objectName) ? company.Items[0].objectName : null,
                user_type: user_type,
                user_full_name: user_first_name + ' ' + user_last_name
              })

            } else {
              resolve({
                level_allowed: false,
                location_allowed: false,
                user_company_id: user_company_id,
                user_company_name: (company.Items[0].objectName) ? company.Items[0].objectName : null,
                user_type: user_type,
                user_full_name: user_first_name + ' ' + user_last_name
              })

            }

          }
        }

      })

    }
  );
}


export function is_object_permitted(user_id, company_id, object_id, user_type = null) {

  return new Promise(
    async function(resolve, reject) {

      if (user_type == 'company' || user_type == 'owner') {
        resolve({
          is_object_allowed: true
        })
      }

      const permissionsParam = {
        TableName: process.env.userPermissionsTableName,
        //  KeyConditionExpression: "userId = :userId",
        FilterExpression: "locationId = :locationId and companyId = :companyId and userId = :userId",
        ExpressionAttributeValues: {
          ":userId": user_id,
          ":locationId": object_id,
          ":companyId": company_id
        }
      };

      let permissionsResult = await dynamoDbLib.call("scan", permissionsParam);

      if (permissionsResult.Items.length == 1) {
        resolve({
          is_object_allowed: true
        })

      } else {
        resolve({
          is_object_allowed: false
        })
      }


    })
}