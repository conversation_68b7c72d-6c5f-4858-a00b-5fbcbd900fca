import uuid from "uuid";
import * as dynamoDbLib from "../../../libs/dynamodb-lib";
import * as bugsnagClient from "../../../libs/bugsnag";

import {
  success,
  failure
} from "../../../libs/response-lib";

import {
  is_level_permitted
} from "../../../libs/permissions";

const AWS = require('aws-sdk')
const SNS = new AWS.SNS();
const cognito_client = new AWS.CognitoIdentityServiceProvider()
import PowerOfficeTempUrl from "../../../libs/poweroffice_tempurl";

export async function integration(event, context, callback) {

  try {

    const access_scope = ['owner', 'manager', 'employee']
    const data = JSON.parse(event.body);


    let pool_id = process.env.COGNITO_POOL_ID
    var user_id = event.requestContext.identity.cognitoAuthenticationProvider.split('CognitoSignIn:')[1]
    //var user_id = '62f6d0d4-4d4c-4ce3-82c9-0d92b4b21e3e'

    const permissions = await is_level_permitted(user_id, access_scope);

    if (permissions.level_allowed) {

      let new_integration_id = uuid.v1()


      await dynamoDbLib.call("put", {
        TableName: process.env.integrationsTableName,
        Item: {
          integrationCreatedBy: user_id,
          integrationCompanyId: permissions.user_company_id,
          integrationId: new_integration_id,
          integrationName: data.integrationName,
          integrationType: 'poweroffice',
          integrationStatus: 'pending',
          //integrationPOAppKey: data.integrationPOAppKey,
          //integrationPOClientKey: data.integrationPOClientKey,
          //integrationPO0MVA: data.integrationPO0MVA,
          //integrationPO15MVA: data.integrationPO15MVA,
          //integrationPO25MVA: data.integrationPO25MVA,
          createdAt: new Date().getTime()
        }
      });

      await dynamoDbLib.call("update", {
        TableName: process.env.objectsTableName,

        Key: {
          objectCompanyId: permissions.user_company_id,
          objectId: data.integrationShop
        },

        UpdateExpression: "SET objectPowerOfficeIntegration = :objectPowerOfficeIntegration",
        ExpressionAttributeValues: {
          ":objectPowerOfficeIntegration": new_integration_id,
        },
        ReturnValues: "ALL_NEW"
      });

      let poweroffice = await new PowerOfficeTempUrl(data.integrationOrgNum)

      let url = await poweroffice.createTemporaryUrl(data.redirect, new_integration_id)

      callback(null, success({
        url
      }));

    } else {

      callback(null, failure({
        status: 'you do not have access to this api call'
      }));

    }

  } catch (e) {
    console.log(e);
    bugsnagClient.notify(user_id, e)

    callback(null, failure({
      status: e
    }));
  }

}