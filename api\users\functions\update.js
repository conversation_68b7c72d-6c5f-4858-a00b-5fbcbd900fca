import {
  success,
  failure
} from "../../../libs/response-lib";

import {
  is_level_permitted
} from "../../../libs/permissions";
import * as bugsnagClient from "../../../libs/bugsnag";

const AWS = require('aws-sdk')
const cognito_client = new AWS.CognitoIdentityServiceProvider()

export async function user(event, context, callback) {

  try {

    const data = JSON.parse(event.body);
    let admin_id = event.requestContext.identity.cognitoAuthenticationProvider.split('CognitoSignIn:')[1]
    let pool_id = process.env.COGNITO_POOL_ID
    let user_id = event.pathParameters.id
    let first_name = data.first_name
    let last_name = data.last_name
    let phone = data.phone
    let email = data.email

    let params = {
      UserPoolId: pool_id,
      Username: user_id
    };

    let user_data = await cognito_client.adminGetUser(params).promise();

    let company_id = ''
    let user_type = ''

    for (var x in user_data.UserAttributes) {

      if (user_data.UserAttributes[x].Name == 'custom:company_id') {
        company_id = user_data.UserAttributes[x].Value
      }

      if (user_data.UserAttributes[x].Name == 'custom:user_type') {
        user_type = user_data.UserAttributes[x].Value
      }

    }

    let access_scope = ['owner', 'manager', 'employee']
    /*
    if (user_type == 'owner' && (data.user_type == 'owner' || data.user_type == 'manager' || data.user_type == 'employee')) {
      access_scope = ['owner']
    }

    if (user_type == 'manager' && data.user_type == 'owner') {
      access_scope = ['owner']
    }

    if (user_type == 'manager' && (data.user_type == 'manager' || data.user_type == 'employee')) {
      access_scope = ['owner', 'manager']
    }

    if (user_type == 'employee' && data.user_type == 'owner') {
      access_scope = ['owner']
    }

    if (user_type == 'employee' && data.user_type == 'manager') {
      access_scope = ['owner', 'manager']
    }
    */




    const permissions = await is_level_permitted(admin_id, access_scope);

    if (permissions.level_allowed && company_id == permissions.user_company_id) {

      let params = {
        UserAttributes: [{
            Name: 'custom:first_name',
            Value: first_name
          },
          {
            Name: 'custom:last_name',
            Value: last_name
          },
          {
            Name: 'custom:user_phone',
            Value: phone
          },
          {
            Name: 'custom:user_type',
            Value: (data.user_type) ? data.user_type : user_type
          },
          {
            Name: 'custom:language',
            Value: (data.language) ? data.language : ''
          },
          {
            Name: 'custom:pin_code',
            Value: (data.pin_code) ? data.pin_code : ''
          }
        ],
        UserPoolId: pool_id,
        Username: user_id
      };

      if (email) {
        params.UserAttributes.push({
          Name: 'email',
          Value: email
        }, )
      }



      await cognito_client.adminUpdateUserAttributes(params).promise();



      let updated_user = await cognito_client.listUsers({
        UserPoolId: pool_id,
        Filter: "username = \"" + user_id + "\""
      }).promise()

      callback(null, success(updated_user.Users[0]))

    } else {

      callback(null, failure({
        status: 'you do not have access to this api call'
      }));
    }

  } catch (e) {
    bugsnagClient.notify(user_id, e)
    callback(null, failure({
      error: e
    }))
  }

}