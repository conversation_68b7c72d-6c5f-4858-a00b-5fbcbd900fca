import uuid from "uuid";
import * as dynamoDbLib from "../../../libs/dynamodb-lib";
import * as bugsnagClient from "../../../libs/bugsnag";

import {
  success,
  failure
} from "../../../libs/response-lib";

import {
  is_level_permitted,
  is_object_permitted
} from "../../../libs/permissions";


export async function task(event, context, callback) {

  try {

    let access_scope = ['owner', 'manager', 'employee']
    const data = JSON.parse(event.body);
    var user_id = event.requestContext.identity.cognitoAuthenticationProvider.split('CognitoSignIn:')[1]
    const permissions = await is_level_permitted(user_id, access_scope);


    if (permissions.level_allowed) {

      let params_1 = {
        TableName: process.env.tasksSchedulesTableName,
        ScanIndexForward: false,
        Limit: 1,
        KeyConditionExpression: "taskScheduleId = :taskScheduleId",
        ExpressionAttributeValues: {
          ":taskScheduleId": data.taskScheduleId
        }
      };

      let taskScheduleResult = await dynamoDbLib.call("query", params_1);

      const location_permission = await is_object_permitted(user_id, permissions.user_company_id, taskScheduleResult.Items[0].locationId);


      if (location_permission.is_object_allowed) {

        if (taskScheduleResult.Items.length == 1 && taskScheduleResult.Items[0].taskStatus == 'open') {

          let task_type = taskScheduleResult.Items[0].taskType

          let params_2 = {}
          if (task_type == 'logtemp') {

            // this is a log temp task.

            params_2 = {
              TableName: process.env.tasksLogTableName,
              Item: {
                taskLogId: uuid.v1(),
                taksLoggerId: user_id,
                taskId: taskScheduleResult.Items[0].taskId,
                taskScheduleId: taskScheduleResult.Items[0].taskScheduleId,
                deviceName: taskScheduleResult.Items[0].deviceName,
                deviceType: (taskScheduleResult.Items[0].deviceType) ? taskScheduleResult.Items[0].deviceType : null,
                taskName: taskScheduleResult.Items[0].taskName,
                logged_at: new Date().getTime(),
                companyId: permissions.user_company_id,
                logged_data: data.temperature,
                task_type: data.type,
                locationId: taskScheduleResult.Items[0].locationId,
                taskDeviationFor: taskScheduleResult.Items[0].taskDeviationFor
              }
            };

          } else {

            params_2 = {
              TableName: process.env.tasksLogTableName,
              Item: {
                taskLogId: uuid.v1(),
                taksLoggerId: user_id,
                taskId: taskScheduleResult.Items[0].taskId,
                locationId: taskScheduleResult.Items[0].locationId,
                taskName: taskScheduleResult.Items[0].taskName,
                companyId: permissions.user_company_id,
                task_type: data.type,
                logData: data.log_data,
                logged_data: (data.temperature) ? data.temperature : null,
                taskScheduleId: taskScheduleResult.Items[0].taskScheduleId,
                logged_at: new Date().getTime(),
                taskDeviationFor: taskScheduleResult.Items[0].taskDeviationFor,
                cartId: (taskScheduleResult.Items[0].cartId) ? taskScheduleResult.Items[0].cartId : null
              }
            };

          }

          // this could be a clean, others tasks.

          // create a log record

          await dynamoDbLib.call("put", params_2);

          // make is as completed.

          let params_3 = {
            TableName: process.env.tasksSchedulesTableName,
            Key: {
              taskScheduleId: taskScheduleResult.Items[0].taskScheduleId,
              taskId: taskScheduleResult.Items[0].taskId
            },
            UpdateExpression: "SET taskStatus = :taskStatus",
            ExpressionAttributeValues: {
              ":taskStatus": 'completed'
            },
            ReturnValues: "ALL_NEW"
          };

          await dynamoDbLib.call("update", params_3);


          // check if there is other schedules still open for the same task.


          let params_4 = {
            TableName: process.env.tasksSchedulesTableName,
            FilterExpression: "taskId = :taskId and taskStatus = :taskStatus",
            ExpressionAttributeValues: {
              ":taskId": taskScheduleResult.Items[0].taskId,
              ":taskStatus": 'open'
            }
          };

          let result = await dynamoDbLib.call("scan", params_4);

          if (result.Items.length == 0) {

            let params_5 = {
              TableName: process.env.tasksTableName,

              Key: {
                taskId: taskScheduleResult.Items[0].taskId
              },
              UpdateExpression: "SET taskStatus = :taskStatus",
              ExpressionAttributeValues: {
                ":taskStatus": 'completed'
              },
              ReturnValues: "ALL_NEW"
            };

            await dynamoDbLib.call("update", params_5);


          }

          callback(null, success({
            status: true
          }));



        } else {

          callback(null, failure({
            status: 'no task schedule for the provided id, or task already completed'
          }));

        }


      } else {

        callback(null, failure({
          status: 'you do not have access to this location'
        }));

      }

    } else {

      callback(null, failure({
        status: 'you do not have access to this api call'
      }));

    }

  } catch (e) {
    bugsnagClient.notify(user_id, e)
    callback(null, failure({
      status: e
    }));
  }
}