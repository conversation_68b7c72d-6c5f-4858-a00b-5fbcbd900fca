import uuid from "uuid";
import * as dynamoDbLib from "../../../libs/dynamodb-lib";
import * as bugsnagClient from "../../../libs/bugsnag";

import {
  success,
  failure
} from "../../../libs/response-lib";

import {
  is_level_permitted
} from "../../../libs/permissions";

const AWS = require('aws-sdk')
const cognito_client = new AWS.CognitoIdentityServiceProvider()

const acm = new AWS.ACM({
  region: "us-east-1",
  accessKeyId: '********************',
  secretAccessKey: '6bB750d1OHP1Efz2f1AXrXO8kPZhAOpKf22xsyVk'
});

const cloudfront = new AWS.CloudFront({
  apiVersion: '2018-11-05',
  accessKeyId: '********************',
  secretAccessKey: '6bB750d1OHP1Efz2f1AXrXO8kPZhAOpKf22xsyVk',
});

export async function shop(event, context, callback) {


  try {

    const access_scope = ['owner', 'manager', 'employee']

    let pool_id = process.env.COGNITO_POOL_ID
    var user_id = event.requestContext.identity.cognitoAuthenticationProvider.split('CognitoSignIn:')[1]


    const permissions = await is_level_permitted(user_id, access_scope);

    if (permissions.level_allowed) {

      let shop_id = event.pathParameters.id

      const params = {
        TableName: process.env.objectsTableName,

        Key: {
          objectCompanyId: permissions.user_company_id,
          objectId: shop_id
        },

        UpdateExpression: "SET domainStatus = :domainStatus, domainUpdatedAt = :domainUpdatedAt",
        ExpressionAttributeValues: {
          ":domainStatus": 'submitted',
          ":domainUpdatedAt": new Date().getTime(),
        },
        ReturnValues: "ALL_NEW"
      };

      const result = await dynamoDbLib.call("update", params);

      callback(null, success(true));

    } else {

      callback(null, failure({
        status: 'you do not have access to this api call'
      }));

    }

  } catch (e) {
    bugsnagClient.notify(user_id, e)
    callback(null, failure({
      status: e
    }));
  }

}