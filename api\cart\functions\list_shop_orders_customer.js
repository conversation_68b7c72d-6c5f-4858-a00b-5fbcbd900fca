import * as dynamoDbLib from "../../../libs/dynamodb-lib";
import * as bugsnagClient from "../../../libs/bugsnag";
import {
  success,
  failure
} from "../../../libs/response-lib";

import {
  is_level_permitted
} from "../../../libs/permissions";

const AWS = require('aws-sdk')
const cognito_client = new AWS.CognitoIdentityServiceProvider()


export async function orders(event, context, callback) {

  try {

    const access_scope = ['owner', 'manager', 'employee']
    let pool_id = process.env.COGNITO_POOL_ID
    var user_id = event.requestContext.identity.cognitoAuthenticationProvider.split('CognitoSignIn:')[1]
    //var user_id = '8e41a015-604b-4d46-8337-e67584b8a852'
    const permissions = await is_level_permitted(user_id, access_scope);

    if (permissions.level_allowed) {

      const permissionParams = {
        TableName: process.env.userPermissionsTableName,
        FilterExpression: "roleType = :roleType and companyId = :companyId",
        ExpressionAttributeValues: {
          ":roleType": 'shop_access_role',
          ":companyId": permissions.user_company_id
        }
      }

      const permissionResult = await dynamoDbLib.call("scan", permissionParams);

      let shops = []

      for (var x in permissionResult.Items) {
        shops.push(permissionResult.Items[x].shopId)
      }

      let ordersParams = {}

      if (permissions.user_type == 'employee') {

        // show him only his orders

        ordersParams = {
          TableName: process.env.cartTableName,
          FilterExpression: "shopId IN (:shops) and userId = :userId",
          ExpressionAttributeValues: {
            ":shops": shops.toString(),
            ":userId": user_id,
            // /":orderStatus": 'canceled'
          }
        };

      } else {

        // show all the orders in this shop.

        ordersParams = {
          TableName: process.env.cartTableName,
          FilterExpression: "shopId IN (:shops) and companyId = :companyId and orderStatus <> :orderStatus",
          ExpressionAttributeValues: {
            ":shops": shops.toString(),
            ":companyId": permissions.user_company_id,
            ":orderStatus": 'canceled'
          }
        };

      }


      let users = []
      let compaines = []
      let delivery_points = []

      let users_ids = []
      let compaines_ids = []
      let delivery_points_ids = []




      const orders = await dynamoDbLib.call("scan", ordersParams);

      for (var x in orders.Items) {

        if (!users_ids.includes(orders.Items[x].userId)) {
          users_ids.push(orders.Items[x].userId)
        }

        if (orders.Items[x].on_behalf_of) {
          if (!users_ids.includes(orders.Items[x].on_behalf_of)) {
            users_ids.push(orders.Items[x].on_behalf_of)
          }

        }

        if (!delivery_points_ids.includes(orders.Items[x].delivery_point)) {
          delivery_points_ids.push(orders.Items[x].delivery_point)
        }

      }



      for (var x in users_ids) {
        let params = {
          UserPoolId: pool_id,
          Username: users_ids[x]
        };
        let user = await cognito_client.adminGetUser(params).promise()

        users.push({
          user_id: users_ids[x],
          user: user
        })

        let company_id = null

        for (var z in user.UserAttributes) {
          if (user.UserAttributes[z].Name == 'custom:company_id') {
            company_id = user.UserAttributes[z].Value
          }
        }

        if (!compaines_ids.includes(company_id)) {
          compaines_ids.push(company_id)
        }

      }




      for (var x in compaines_ids) {

        let user_company = await dynamoDbLib.call("scan", {
          TableName: process.env.objectsTableName,
          FilterExpression: "objectType = :objectType and objectId = :objectId",
          ExpressionAttributeValues: {
            ":objectType": 'company',
            ":objectId": compaines_ids[x],
          }
        });

        compaines.push({
          company_id: compaines_ids[x],
          company: user_company.Items[0]
        })
      }

      for (var x in delivery_points_ids) {

        let DeliveryPointsParams = {
          TableName: process.env.objectsTableName,
          FilterExpression: "objectType = :objectType and objectId = :objectId",
          ExpressionAttributeValues: {
            ":objectType": 'delivery_point',
            ":objectId": delivery_points_ids[x],
          }
        };

        let delivery_point = await dynamoDbLib.call("scan", DeliveryPointsParams);
        delivery_points.push({
          delivery_point_id: delivery_points_ids[x],
          delivery_point: delivery_point.Items[0]
        })

      }


      let shop_categories = await dynamoDbLib.call("scan", {
        TableName: process.env.objectsTableName,
        FilterExpression: "objectType = :objectType and objectShopId = :objectShopId",
        ExpressionAttributeValues: {
          ":objectType": 'category',
          ":objectShopId": shop_id
        }
      });

      let shop_products = await dynamoDbLib.call("scan", {
        TableName: process.env.objectsTableName,
        FilterExpression: "objectType = :objectType and objectShopId = :objectShopId",
        ExpressionAttributeValues: {
          ":objectType": 'product',
          ":objectShopId": shop_id
        }
      });

      for (var x in orders.Items) {


        for (var y in orders.Items[x].items) {

          for (var z in shop_products.Items) {
            if (orders.Items[x].items[y].id == shop_products.Items[z].objectId) {

              for (var k in shop_categories.Items) {
                if (shop_categories.Items[k].objectId == shop_products.Items[z].objectCategory) {
                  orders.Items[x].items[y].category = shop_categories.Items[k].objectName
                }
              }

            }
          }

        }

        for (var y in users) {

          let company_id = null

          if (orders.Items[x].userId == users[y].user_id) {
            orders.Items[x].user = users[y].user
            for (var z in users[y].user.UserAttributes) {

              if (users[y].user.UserAttributes[z].Name == 'custom:company_id') {
                company_id = users[y].user.UserAttributes[z].Value
                for (var k in compaines) {
                  if (company_id == compaines[k].company_id) {
                    orders.Items[x].user_company = compaines[k].company
                  }
                }
              }
            }
          }

          if (orders.Items[x].on_behalf_of) {

            if (orders.Items[x].on_behalf_of == users[y].user_id) {

              orders.Items[x].on_behalf_of = users[y].user
              for (var z in users[y].user.UserAttributes) {

                if (users[y].user.UserAttributes[z].Name == 'custom:company_id') {
                  company_id = users[y].user.UserAttributes[z].Value
                  for (var k in compaines) {
                    if (company_id == compaines[k].company_id) {
                      orders.Items[x].user_company = compaines[k].company
                    }
                  }
                }
              }
            }
          }
        }

        for (var y in delivery_points) {
          if (delivery_points[y].delivery_point_id == orders.Items[x].delivery_point) {
            orders.Items[x].delivery_point = delivery_points[y].delivery_point
          }
        }

      }



      callback(null, success(orders.Items));



    } else {

      callback(null, failure({
        status: 'you do not have access to this api call'
      }));

    }
  } catch (e) {
    bugsnagClient.notify(user_id, e)
    callback(null, failure({
      status: e
    }));
  }
}