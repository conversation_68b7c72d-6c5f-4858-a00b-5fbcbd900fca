import {
  success,
  failure
} from "../../../libs/response-lib";
import {
  is_level_permitted
} from "../../../libs/permissions";
import * as bugsnagClient from "../../../libs/bugsnag";

const AWS = require('aws-sdk')
const cognito_client = new AWS.CognitoIdentityServiceProvider()

export async function user(event, context, callback) {


  try {

    const data = JSON.parse(event.body);
    var admin_id = event.requestContext.identity.cognitoAuthenticationProvider.split('CognitoSignIn:')[1]

    var pool_id = process.env.COGNITO_POOL_ID
    var user_id = event.pathParameters.id


    let params = {
      UserPoolId: pool_id,
      Username: user_id
    };


    let user_data = await cognito_client.adminGetUser(params).promise()

    let company_id = ''
    let user_type = ''

    for (var x in user_data.UserAttributes) {

      if (user_data.UserAttributes[x].Name == 'custom:company_id') {
        company_id = user_data.UserAttributes[x].Value
      }

      if (user_data.UserAttributes[x].Name == 'custom:user_type') {
        user_type = user_data.UserAttributes[x].Value
      }

    }

    let access_scope = []

    if (user_type == 'manager') {
      access_scope = ['owner', 'manager']
    } else if (user_type == 'employee') {
      access_scope = ['owner', 'manager']

    } else {

      access_scope = ['owner']

    }

    const permissions = await is_level_permitted(admin_id, access_scope);

    if (permissions.level_allowed && company_id == permissions.user_company_id && user_id != admin_id) {

      var params = {
        UserPoolId: pool_id,
        Username: user_id
      };

      await cognito_client.adminEnableUser(params).promise()


      // delete all tasks assigned by this user.

      callback(null, success(true))

    } else {

      callback(null, failure({
        status: 'you do not have access to this api call'
      }));
    }


  } catch (e) {
    bugsnagClient.notify(user_id, e)
    callback(null, failure({
      error: e
    }))
  }
}