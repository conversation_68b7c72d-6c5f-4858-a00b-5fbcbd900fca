(()=>{"use strict";var e={216:(e,t,s)=>{s.r(t),s.d(t,{notify:()=>n});const r=require("@bugsnag/js"),a=s.n(r)()({apiKey:"fd490dc0489374329842ddd3b0b568d7",releaseStage:"dev",appType:"backend",appVersion:"1.00"});function n(e=null,t){a.user={id:e},a.notify(t)}},493:(e,t,s)=>{s.r(t),s.d(t,{call:()=>o});var r=s(336),a=s.n(r);a().config.update({region:"eu-central-1"});const n=new(a().DynamoDB.DocumentClient);async function o(e,t){if("scan"==e){let e={Items:[]},r=await n.scan(t).promise();for(e.Items=r.Items;void 0!==r.LastEvaluatedKey;)for(var s in t.ExclusiveStartKey=r.LastEvaluatedKey,r=await n.scan(t).promise(),r.Items)e.Items.push(r.Items[s]);return e}return n[e](t).promise()}},328:(e,t,s)=>{s.r(t),s.d(t,{is_level_permitted:()=>o,is_object_permitted:()=>i});var r=s(336),a=s.n(r),n=s(493);function o(e,t){return new Promise((function(s,r){let o=new(a().CognitoIdentityServiceProvider),i=(process.env.COGNITO_POOL_ID,""),c="",u="",d="",l="",m={UserPoolId:process.env.COGNITO_POOL_ID,Username:e};o.adminGetUser(m,(async function(e,a){if(e)r(e);else{for(var o in a.UserAttributes)"custom:company_id"==a.UserAttributes[o].Name&&(i=a.UserAttributes[o].Value),"custom:user_type"==a.UserAttributes[o].Name&&(c=a.UserAttributes[o].Value),"custom:first_name"==a.UserAttributes[o].Name&&(d=a.UserAttributes[o].Value),"custom:last_name"==a.UserAttributes[o].Name&&(l=a.UserAttributes[o].Value),"email"==a.UserAttributes[o].Name&&(u=a.UserAttributes[o].Value);const e=await n.call("scan",{TableName:process.env.objectsTableName,FilterExpression:"objectType = :objectType and objectId = :objectId",ExpressionAttributeValues:{":objectType":"company",":objectId":i}});"company"==c||"owner"==c?s({level_allowed:!0,location_allowed:!0,user_company_id:i,user_company_name:e.Items[0].objectName?e.Items[0].objectName:null,user_type:c,user_email:u,user_full_name:d+" "+l}):-1!=t.indexOf(c)?s({level_allowed:!0,location_allowed:!1,user_company_id:i,user_company_name:e.Items[0].objectName?e.Items[0].objectName:null,user_type:c,user_full_name:d+" "+l}):s({level_allowed:!1,location_allowed:!1,user_company_id:i,user_company_name:e.Items[0].objectName?e.Items[0].objectName:null,user_type:c,user_full_name:d+" "+l})}}))}))}function i(e,t,s,r=null){return new Promise((async function(a,o){"company"!=r&&"owner"!=r||a({is_object_allowed:!0});const i={TableName:process.env.userPermissionsTableName,FilterExpression:"locationId = :locationId and companyId = :companyId and userId = :userId",ExpressionAttributeValues:{":userId":e,":locationId":s,":companyId":t}};1==(await n.call("scan",i)).Items.length?a({is_object_allowed:!0}):a({is_object_allowed:!1})}))}a().config.update({region:"eu-central-1"})},400:(e,t,s)=>{s.r(t),s.d(t,{default:()=>n});const r=s(167),a=s(740);class n{constructor(e,t){return new Promise((async(s,a)=>{try{this.client_id=e,this.client_secret=t,this.auth_url=process.env.POWEROFFICE_AUTH_URL,this.api_url=process.env.POWEROFFICE_API_URL,this.access_token=await this.getAuthorizationCode(),this.axiosInstance=r.create({baseURL:this.api_url,timeout:3e5,headers:{Authorization:"Bearer "+this.access_token,"Content-Type":"application/json"}})}catch(e){return a(e)}s(this)}))}async getGeneralLedgerAccounts(){return(await this.axiosInstance.get("/GeneralLedgerAccount")).data}async getVatCodes(){return(await this.axiosInstance.get("/VatCode")).data}async getCustomers(e=null){return(null==e?await this.axiosInstance.get("/customer"):await this.axiosInstance.get("/customer"+e)).data}async getCustomer(e){return(await this.axiosInstance.get("/customer/"+e)).data}async getCustomerContactPerson(e){return(await this.axiosInstance.get("/customer/"+e+"/contact")).data}async createCustomerContactPerson(e,t){return await this.axiosInstance.post("/customer/"+e+"/contact",t)}async createCustomer(e){return await this.axiosInstance.post("/customer",e)}async updateCustomer(e){return await this.axiosInstance.post("/customer",e)}async createAddress(e){return await this.axiosInstance.post("/address",e)}async createInvoice(e){return await this.axiosInstance.post("/OutgoingInvoice",e)}async createBankJournalVoucher(e){return await this.axiosInstance.post("/Voucher/BankJournalVoucher/",e)}async deleteInvoice(e){return await this.axiosInstance.delete("/OutgoingInvoice/"+e+"/")}async getInvoice(e){return(await this.axiosInstance.get("/OutgoingInvoice/"+e+"/")).data}async getProductGroups(){return(await this.axiosInstance.get("/ProductGroup")).data}async createProductGroup(e){return await this.axiosInstance.post("/ProductGroup",e)}async updateProductGroup(e){return await this.axiosInstance.post("/ProductGroup",e)}async deleteProductGroup(e){return await this.axiosInstance.delete("/ProductGroup/"+e+"/")}async getProducts(){return(await this.axiosInstance.get("/Product")).data}async createProduct(e){return await this.axiosInstance.post("/Product",e)}async updateProduct(e){return await this.axiosInstance.post("/Product",e)}sleep(e){return new Promise((t=>setTimeout(t,e)))}async getAuthorizationCode(){const e=a.client(r.create(),{url:this.auth_url,grant_type:"client_credentials",client_id:this.client_id,client_secret:this.client_secret});return(await e()).access_token}}},226:(e,t,s)=>{function r(e){return n(200,e)}function a(e){return n(500,e)}function n(e,t){return{statusCode:e,headers:{"Access-Control-Allow-Origin":"*","Access-Control-Allow-Credentials":!0},body:JSON.stringify(t)}}s.r(t),s.d(t,{failure:()=>a,success:()=>r})},336:e=>{e.exports=require("aws-sdk")},167:e=>{e.exports=require("axios")},740:e=>{e.exports=require("axios-oauth-client")},955:e=>{e.exports=require("babel-runtime/helpers/asyncToGenerator")},403:e=>{e.exports=require("babel-runtime/regenerator")}},t={};function s(r){var a=t[r];if(void 0!==a)return a.exports;var n=t[r]={exports:{}};return e[r](n,n.exports,s),n.exports}s.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return s.d(t,{a:t}),t},s.d=(e,t)=>{for(var r in t)s.o(t,r)&&!s.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},s.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),s.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var r={};(()=>{var e=r;Object.defineProperty(e,"__esModule",{value:!0}),e.init=void 0;var t,a=d(s(403)),n=d(s(955)),o=(e.init=(t=(0,n.default)(a.default.mark((function e(t,s,r){var n,u,d,p,b,I,_,f,y,x,A,v,N,g,h,w,P,U,j,C,T,O,E,k,V,F,L,G,M,S,z,K,R,D,q,W,B,J,H,Y,Z,$,Q,X,ee,te,se,re,ae,ne;return a.default.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,n=t.Records[0].Sns.Message,e.next=4,i.call("scan",{TableName:process.env.userPermissionsTableName,FilterExpression:"permissionId = :permissionId",ExpressionAttributeValues:{":permissionId":n}});case 4:return u=e.sent,d=u.Items[0].shopId,p={TableName:process.env.objectsTableName,FilterExpression:"objectType = :objectType and objectId = :objectId",ExpressionAttributeValues:{":objectType":"shop",":objectId":d}},e.next=9,i.call("scan",p);case 9:if(!(b=e.sent).Items[0].objectPowerOfficeIntegration){e.next=161;break}return b.Items[0].objectCompanyId,b.Items[0].objectPowerOfficeMigrationMode,I={TableName:process.env.integrationsTableName,FilterExpression:"integrationId = :integrationId",ExpressionAttributeValues:{":integrationId":b.Items[0].objectPowerOfficeIntegration}},e.next=16,i.call("scan",I);case 16:return(_=e.sent).Items[0].integrationPOAppKey,f=_.Items[0].integrationPOClientKey,_.Items[0].integrationPO0MVA,_.Items[0].integrationPO15MVA,_.Items[0].integrationPO25MVA,_.Items[0].createdAt,e.next=25,new c.default(process.env.POWEROFFICE_APP_KEY,f);case 25:return y=e.sent,x=[],e.next=29,i.call("scan",{TableName:process.env.objectsTableName,FilterExpression:"objectType = :objectType and objectShopId = :objectShopId",ExpressionAttributeValues:{":objectType":"delivery_point",":objectShopId":d}});case 29:A=e.sent,e.t0=a.default.keys(u.Items);case 31:if((e.t1=e.t0()).done){e.next=94;break}return v=e.t1.value,e.next=35,i.call("scan",{TableName:process.env.objectsTableName,FilterExpression:"objectType = :objectType and objectId = :objectId",ExpressionAttributeValues:{":objectType":"company",":objectId":u.Items[v].companyId}});case 35:return N=e.sent,g=process.env.COGNITO_POOL_ID,h=u.Items[v].permissionId,w=u.Items[v].powerofficeCustomerId?u.Items[v].powerofficeCustomerId:null,e.next=42,l.describeUserPool({UserPoolId:g}).promise();case 42:P=e.sent,U=P.UserPool.EstimatedNumberOfUsers,j=Math.ceil(U/60),C="",T=1;case 47:if(!(T<=j)){e.next=92;break}return{},O=""!=C?{UserPoolId:g,AttributesToGet:null,Filter:'name = "'+u.Items[v].companyId+'"',Limit:0,PaginationToken:C}:{UserPoolId:g,AttributesToGet:null,Filter:'name = "'+u.Items[v].companyId+'"',Limit:0},e.next=52,l.listUsers(O).promise();case 52:E=e.sent,C=E.PaginationToken,e.t2=a.default.keys(E.Users);case 55:if((e.t3=e.t2()).done){e.next=89;break}for(G in k=e.t3.value,V="",F="",L=!1,E.Users[k].Attributes)"custom:user_type"==E.Users[k].Attributes[G].Name&&(V=E.Users[k].Attributes[G].Value),"custom:company_id"==E.Users[k].Attributes[G].Name&&(F=E.Users[k].Attributes[G].Value),"email_verified"==E.Users[k].Attributes[G].Name&&E.Users[k].Attributes[G].Value;if(u.Items[v].contactId,L||"owner"!=V||F!=u.Items[v].companyId){e.next=87;break}for(G in L=!0,M="",S="",z="",K="",R=N.Items[0].objectAddress,D=N.Items[0].objectPostCode,q=N.Items[0].objectName,W=N.Items[0].objectOrganizationId,B=u.Items[v].invoice_delivery_type?u.Items[v].invoice_delivery_type:1,J=N.Items[0].objectCity,H="","mail"==B&&(B=1),"ehf"==B&&(B=3),"avtale_giro"==B&&(B=4),E.Users[k].Attributes)"custom:first_name"==E.Users[k].Attributes[G].Name&&(M=E.Users[k].Attributes[G].Value),"custom:last_name"==E.Users[k].Attributes[G].Name&&(S=E.Users[k].Attributes[G].Value),"custom:user_phone"==E.Users[k].Attributes[G].Name&&(K=E.Users[k].Attributes[G].Value),E.Users[k].Attributes[G].Name,"custom:company_id"==E.Users[k].Attributes[G].Name&&(H=E.Users[k].Attributes[G].Value),E.Users[k].Attributes[G].Name,E.Users[k].Attributes[G].Name,E.Users[k].Attributes[G].Name,"email"==E.Users[k].Attributes[G].Name&&(z=E.Users[k].Attributes[G].Value),E.Users[k].Attributes[G].Name;return Y=[],e.next=83,i.call("scan",{TableName:process.env.userPermissionsTableName,FilterExpression:"userId = :userId",ExpressionAttributeValues:{":userId":H}});case 83:for($ in(Z=e.sent).Items)for(k in A.Items)Z.Items[$].locationId==A.Items[k].objectId&&(Z.Items[$].powerofficeAddressId?Y.push({Id:Z.Items[$].powerofficeAddressId,Address1:A.Items[k].objectAddress,CountryCode:"NO",IsPrimary:!1}):Y.push({Address1:A.Items[k].objectAddress,CountryCode:"NO",IsPrimary:!1}));for(k in E.Users)if(E.Users[k].Username==u.Items[v].contactId)for(G in E.Users[k].Attributes)"custom:first_name"==E.Users[k].Attributes[G].Name&&(M=E.Users[k].Attributes[G].Value),"custom:last_name"==E.Users[k].Attributes[G].Name&&(S=E.Users[k].Attributes[G].Value),"custom:user_phone"==E.Users[k].Attributes[G].Name&&(K=E.Users[k].Attributes[G].Value),"email"==E.Users[k].Attributes[G].Name&&(z=E.Users[k].Attributes[G].Value);x.push({first_name:M,last_name:S,phone:K,email:z,address:R,post_code:D,company_name:q,organization_id:W,city:J,customer_company_id:H,invoice_delivery_type:B,customer_address:Y,poweroffice_customer_id:w,permission_id:h});case 87:e.next=55;break;case 89:T++,e.next=47;break;case 92:e.next=31;break;case 94:return x=m(x,"customer_company_id"),Q=null,e.next=98,y.getCustomers("?$filter=IsArchived%20eq%20false");case 98:X=e.sent,e.t4=a.default.keys(x);case 100:if((e.t5=e.t4()).done){e.next=133;break}if(v=e.t5.value,null==x[v].poweroffice_customer_id||0==x[v].poweroffice_customer_id){e.next=120;break}return console.log("if"),e.next=106,y.getCustomerContactPerson(x[v].poweroffice_customer_id);case 106:if(0!=(ee=e.sent).data.length){e.next=112;break}return e.next=110,y.createCustomerContactPerson(x[v].poweroffice_customer_id,{FirstName:x[v].first_name,LastName:x[v].last_name,EmailAddress:x[v].email});case 110:e.next=114;break;case 112:return e.next=114,y.createCustomerContactPerson(x[v].poweroffice_customer_id,{id:ee.data[0].id,FirstName:x[v].first_name,LastName:x[v].last_name,EmailAddress:x[v].email});case 114:return e.next=116,y.updateCustomer({id:x[v].poweroffice_customer_id,emailAddress:x[v].email,firstName:x[v].first_name,lastName:x[v].last_name,invoiceDeliveryType:x[v].invoice_delivery_type,invoiceEmailAddress:x[v].email,isPerson:!1,isArchived:!1,legalName:x[v].company_name,name:x[v].company_name,phoneNumber:x[v].phone,vatNumber:x[v].organization_id,streetAddresses:x[v].customer_address,mailAddress:{address1:x[v].address,city:x[v].city,zipCode:x[v].post_code,countryCode:"NO"}});case 116:e.sent,Q=x[v].poweroffice_customer_id,e.next=131;break;case 120:return console.log("else"),e.next=123,y.createCustomer({EmailAddress:x[v].email,FirstName:x[v].first_name,LastName:x[v].last_name,InvoiceDeliveryType:x[v].invoice_delivery_type,InvoiceEmailAddress:x[v].email,IsPerson:!1,IsArchived:!1,LegalName:x[v].company_name,Name:x[v].company_name,PhoneNumber:x[v].phone,VatNumber:x[v].organization_id,streetAddresses:x[v].customer_address,MailAddress:{Address1:x[v].address,City:x[v].city,ZipCode:x[v].post_code,CountryCode:"NO"}});case 123:if(!(te=e.sent).data.data){e.next=129;break}return e.next=127,y.createCustomerContactPerson(te.data.data.id,{FirstName:x[v].first_name,LastName:x[v].last_name,EmailAddress:x[v].email});case 127:return e.next=129,i.call("update",{TableName:process.env.userPermissionsTableName,Key:{permissionId:x[v].permission_id},UpdateExpression:"SET powerofficeCustomerId = :powerofficeCustomerId",ExpressionAttributeValues:{":powerofficeCustomerId":te.data.data.id},ReturnValues:"ALL_NEW"});case 129:console.log(te.data),Q=te.data.data.id;case 131:e.next=100;break;case 133:return e.next=135,y.getCustomer(Q);case 135:X=e.sent,se=X.data.streetAddresses,e.t6=a.default.keys(x);case 138:if((e.t7=e.t6()).done){e.next=161;break}re=e.t7.value,e.t8=a.default.keys(se);case 141:if((e.t9=e.t8()).done){e.next=159;break}ae=e.t9.value,e.t10=a.default.keys(A.Items);case 144:if((e.t11=e.t10()).done){e.next=157;break}if(k=e.t11.value,console.log("vat"+X.data.vatNumber),X.data.vatNumber!=x[re].organization_id){e.next=155;break}return e.next=150,i.call("scan",{TableName:process.env.userPermissionsTableName,FilterExpression:"userId = :userId and locationId = :locationId",ExpressionAttributeValues:{":userId":x[re].customer_company_id,":locationId":A.Items[k].objectId}});case 150:if(ne=e.sent,!(se[ae].address1==A.Items[k].objectAddress&&ne.Items.length>0)){e.next=155;break}return console.log(ne.Items[0].permissionId),e.next=155,i.call("update",{TableName:process.env.userPermissionsTableName,Key:{permissionId:ne.Items[0].permissionId},UpdateExpression:"SET powerofficeAddressId = :powerofficeAddressId",ExpressionAttributeValues:{":powerofficeAddressId":se[ae].id},ReturnValues:"ALL_NEW"});case 155:e.next=144;break;case 157:e.next=141;break;case 159:e.next=138;break;case 161:r(null,(0,o.success)(!0)),e.next=168;break;case 164:e.prev=164,e.t12=e.catch(0),console.log(e.t12),r(null,(0,o.failure)({error:e.t12}));case 168:case"end":return e.stop()}}),e,this,[[0,164]])}))),function(e,s,r){return t.apply(this,arguments)}),s(226)),i=(s(328),u(s(216)),u(s(493))),c=d(s(400));function u(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var s in e)Object.prototype.hasOwnProperty.call(e,s)&&(t[s]=e[s]);return t.default=e,t}function d(e){return e&&e.__esModule?e:{default:e}}var l=new(s(336).CognitoIdentityServiceProvider);function m(e,t){return e.filter((function(e,s,r){return r.map((function(e){return e[t]})).indexOf(e[t])===s}))}})();var a=exports;for(var n in r)a[n]=r[n];r.__esModule&&Object.defineProperty(a,"__esModule",{value:!0})})();