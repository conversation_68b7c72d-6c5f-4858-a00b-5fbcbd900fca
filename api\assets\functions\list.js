import * as dynamoDbLib from "../../../libs/dynamodb-lib";
import * as bugsnagClient from "../../../libs/bugsnag";

import {
  success,
  failure
} from "../../../libs/response-lib";

import {
  is_level_permitted
} from "../../../libs/permissions";

var arraySort = require('array-sort')

export async function assets(event, context, callback) {

  try {

    const access_scope = ['owner', 'manager', 'employee']
    var user_id = event.requestContext.identity.cognitoAuthenticationProvider.split('CognitoSignIn:')[1]

    const permissions = await is_level_permitted(user_id, access_scope);
    if (permissions.level_allowed) {


      const params = {
        TableName: process.env.objectsTableName,
        FilterExpression: "objectType = :objectType and objectCompanyId = :objectCompanyId",
        ExpressionAttributeValues: {
          ":objectType": 'asset',
          ":objectCompanyId": permissions.user_company_id
        }
      };

      let locations = await dynamoDbLib.call("scan", params);


      for (var x in locations.Items) {

        let products = await dynamoDbLib.call("scan", {
          TableName: process.env.objectsTableName,
          FilterExpression: "objectType = :objectType and objectLocation = :objectLocation",
          ExpressionAttributeValues: {
            ":objectType": 'product',
            ":objectLocation": locations.Items[x].objectId
          }
        });

        locations.Items[x].products = products.Items.length

      }


      callback(null, success(locations.Items));

    } else {

      callback(null, failure({
        status: 'you do not have access to this api call'
      }));

    }

  } catch (e) {
    bugsnagClient.notify(user_id, e)
    callback(null, failure({
      status: e
    }));
  }
}