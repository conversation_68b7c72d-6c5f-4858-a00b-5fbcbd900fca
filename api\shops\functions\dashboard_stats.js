import * as dynamoDbLib from "../../../libs/dynamodb-lib";
import * as bugsnagClient from "../../../libs/bugsnag";
import {
  success,
  failure
} from "../../../libs/response-lib";

import {
  is_level_permitted
} from "../../../libs/permissions";

var arraySort = require('array-sort')
var unique = require('array-unique');
const groupArray = require('group-array');

var moment = require("moment-timezone");

export async function list(event, context, callback) {


  try {
    const access_scope = ['owner', 'manager', 'employee']

    var user_id = event.requestContext.identity.cognitoAuthenticationProvider.split('CognitoSignIn:')[1]
    //var user_id = '2ecfa80b-b874-4b10-bd41-1bc3c0501acd'
    const data = JSON.parse(event.body);
    /*
    let data = {
      "shop_id": "9d9299c0-c191-11e9-a05c-e348f9a1aa15",
      "period": "week"
    }
    */
    const permissions = await is_level_permitted(user_id, access_scope);


    if (permissions.level_allowed) {

      let period = data.period
      let shop_id = data.shop_id


      const shop = await dynamoDbLib.call("scan", {
        TableName: process.env.objectsTableName,
        FilterExpression: "objectType = :objectType and objectCompanyId = :objectCompanyId and objectId = :objectId",
        ExpressionAttributeValues: {
          ":objectType": 'shop',
          ":objectCompanyId": permissions.user_company_id,
          ":objectId": shop_id
        }
      });

      if (shop.Items.length == 1) {

        let shop_currency = (shop.Items[0].shopCurrency) ? shop.Items[0].shopCurrency : 'NOK'

        let orderMode = data.orderMode
        let revenueMode = data.revenueMode
        let selectedYear = data.selectedYear

        let current_from = null
        let current_to = null
        let last_from = null
        let last_to = null

        if (period == 'week') {

          let current_week = getStartEndOfWeek()
          let last_week = getStartEndOfWeek(-1)

          current_from = current_week[0]
          current_to = current_week[1]
          last_from = last_week[0]
          last_to = last_week[1]
        }


        if (period == 'month') {

          let current_month = getStartEndOfMonth()
          let last_month = getStartEndOfMonth(-1)

          current_from = current_month[0]
          current_to = current_month[1]
          last_from = last_month[0]
          last_to = last_month[1]
        }
        var d = new Date();
        var current_year = d.getFullYear();

        let current_year = getStartEndOfYear(0, current_year)
        let last_year = getStartEndOfYear(-1, selectedYear)

        if (period == 'year') {
          current_from = current_year[0]
          current_to = current_year[1]
          last_from = last_year[0]
          last_to = last_year[1]
        }

        let current_year_from = current_year[0]
        let current_year_to = current_year[1]

        let opreator = (orderMode == 'delivery') ? 'delivery_date' : 'createdAt'

        const currentCartParsms = {
          TableName: process.env.cartTableName,
          FilterExpression: opreator + " BETWEEN :startOfMonth AND :endOfMonth and shopId = :shopId and orderStatus = :orderStatus and schedule_type = :schedule_type",
          ExpressionAttributeValues: {
            ":startOfMonth": current_from,
            ":endOfMonth": current_to,
            ":shopId": shop_id,
            ":orderStatus": 'pending',
            ":schedule_type": 'once'
          }
        };


        const lastCartParsms = {
          TableName: process.env.cartTableName,
          FilterExpression: opreator + " BETWEEN :startOfMonth AND :endOfMonth and shopId = :shopId and orderStatus = :orderStatus and schedule_type = :schedule_type",
          ExpressionAttributeValues: {
            ":startOfMonth": last_from,
            ":endOfMonth": last_to,
            ":shopId": shop_id,
            ":orderStatus": 'pending',
            ":schedule_type": 'once'
          }
        };

        const yearCartParsms = {
          TableName: process.env.cartTableName,
          FilterExpression: opreator + " BETWEEN :startOfMonth AND :endOfMonth and shopId = :shopId and orderStatus = :orderStatus and schedule_type = :schedule_type",
          ExpressionAttributeValues: {
            ":startOfMonth": current_year_from,
            ":endOfMonth": current_year_to,
            ":shopId": shop_id,
            ":orderStatus": 'pending',
            ":schedule_type": 'once'
          }
        };

        const current_orders = await dynamoDbLib.call("scan", currentCartParsms);
        const last_orders = await dynamoDbLib.call("scan", lastCartParsms);
        const year_orders = await dynamoDbLib.call("scan", yearCartParsms);



        let from = new Date(current_from).setHours(0, 0, 0)
        let to = new Date(current_to).setHours(23, 59, 59)


        var days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];

        var startDate = new Date(from);
        var endDate = new Date(to);


        let fields = new Object();
        fields['Product Name'] = 'product_name'

        var getDateArray = function(start, end) {
          var arr = new Array();
          var dt = new Date(start);
          while (dt <= end) {
            let date = dt.getFullYear() + '-' + pad((dt.getMonth() + 1), 2) + '-' + pad(dt.getDate(), 2)
            arr.push({
              index: dt.getDay(),
              name: days[dt.getDay()],
              date: date
            });
            fields[date] = date
            dt.setDate(dt.getDate() + 1);
          }
          return arr;
        }

        var dateArr = getDateArray(startDate, endDate);

        let current_total_revenue = 0
        let current_total_orders = current_orders.Items.length
        let current_total_products = 0
        let current_total_customers = 0
        let current_customers_list = []
        let current_revenu_list = []


        for (var x in dateArr) {
          current_revenu_list.push({
            time: dateArr[x].date,
            value: 0
          })
        }


        let year_revenue_list_confirmed = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
        let year_revenue_list_unconfirmed = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]

        for (var x in year_orders.Items) {

          let order_time = (orderMode == 'delivery') ? year_orders.Items[x].delivery_date + 10800000 : year_orders.Items[x].createdAt
          let time = new Date(order_time)
          let order_month = time.getMonth()

          //year_revenue_list[order_month] += parseInt(year_orders.Items[x].total)


          if (revenueMode == 'net' && year_orders.Items[x].mva) {
            if (order_time <= new Date().getTime()) {
              year_revenue_list_confirmed[order_month] += (parseInt(year_orders.Items[x].total) - parseInt(year_orders.Items[x].mva))
            } else {
              year_revenue_list_unconfirmed[order_month] += (parseInt(year_orders.Items[x].total) - parseInt(year_orders.Items[x].mva))
            }

          } else {
            if (order_time <= new Date().getTime()) {
              year_revenue_list_confirmed[order_month] += parseInt(year_orders.Items[x].total)
            } else {
              year_revenue_list_unconfirmed[order_month] += parseInt(year_orders.Items[x].total)
            }

          }


        }

        for (var x in current_orders.Items) {

          let order_time = (orderMode == 'delivery') ? current_orders.Items[x].delivery_date + 10800000 : current_orders.Items[x].createdAt

          if (revenueMode == 'net' && current_orders.Items[x].mva) {

            current_total_revenue += (parseInt(current_orders.Items[x].total) - parseInt(current_orders.Items[x].mva))

          } else {
            current_total_revenue += parseInt(current_orders.Items[x].total)
          }


          let dt = new Date(order_time)
          let date = dt.getFullYear() + '-' + pad((dt.getMonth() + 1), 2) + '-' + pad(dt.getDate(), 2)
          current_revenu_list.push({
            time: date,
            value: (revenueMode == 'net') ? (parseFloat(current_orders.Items[x].total) - parseFloat(current_orders.Items[x].mva)) : parseFloat(current_orders.Items[x].total)
          })

          current_customers_list.push(current_orders.Items[x].companyId)

          for (var y in current_orders.Items[x].items) {
            current_total_products += Number.isInteger(parseInt(current_orders.Items[x].items[y].quantity)) ? parseInt(current_orders.Items[x].items[y].quantity) : 0
          }

        }

        current_customers_list = unique(current_customers_list)
        current_total_customers = current_customers_list.length
        current_total_revenue = Math.round(parseFloat(current_total_revenue) * 100) / 100


        let last_total_revenue = 0
        let last_total_orders = last_orders.Items.length
        let last_total_products = 0
        let last_total_customers = 0
        let last_customers_list = []


        for (var x in last_orders.Items) {

          if (revenueMode == 'net' && last_orders.Items[x].mva) {
            last_total_revenue += (parseFloat(last_orders.Items[x].total) - parseFloat(last_orders.Items[x].mva))
          } else {
            last_total_revenue += parseFloat(last_orders.Items[x].total)
          }

          last_customers_list.push(last_orders.Items[x].companyId)

          for (var y in last_orders.Items[x].items) {
            last_total_products += Number.isInteger(parseInt(last_orders.Items[x].items[y].quantity)) ? parseInt(last_orders.Items[x].items[y].quantity) : 0
          }

        }

        last_customers_list = unique(last_customers_list)
        last_total_customers = last_customers_list.length
        last_total_revenue = last_total_revenue.toFixed(2)

        let revenue_percent = getPercentageChange(last_total_revenue, current_total_revenue).toFixed(0);
        let orders_percent = getPercentageChange(last_total_orders, current_total_orders).toFixed(0);
        let products_percent = getPercentageChange(last_total_products, current_total_products).toFixed(0);
        let customers_percent = getPercentageChange(last_total_customers, current_total_customers).toFixed(0);

        let current_revenu_list_temp = []
        current_revenu_list = groupArray(current_revenu_list, 'time')

        for (var x in current_revenu_list) {
          let total = 0
          for (var y in current_revenu_list[x]) {
            total += current_revenu_list[x][y].value
          }
          current_revenu_list_temp.push({
            time: new Date(x).getTime() + 10800000,
            value: total
          })
        }

        current_revenu_list_temp = arraySort(current_revenu_list_temp, 'time')

        callback(null, success({
          current_from,
          current_to,
          last_from,
          last_to,
          current_total_revenue,
          last_total_revenue,
          revenue_percent,
          current_total_orders,
          orders_percent,
          current_total_products,
          products_percent,
          current_total_customers,
          customers_percent,
          current_revenu_list: current_revenu_list_temp,
          year_revenue_list_confirmed,
          year_revenue_list_unconfirmed,
          shop_currency
        }));
      } else {

        callback(null, failure({
          status: 'you do not have access to this api call'
        }));

      }
    } else {

      callback(null, failure({
        status: 'you do not have access to this api call'
      }));

    }

  } catch (e) {
    bugsnagClient.notify(user_id, e)
    console.log(e);
    callback(null, failure({
      status: e
    }));
  }
}


function getStartEndOfYear(year, selectedYear) {

  let from_date = null
  let to_date = null

  var d = new Date();
  var current_year = d.getFullYear();
  var diff = current_year - parseInt(selectedYear)


  //  from_date = moment().subtract(1 + diff, 'years').startOf('year')
  //  to_date = moment().subtract(1 + diff, 'years').subtract(1, 'days').endOf('year')

  from_date = moment().subtract(diff, 'years').startOf('year');
  to_date = moment().subtract(diff, 'years').subtract(1, 'days').endOf('year');

  return [new Date(from_date).getTime(), new Date(to_date).getTime()]

}

function getStartEndOfMonth(month) {

  let date = new Date();
  if (month == -1) {
    date.setDate(date.getDate() - 30);
  }
  let first = new Date(date.getFullYear(), date.getMonth(), 1).setHours(0, 0, 1)
  let last = new Date(date.getFullYear(), date.getMonth() + 1, 0).setHours(23, 59, 59);
  return [first, last]

}

function getStartEndOfWeek(week = 0) {

  let from_date = null
  let to_date = null

  if (week == -1) {
    from_date = moment().subtract(1, 'weeks').startOf('isoWeek')
    from_date = moment(from_date).tz("Europe/Berlin")
    to_date = moment().subtract(1, 'weeks').endOf('isoWeek')
    to_date = moment(to_date).tz("Europe/Berlin")
  } else {
    from_date = moment().startOf('isoWeek');
    from_date = moment(from_date).tz("Europe/Berlin")
    to_date = moment().endOf('isoWeek');
    to_date = moment(to_date).tz("Europe/Berlin")
  }

  return [(new Date(from_date).getTime() - 10800000), (new Date(to_date).getTime()) - 10800000]

}

function getPercentageChange(newNumber, oldNumber) {
  var decreaseValue = oldNumber - newNumber;

  return (decreaseValue / oldNumber) * 100;
}

function pad(num, size) {
  var s = num + "";
  while (s.length < size) s = "0" + s;
  return s;
}