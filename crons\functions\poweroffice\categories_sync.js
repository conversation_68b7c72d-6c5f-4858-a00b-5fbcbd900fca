import {
  success,
  failure
} from "../../../libs/response-lib";

import {
  is_level_permitted
} from "../../../libs/permissions";
import * as bugsnagClient from "../../../libs/bugsnag";
import * as dynamoDbLib from "../../../libs/dynamodb-lib";

import PowerOffice from "../../../libs/poweroffice";

const AWS = require('aws-sdk')
const cognito_client = new AWS.CognitoIdentityServiceProvider()


export async function init(event, context, callback) {

  try {

    let category_id = event.Records[0].Sns.Message

    const categoryParams = {
      TableName: process.env.objectsTableName,
      FilterExpression: "objectId = :objectId",
      ExpressionAttributeValues: {
        ":objectId": category_id
      }
    };

    const categories = await dynamoDbLib.call("scan", categoryParams);
    const shop_id = categories.Items[0].objectShopId


    const shopsSarams = {
      TableName: process.env.objectsTableName,
      FilterExpression: "objectType = :objectType and objectId = :objectId",
      ExpressionAttributeValues: {
        ":objectType": 'shop',
        ":objectId": shop_id
      }
    };

    const shops = await dynamoDbLib.call("scan", shopsSarams);


    if (shops.Items[0].objectPowerOfficeIntegration) {

      let company_id = shops.Items[0].objectCompanyId
      let migration_mode = shops.Items[0].objectPowerOfficeMigrationMode


      const integrationParams = {
        TableName: process.env.integrationsTableName,
        FilterExpression: "integrationId = :integrationId",
        ExpressionAttributeValues: {
          ":integrationId": shops.Items[0].objectPowerOfficeIntegration
        }
      };

      const integrations = await dynamoDbLib.call("scan", integrationParams);
      let poweroffice_client_key = integrations.Items[0].integrationPOAppKey
      let poweroffice_secrect_key = integrations.Items[0].integrationPOClientKey
      let poweroffice_0_mva_account = integrations.Items[0].integrationPO0MVA
      let poweroffice_15_mva_account = integrations.Items[0].integrationPO15MVA
      let poweroffice_25_mva_account = integrations.Items[0].integrationPO25MVA
      let migration_start_date = integrations.Items[0].createdAt

      let poweroffice = await new PowerOffice(process.env.POWEROFFICE_APP_KEY, poweroffice_secrect_key)


      for (var x in categories.Items) {


        if (categories.Items[x].powerOfficeCategoryId) {


          await poweroffice.updateProductGroup({
            id: categories.Items[x].powerOfficeCategoryId,
            name: categories.Items[x].objectName + ' - Kompis app',
          })

        } else {

          let category = await poweroffice.createProductGroup({
            name: categories.Items[x].objectName + ' - Kompis app',
          })

          const result = await dynamoDbLib.call("update", {
            TableName: process.env.objectsTableName,

            Key: {
              objectCompanyId: company_id,
              objectId: categories.Items[x].objectId
            },

            UpdateExpression: "SET powerOfficeCategoryId = :powerOfficeCategoryId",
            ExpressionAttributeValues: {
              ":powerOfficeCategoryId": category.data.data.id,
            },
            ReturnValues: "ALL_NEW"
          });

        }
      }
    }


    callback(null, success(true))

  } catch (e) {
    //  bugsnagClient.notify(user_id, e)
    console.log(e);
    callback(null, failure({
      error: e
    }))
  }

}