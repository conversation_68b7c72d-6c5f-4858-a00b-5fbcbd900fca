import uuid from "uuid";
import * as dynamoDbLib from "../../../libs/dynamodb-lib";
import * as bugsnagClient from "../../../libs/bugsnag";

import {
  success,
  failure
} from "../../../libs/response-lib";

import {
  is_level_permitted
} from "../../../libs/permissions";

const AWS = require('aws-sdk')
const SNS = new AWS.SNS();

export async function product(event, context, callback) {


  try {
    const access_scope = ['owner', 'manager', 'employee']

    const data = JSON.parse(event.body);
    var user_id = event.requestContext.identity.cognitoAuthenticationProvider.split('CognitoSignIn:')[1]

    const permissions = await is_level_permitted(user_id, access_scope);
    if (permissions.level_allowed) {

      let product_id = uuid.v1()

      const params = {
        TableName: process.env.objectsTableName,
        Item: {
          objectCreatedBy: user_id,
          objectCompanyId: permissions.user_company_id,
          objectId: product_id,
          objectName: data.objectName,
          objectShopId: data.objectShopId,
          objectVisibility: true,
          objectPrice: data.objectPrice,
          objectCost: data.objectCost,
          objectMVA: data.objectMVA,
          objectMinimumOrderQuantity: data.objectMinimumOrderQuantity,
          objectMaximumOrderQuantity: data.objectMaximumOrderQuantity,
          objectMaximumDiscount: data.objectMaximumDiscount,
          objectDesc: data.objectDesc,
          objectLocation: data.objectLocation,
          objectImage: data.objectImage,
          objectCategory: data.objectCategory,
          objectType: "product",
          createdAt: new Date().getTime()
        }
      };

      await dynamoDbLib.call("put", params);

      await SNS.publish({
        Message: product_id,
        TopicArn: process.env.generatePowerOfficeProductsSNSTopic
      }, function(err, data) {

        if (err) {
          callback(null, err);
        }

      });


      callback(null, success(params.Item));

    } else {

      callback(null, failure({
        status: 'you do not have access to this api call'
      }));

    }

  } catch (e) {
    bugsnagClient.notify(user_id, e)
    callback(null, failure({
      status: e
    }));
  }

}