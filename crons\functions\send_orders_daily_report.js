import uuid from "uuid";
import * as dynamoDbLib from "../../libs/dynamodb-lib";
import * as bugsnagClient from "../../libs/bugsnag";
const AWS = require('aws-sdk')
const cognito_client = new AWS.CognitoIdentityServiceProvider()
import nodemailer from "nodemailer"
import {
  success,
  failure
} from "../../libs/response-lib";



export async function send(event, context, callback) {

  try {

    let current_day = new Date();
    let current_hour = current_day.getHours();

    let tomorrow_from = new Date();
    tomorrow_from.setDate(current_day.getDate() + 1);
    tomorrow_from.setHours(0, 0, 0, 0)

    let tomorrow_to = new Date();
    tomorrow_to.setDate(current_day.getDate() + 1);
    tomorrow_to.setHours(23, 59, 59, 0)


    const shopsParams = {
      TableName: process.env.objectsTableName,
      FilterExpression: "objectType = :objectType and orderDeadlineTime = :orderDeadlineTime and dailyOrderNotification = :dailyOrderNotification",
      ExpressionAttributeValues: {
        ":objectType": 'shop',
        ":orderDeadlineTime": current_hour + 2,
        ":dailyOrderNotification": true
      }
    };

    const shops = await dynamoDbLib.call("scan", shopsParams);

    for (var x in shops.Items) {


      let sendNotificationsTo = (shops.Items[x].sendNotificationsTo) ? shops.Items[x].sendNotificationsTo : []

      let ordersParams = {
        TableName: process.env.cartTableName,
        FilterExpression: "shopId = :shopId and orderStatus <> :orderStatus and delivery_date >= :date1 and delivery_date <= :date2",
        ExpressionAttributeValues: {
          ":shopId": shops.Items[x].objectId,
          ":orderStatus": 'canceled',
          ":date1": tomorrow_from.getTime(),
          ":date2": tomorrow_to.getTime()
        }
      };

      let orders = await dynamoDbLib.call("scan", ordersParams);

      let pool_id = process.env.COGNITO_POOL_ID
      let emails = []

      let params = {
        UserPoolId: pool_id,
        Filter: "name = \"" + shops.Items[x].objectCompanyId + "\"",
        AttributesToGet: null,
        Limit: 0
      }

      let users_data = await cognito_client.listUsers(params).promise()

      for (var y in users_data.Users) {

        let email = ''
        let user_type = ''
        let username = users_data.Users[y].Username


        for (var z in users_data.Users[y].Attributes) {

          if (users_data.Users[y].Attributes[z].Name == 'email') {
            email = users_data.Users[y].Attributes[z].Value
          }

          if (users_data.Users[y].Attributes[z].Name == 'custom:user_type') {
            user_type = users_data.Users[y].Attributes[z].Value
          }

        }

        if (sendNotificationsTo.includes(username)) {
          emails.push(email)
        }

      }

      let transporter = nodemailer.createTransport({
        host: 'email-smtp.eu-west-1.amazonaws.com',
        port: 465,
        secure: true,
        auth: {
          user: 'AKIAJLILB3DMXPRVD4AA',
          pass: 'Amg0o1jLDdfBLT++mU1G0xw99F+cCjVKFg4DYMlQOppz'
        }
      });

      let from_email = '<EMAIL>'
      let mailOptions = {}

      for (var k in emails) {

        console.log(emails[k]);
        if (orders.Items.length == 0) {
          mailOptions = {
            from: from_email,
            to: emails[k],
            subject: 'Din oppdatering om nye ordre',
            html: 'Hei <br /><br />Du har ingen nye ordre som skal lages til imorgen.<br />Ha en fin kveld'
          };
        } else {
          mailOptions = {
            from: from_email,
            to: emails[k],
            subject: 'Din oppdatering om nye ordre',
            html: 'Hei <br /><br />Gratulerer, du har ' + orders.Items.length + ' nye ordre fra ' + shops.Items[x].objectName + ' for leveranse imorgen.<br />For detaljer, <a href="https://kompis.app/auth/login">klikk her</a>.<br /><br />Mhv<br />Kompis'
          };
        }

        await transporter.sendMail(mailOptions).then(function(info) {
          callback(null, success(true));
        }).catch(function(err) {
          bugsnagClient.notify(user_id, err)
          callback(null, failure({
            status: err
          }));
        });

      }



    }


    callback(null, 'done');

  } catch (e) {
    bugsnagClient.notify(null, e)
    callback(null, e);
  }
}